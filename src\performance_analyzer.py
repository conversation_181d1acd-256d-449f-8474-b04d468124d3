#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل الأداء والتحسين - Performance Analyzer and Optimizer
يحلل نتائج الاختبارات ويحدد مشاكل الأداء ويقترح التحسينات
"""

import os
import sys
import json
import time
import gc
import threading
import tracemalloc
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging

# محاولة استيراد psutil، إذا لم يكن متوفراً استخدم بدائل
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("تحذير: psutil غير متوفر، سيتم استخدام بدائل محدودة")

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/performance_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """مقاييس الأداء"""
    cpu_usage: float
    memory_usage: float
    disk_io: Dict[str, float]
    network_io: Dict[str, float]
    execution_time: float
    memory_peak: float
    thread_count: int
    file_handles: int
    timestamp: str

@dataclass
class MemoryLeak:
    """تسريب الذاكرة"""
    function_name: str
    file_name: str
    line_number: int
    memory_increase: float
    occurrence_count: int
    severity: str  # low, medium, high, critical

@dataclass
class PerformanceIssue:
    """مشكلة في الأداء"""
    category: str  # cpu, memory, disk, network
    severity: str  # low, medium, high, critical
    description: str
    affected_component: str
    suggested_fix: str
    impact_score: float

class PerformanceAnalyzer:
    """محلل الأداء الشامل"""
    
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.memory_leaks: List[MemoryLeak] = []
        self.performance_issues: List[PerformanceIssue] = []
        self.baseline_metrics: Optional[PerformanceMetrics] = None
        self.monitoring_active = False
        self.monitor_thread = None
        
        # إنشاء مجلد السجلات
        os.makedirs('logs', exist_ok=True)
        
        # بدء مراقبة الذاكرة
        tracemalloc.start()
    
    def start_monitoring(self, interval: float = 1.0):
        """بدء مراقبة الأداء المستمرة"""
        if self.monitoring_active:
            logger.warning("المراقبة نشطة بالفعل")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("تم بدء مراقبة الأداء المستمرة")
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("تم إيقاف مراقبة الأداء")
    
    def _monitor_loop(self, interval: float):
        """حلقة المراقبة المستمرة"""
        while self.monitoring_active:
            try:
                metrics = self._collect_current_metrics()
                self.metrics_history.append(metrics)
                
                # الاحتفاظ بآخر 1000 قياس فقط
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                # فحص التسريبات والمشاكل
                self._detect_memory_leaks()
                self._detect_performance_issues()
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"خطأ في مراقبة الأداء: {e}")
                time.sleep(interval)
    
    def _collect_current_metrics(self) -> PerformanceMetrics:
        """جمع مقاييس الأداء الحالية"""
        if PSUTIL_AVAILABLE:
            process = psutil.Process()
            # معلومات المعالج
            cpu_usage = process.cpu_percent()
        else:
            cpu_usage = 0.0

        if PSUTIL_AVAILABLE:
            # معلومات الذاكرة
            memory_info = process.memory_info()
            memory_usage = memory_info.rss / 1024 / 1024  # MB

            # معلومات القرص
            try:
                disk_io = process.io_counters()
                disk_io_dict = {
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes,
                    'read_count': disk_io.read_count,
                    'write_count': disk_io.write_count
                }
            except:
                disk_io_dict = {'read_bytes': 0, 'write_bytes': 0, 'read_count': 0, 'write_count': 0}

            # معلومات الشبكة (تقريبية)
            network_io_dict = {
                'bytes_sent': 0,
                'bytes_recv': 0,
                'packets_sent': 0,
                'packets_recv': 0
            }

            # معلومات إضافية
            thread_count = process.num_threads()
            file_handles = process.num_fds() if hasattr(process, 'num_fds') else 0
        else:
            # بدائل بدون psutil
            memory_usage = 0.0
            disk_io_dict = {'read_bytes': 0, 'write_bytes': 0, 'read_count': 0, 'write_count': 0}
            network_io_dict = {'bytes_sent': 0, 'bytes_recv': 0, 'packets_sent': 0, 'packets_recv': 0}
            thread_count = threading.active_count()
            file_handles = 0
        
        # ذروة الذاكرة من tracemalloc
        try:
            current, peak = tracemalloc.get_traced_memory()
            memory_peak = peak / 1024 / 1024  # MB
            if not PSUTIL_AVAILABLE:
                memory_usage = current / 1024 / 1024  # استخدم tracemalloc كبديل
        except:
            memory_peak = 0.0
        
        return PerformanceMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            disk_io=disk_io_dict,
            network_io=network_io_dict,
            execution_time=0.0,  # سيتم حسابه لاحقاً
            memory_peak=memory_peak,
            thread_count=thread_count,
            file_handles=file_handles,
            timestamp=datetime.now().isoformat()
        )
    
    def _detect_memory_leaks(self):
        """اكتشاف تسريبات الذاكرة"""
        if len(self.metrics_history) < 10:
            return
        
        # فحص الاتجاه العام لاستخدام الذاكرة
        recent_metrics = self.metrics_history[-10:]
        memory_trend = [m.memory_usage for m in recent_metrics]
        
        # حساب معدل الزيادة
        if len(memory_trend) >= 2:
            memory_increase = memory_trend[-1] - memory_trend[0]
            if memory_increase > 50:  # زيادة أكثر من 50 MB
                # الحصول على معلومات التتبع
                snapshot = tracemalloc.take_snapshot()
                top_stats = snapshot.statistics('lineno')
                
                for stat in top_stats[:5]:  # أعلى 5 مصادر
                    leak = MemoryLeak(
                        function_name="unknown",
                        file_name=stat.traceback.format()[0] if stat.traceback.format() else "unknown",
                        line_number=0,
                        memory_increase=memory_increase,
                        occurrence_count=stat.count,
                        severity="medium" if memory_increase < 100 else "high"
                    )
                    
                    # تجنب التكرار
                    if not any(l.file_name == leak.file_name for l in self.memory_leaks):
                        self.memory_leaks.append(leak)
    
    def _detect_performance_issues(self):
        """اكتشاف مشاكل الأداء"""
        if len(self.metrics_history) < 5:
            return
        
        recent_metrics = self.metrics_history[-5:]
        
        # فحص استخدام المعالج العالي
        avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
        if avg_cpu > 80:
            issue = PerformanceIssue(
                category="cpu",
                severity="high",
                description=f"استخدام عالي للمعالج: {avg_cpu:.1f}%",
                affected_component="system",
                suggested_fix="تحسين الخوارزميات أو تقليل العمليات المتزامنة",
                impact_score=avg_cpu / 100
            )
            self._add_unique_issue(issue)
        
        # فحص استخدام الذاكرة العالي
        avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
        if avg_memory > 1000:  # أكثر من 1 GB
            issue = PerformanceIssue(
                category="memory",
                severity="high",
                description=f"استخدام عالي للذاكرة: {avg_memory:.1f} MB",
                affected_component="memory_management",
                suggested_fix="تحسين إدارة الذاكرة وتنظيف الكائنات غير المستخدمة",
                impact_score=min(avg_memory / 2000, 1.0)
            )
            self._add_unique_issue(issue)
        
        # فحص عدد الخيوط العالي
        avg_threads = sum(m.thread_count for m in recent_metrics) / len(recent_metrics)
        if avg_threads > 50:
            issue = PerformanceIssue(
                category="threading",
                severity="medium",
                description=f"عدد عالي من الخيوط: {avg_threads:.0f}",
                affected_component="thread_management",
                suggested_fix="تحسين إدارة الخيوط واستخدام thread pools",
                impact_score=min(avg_threads / 100, 1.0)
            )
            self._add_unique_issue(issue)
    
    def _add_unique_issue(self, issue: PerformanceIssue):
        """إضافة مشكلة فريدة (تجنب التكرار)"""
        existing = any(
            i.category == issue.category and i.description == issue.description
            for i in self.performance_issues
        )
        if not existing:
            self.performance_issues.append(issue)
    
    def analyze_test_results(self, test_results_path: str) -> Dict[str, Any]:
        """تحليل نتائج الاختبارات"""
        analysis_results = {
            "summary": {},
            "performance_issues": [],
            "memory_leaks": [],
            "recommendations": []
        }
        
        try:
            # قراءة نتائج الاختبارات إذا كانت متوفرة
            if os.path.exists(test_results_path):
                with open(test_results_path, 'r', encoding='utf-8') as f:
                    test_data = json.load(f)
                analysis_results["test_data"] = test_data
            
            # تحليل المقاييس المجمعة
            if self.metrics_history:
                analysis_results["summary"] = self._generate_summary()
            
            # إضافة المشاكل المكتشفة
            analysis_results["performance_issues"] = [
                asdict(issue) for issue in self.performance_issues
            ]
            analysis_results["memory_leaks"] = [
                asdict(leak) for leak in self.memory_leaks
            ]
            
            # توليد التوصيات
            analysis_results["recommendations"] = self._generate_recommendations()
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نتائج الاختبارات: {e}")
            analysis_results["error"] = str(e)
        
        return analysis_results
    
    def _generate_summary(self) -> Dict[str, Any]:
        """توليد ملخص الأداء"""
        if not self.metrics_history:
            return {}
        
        cpu_values = [m.cpu_usage for m in self.metrics_history]
        memory_values = [m.memory_usage for m in self.metrics_history]
        
        return {
            "total_measurements": len(self.metrics_history),
            "cpu_usage": {
                "average": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory_usage": {
                "average": sum(memory_values) / len(memory_values),
                "max": max(memory_values),
                "min": min(memory_values),
                "peak": max(m.memory_peak for m in self.metrics_history)
            },
            "issues_found": len(self.performance_issues),
            "memory_leaks_found": len(self.memory_leaks)
        }
    
    def _generate_recommendations(self) -> List[str]:
        """توليد توصيات التحسين"""
        recommendations = []
        
        # توصيات بناءً على المشاكل المكتشفة
        for issue in self.performance_issues:
            recommendations.append(f"[{issue.category.upper()}] {issue.suggested_fix}")
        
        # توصيات عامة
        if self.memory_leaks:
            recommendations.append("تنفيذ garbage collection دوري لتنظيف الذاكرة")
            recommendations.append("مراجعة إدارة الكائنات وإغلاق الموارد بشكل صحيح")
        
        if any(issue.category == "cpu" for issue in self.performance_issues):
            recommendations.append("استخدام multiprocessing للعمليات الثقيلة")
            recommendations.append("تحسين الخوارزميات وتقليل التعقيد الحسابي")
        
        return recommendations
    
    def save_analysis_report(self, output_path: str):
        """حفظ تقرير التحليل"""
        analysis = self.analyze_test_results("")
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        logger.info(f"تم حفظ تقرير التحليل في: {output_path}")

if __name__ == "__main__":
    # تشغيل تجريبي للمحلل
    analyzer = PerformanceAnalyzer()
    
    print("🔍 بدء تحليل الأداء...")
    analyzer.start_monitoring(interval=0.5)
    
    # محاكاة بعض العمليات
    time.sleep(5)
    
    analyzer.stop_monitoring()
    
    # حفظ التقرير
    analyzer.save_analysis_report("logs/performance_analysis_report.json")
    print("✅ تم إكمال تحليل الأداء وحفظ التقرير")
