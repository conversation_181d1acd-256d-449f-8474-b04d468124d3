#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التحقق من صحة البيانات - Data Validation System
يتحقق من صحة البيانات في جميع مراحل المعالجة
"""

import os
import re
import json
import logging
import hashlib
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import magic  # python-magic for file type detection

@dataclass
class ValidationResult:
    """نتيجة التحقق من صحة البيانات"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    corrected_data: Optional[Any] = None
    validation_time: datetime = None
    
    def __post_init__(self):
        if self.validation_time is None:
            self.validation_time = datetime.now()

@dataclass
class ValidationRule:
    """قاعدة التحقق"""
    name: str
    description: str
    validator_function: str
    parameters: Dict[str, Any]
    severity: str  # ERROR, WARNING, INFO
    auto_correct: bool = False
    correction_function: Optional[str] = None

class DataValidator:
    """نظام التحقق من صحة البيانات"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التحقق
        self.validation_settings = self._load_validation_settings()
        
        # قواعد التحقق
        self.validation_rules = self._load_validation_rules()
        
        # إحصائيات التحقق
        self.validation_stats = {
            "total_validations": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "auto_corrections": 0,
            "validation_errors": 0,
            "validation_warnings": 0,
            "last_validation": None
        }
        
        # ملفات البيانات
        self.data_dir = Path.home() / ".smart_content_app" / "validation"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.validation_log_file = self.data_dir / "validation_log.json"
        
        self.logger.info("تم تهيئة نظام التحقق من صحة البيانات")
    
    def _load_validation_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات التحقق"""
        try:
            return self.config_manager.get_setting("validation", "settings", {
                "enable_auto_correction": True,
                "strict_mode": False,
                "max_file_size_mb": 500,
                "allowed_video_formats": [".mp4", ".avi", ".mov", ".mkv", ".webm"],
                "allowed_audio_formats": [".mp3", ".wav", ".aac", ".ogg"],
                "allowed_image_formats": [".jpg", ".jpeg", ".png", ".gif", ".bmp"],
                "min_video_duration": 1,  # ثانية
                "max_video_duration": 600,  # 10 دقائق
                "min_video_resolution": [240, 240],  # عرض × ارتفاع
                "max_video_resolution": [4096, 4096],
                "required_metadata_fields": ["title", "description"],
                "text_encoding": "utf-8",
                "validate_urls": True,
                "validate_social_handles": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات التحقق: {str(e)}")
            return {}
    
    def _load_validation_rules(self) -> List[ValidationRule]:
        """تحميل قواعد التحقق"""
        try:
            rules = [
                ValidationRule(
                    name="file_exists",
                    description="التحقق من وجود الملف",
                    validator_function="validate_file_exists",
                    parameters={},
                    severity="ERROR"
                ),
                ValidationRule(
                    name="file_size",
                    description="التحقق من حجم الملف",
                    validator_function="validate_file_size",
                    parameters={"max_size_mb": self.validation_settings.get("max_file_size_mb", 500)},
                    severity="ERROR"
                ),
                ValidationRule(
                    name="file_format",
                    description="التحقق من تنسيق الملف",
                    validator_function="validate_file_format",
                    parameters={},
                    severity="ERROR"
                ),
                ValidationRule(
                    name="video_properties",
                    description="التحقق من خصائص الفيديو",
                    validator_function="validate_video_properties",
                    parameters={},
                    severity="WARNING"
                ),
                ValidationRule(
                    name="text_encoding",
                    description="التحقق من ترميز النص",
                    validator_function="validate_text_encoding",
                    parameters={"encoding": self.validation_settings.get("text_encoding", "utf-8")},
                    severity="WARNING",
                    auto_correct=True,
                    correction_function="correct_text_encoding"
                ),
                ValidationRule(
                    name="metadata_completeness",
                    description="التحقق من اكتمال البيانات الوصفية",
                    validator_function="validate_metadata_completeness",
                    parameters={"required_fields": self.validation_settings.get("required_metadata_fields", [])},
                    severity="WARNING",
                    auto_correct=True,
                    correction_function="correct_metadata_completeness"
                ),
                ValidationRule(
                    name="url_validity",
                    description="التحقق من صحة الروابط",
                    validator_function="validate_urls",
                    parameters={},
                    severity="WARNING"
                ),
                ValidationRule(
                    name="social_handles",
                    description="التحقق من صحة معرفات وسائل التواصل",
                    validator_function="validate_social_handles",
                    parameters={},
                    severity="WARNING",
                    auto_correct=True,
                    correction_function="correct_social_handles"
                )
            ]
            
            return rules
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قواعد التحقق: {str(e)}")
            return []
    
    def validate_file(self, file_path: str) -> ValidationResult:
        """التحقق من صحة ملف"""
        try:
            errors = []
            warnings = []
            corrected_data = {}
            
            file_path = Path(file_path)
            
            # التحقق من وجود الملف
            if not self.validate_file_exists(file_path):
                errors.append(f"الملف غير موجود: {file_path}")
                return ValidationResult(False, errors, warnings)
            
            # التحقق من حجم الملف
            if not self.validate_file_size(file_path):
                errors.append(f"حجم الملف كبير جداً: {file_path}")
            
            # التحقق من تنسيق الملف
            format_result = self.validate_file_format(file_path)
            if not format_result[0]:
                errors.append(format_result[1])
            
            # التحقق من خصائص الفيديو (إذا كان فيديو)
            if self._is_video_file(file_path):
                video_result = self.validate_video_properties(file_path)
                if not video_result[0]:
                    warnings.append(video_result[1])
            
            # تحديث الإحصائيات
            self.validation_stats["total_validations"] += 1
            if not errors:
                self.validation_stats["successful_validations"] += 1
            else:
                self.validation_stats["failed_validations"] += 1
            
            self.validation_stats["validation_errors"] += len(errors)
            self.validation_stats["validation_warnings"] += len(warnings)
            self.validation_stats["last_validation"] = datetime.now()
            
            # تسجيل النتيجة
            self._log_validation_result(str(file_path), errors, warnings)
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                corrected_data=corrected_data if corrected_data else None
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الملف: {str(e)}")
            return ValidationResult(False, [f"خطأ في التحقق: {str(e)}"], [])
    
    def validate_content_data(self, content_data: Dict[str, Any]) -> ValidationResult:
        """التحقق من صحة بيانات المحتوى"""
        try:
            errors = []
            warnings = []
            corrected_data = content_data.copy()
            
            # التحقق من اكتمال البيانات الوصفية
            metadata_result = self.validate_metadata_completeness(content_data)
            if not metadata_result[0]:
                warnings.append(metadata_result[1])
                if self.validation_settings.get("enable_auto_correction", True):
                    corrected_data = self.correct_metadata_completeness(corrected_data)
                    self.validation_stats["auto_corrections"] += 1
            
            # التحقق من ترميز النص
            text_result = self.validate_text_encoding(content_data)
            if not text_result[0]:
                warnings.append(text_result[1])
                if self.validation_settings.get("enable_auto_correction", True):
                    corrected_data = self.correct_text_encoding(corrected_data)
                    self.validation_stats["auto_corrections"] += 1
            
            # التحقق من الروابط
            if self.validation_settings.get("validate_urls", True):
                url_result = self.validate_urls(content_data)
                if not url_result[0]:
                    warnings.append(url_result[1])
            
            # التحقق من معرفات وسائل التواصل
            if self.validation_settings.get("validate_social_handles", True):
                social_result = self.validate_social_handles(content_data)
                if not social_result[0]:
                    warnings.append(social_result[1])
                    if self.validation_settings.get("enable_auto_correction", True):
                        corrected_data = self.correct_social_handles(corrected_data)
                        self.validation_stats["auto_corrections"] += 1
            
            # تحديث الإحصائيات
            self.validation_stats["total_validations"] += 1
            if not errors:
                self.validation_stats["successful_validations"] += 1
            else:
                self.validation_stats["failed_validations"] += 1
            
            self.validation_stats["validation_errors"] += len(errors)
            self.validation_stats["validation_warnings"] += len(warnings)
            self.validation_stats["last_validation"] = datetime.now()
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                corrected_data=corrected_data if corrected_data != content_data else None
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من بيانات المحتوى: {str(e)}")
            return ValidationResult(False, [f"خطأ في التحقق: {str(e)}"], [])
    
    def validate_file_exists(self, file_path: Path) -> bool:
        """التحقق من وجود الملف"""
        try:
            return file_path.exists() and file_path.is_file()
        except Exception:
            return False
    
    def validate_file_size(self, file_path: Path) -> bool:
        """التحقق من حجم الملف"""
        try:
            max_size_bytes = self.validation_settings.get("max_file_size_mb", 500) * 1024 * 1024
            return file_path.stat().st_size <= max_size_bytes
        except Exception:
            return False
    
    def validate_file_format(self, file_path: Path) -> Tuple[bool, str]:
        """التحقق من تنسيق الملف"""
        try:
            file_ext = file_path.suffix.lower()
            
            # قوائم التنسيقات المسموحة
            allowed_video = self.validation_settings.get("allowed_video_formats", [])
            allowed_audio = self.validation_settings.get("allowed_audio_formats", [])
            allowed_image = self.validation_settings.get("allowed_image_formats", [])
            
            all_allowed = allowed_video + allowed_audio + allowed_image
            
            if file_ext not in all_allowed:
                return False, f"تنسيق الملف غير مدعوم: {file_ext}"
            
            # التحقق من نوع الملف الفعلي باستخدام magic
            try:
                file_type = magic.from_file(str(file_path), mime=True)
                expected_types = {
                    '.mp4': 'video/mp4',
                    '.avi': 'video/x-msvideo',
                    '.mov': 'video/quicktime',
                    '.mkv': 'video/x-matroska',
                    '.webm': 'video/webm',
                    '.mp3': 'audio/mpeg',
                    '.wav': 'audio/wav',
                    '.aac': 'audio/aac',
                    '.ogg': 'audio/ogg',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.bmp': 'image/bmp'
                }
                
                expected_type = expected_types.get(file_ext)
                if expected_type and not file_type.startswith(expected_type.split('/')[0]):
                    return False, f"نوع الملف لا يطابق الامتداد: {file_type} vs {expected_type}"
                    
            except Exception:
                # إذا فشل magic، نعتمد على الامتداد فقط
                pass
            
            return True, ""
            
        except Exception as e:
            return False, f"خطأ في التحقق من تنسيق الملف: {str(e)}"

    def validate_video_properties(self, file_path: Path) -> Tuple[bool, str]:
        """التحقق من خصائص الفيديو"""
        try:
            import cv2

            cap = cv2.VideoCapture(str(file_path))
            if not cap.isOpened():
                return False, "لا يمكن فتح ملف الفيديو"

            # الحصول على خصائص الفيديو
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            cap.release()

            # حساب المدة
            duration = frame_count / fps if fps > 0 else 0

            # التحقق من المدة
            min_duration = self.validation_settings.get("min_video_duration", 1)
            max_duration = self.validation_settings.get("max_video_duration", 600)

            if duration < min_duration:
                return False, f"مدة الفيديو قصيرة جداً: {duration:.1f}s (الحد الأدنى: {min_duration}s)"

            if duration > max_duration:
                return False, f"مدة الفيديو طويلة جداً: {duration:.1f}s (الحد الأقصى: {max_duration}s)"

            # التحقق من الدقة
            min_res = self.validation_settings.get("min_video_resolution", [240, 240])
            max_res = self.validation_settings.get("max_video_resolution", [4096, 4096])

            if width < min_res[0] or height < min_res[1]:
                return False, f"دقة الفيديو منخفضة: {width}x{height} (الحد الأدنى: {min_res[0]}x{min_res[1]})"

            if width > max_res[0] or height > max_res[1]:
                return False, f"دقة الفيديو عالية جداً: {width}x{height} (الحد الأقصى: {max_res[0]}x{max_res[1]})"

            return True, ""

        except Exception as e:
            return False, f"خطأ في التحقق من خصائص الفيديو: {str(e)}"

    def validate_text_encoding(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """التحقق من ترميز النص"""
        try:
            expected_encoding = self.validation_settings.get("text_encoding", "utf-8")

            for key, value in data.items():
                if isinstance(value, str):
                    try:
                        # محاولة ترميز النص
                        value.encode(expected_encoding)
                    except UnicodeEncodeError:
                        return False, f"ترميز النص غير صحيح في الحقل: {key}"

            return True, ""

        except Exception as e:
            return False, f"خطأ في التحقق من ترميز النص: {str(e)}"

    def validate_metadata_completeness(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """التحقق من اكتمال البيانات الوصفية"""
        try:
            required_fields = self.validation_settings.get("required_metadata_fields", [])
            missing_fields = []

            for field in required_fields:
                if field not in data or not data[field] or str(data[field]).strip() == "":
                    missing_fields.append(field)

            if missing_fields:
                return False, f"حقول مطلوبة مفقودة: {', '.join(missing_fields)}"

            return True, ""

        except Exception as e:
            return False, f"خطأ في التحقق من اكتمال البيانات: {str(e)}"

    def validate_urls(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """التحقق من صحة الروابط"""
        try:
            import urllib.parse

            url_pattern = re.compile(
                r'^https?://'  # http:// or https://
                r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
                r'localhost|'  # localhost...
                r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
                r'(?::\d+)?'  # optional port
                r'(?:/?|[/?]\S+)$', re.IGNORECASE)

            invalid_urls = []

            def check_value(value, key_path=""):
                if isinstance(value, str) and ("http://" in value or "https://" in value):
                    # استخراج الروابط من النص
                    urls = re.findall(r'https?://[^\s<>"]+', value)
                    for url in urls:
                        if not url_pattern.match(url):
                            invalid_urls.append(f"{key_path}: {url}")
                elif isinstance(value, dict):
                    for k, v in value.items():
                        check_value(v, f"{key_path}.{k}" if key_path else k)
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        check_value(item, f"{key_path}[{i}]" if key_path else f"[{i}]")

            check_value(data)

            if invalid_urls:
                return False, f"روابط غير صحيحة: {', '.join(invalid_urls)}"

            return True, ""

        except Exception as e:
            return False, f"خطأ في التحقق من الروابط: {str(e)}"

    def validate_social_handles(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """التحقق من صحة معرفات وسائل التواصل"""
        try:
            social_patterns = {
                'twitter': r'^@?[A-Za-z0-9_]{1,15}$',
                'instagram': r'^@?[A-Za-z0-9_.]{1,30}$',
                'tiktok': r'^@?[A-Za-z0-9_.]{1,24}$',
                'snapchat': r'^@?[A-Za-z0-9._-]{3,15}$',
                'youtube': r'^@?[A-Za-z0-9_-]{1,20}$'
            }

            invalid_handles = []

            def check_social_handle(value, key_path=""):
                if isinstance(value, str):
                    for platform, pattern in social_patterns.items():
                        if platform in key_path.lower() or platform in value.lower():
                            if not re.match(pattern, value):
                                invalid_handles.append(f"{key_path} ({platform}): {value}")
                elif isinstance(value, dict):
                    for k, v in value.items():
                        check_social_handle(v, f"{key_path}.{k}" if key_path else k)
                elif isinstance(value, list):
                    for i, item in enumerate(value):
                        check_social_handle(item, f"{key_path}[{i}]" if key_path else f"[{i}]")

            check_social_handle(data)

            if invalid_handles:
                return False, f"معرفات وسائل تواصل غير صحيحة: {', '.join(invalid_handles)}"

            return True, ""

        except Exception as e:
            return False, f"خطأ في التحقق من معرفات وسائل التواصل: {str(e)}"

    def correct_text_encoding(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تصحيح ترميز النص"""
        try:
            corrected_data = data.copy()
            expected_encoding = self.validation_settings.get("text_encoding", "utf-8")

            def fix_encoding(value):
                if isinstance(value, str):
                    try:
                        # محاولة إصلاح الترميز
                        return value.encode('latin1').decode('utf-8')
                    except:
                        # إزالة الأحرف غير المدعومة
                        return value.encode(expected_encoding, errors='ignore').decode(expected_encoding)
                elif isinstance(value, dict):
                    return {k: fix_encoding(v) for k, v in value.items()}
                elif isinstance(value, list):
                    return [fix_encoding(item) for item in value]
                else:
                    return value

            corrected_data = fix_encoding(corrected_data)
            return corrected_data

        except Exception as e:
            self.logger.error(f"خطأ في تصحيح ترميز النص: {str(e)}")
            return data

    def correct_metadata_completeness(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تصحيح اكتمال البيانات الوصفية"""
        try:
            corrected_data = data.copy()
            required_fields = self.validation_settings.get("required_metadata_fields", [])

            for field in required_fields:
                if field not in corrected_data or not corrected_data[field] or str(corrected_data[field]).strip() == "":
                    # إضافة قيم افتراضية
                    default_values = {
                        "title": "عنوان افتراضي",
                        "description": "وصف افتراضي",
                        "tags": [],
                        "category": "عام",
                        "author": "مجهول"
                    }
                    corrected_data[field] = default_values.get(field, f"قيمة افتراضية لـ {field}")

            return corrected_data

        except Exception as e:
            self.logger.error(f"خطأ في تصحيح اكتمال البيانات: {str(e)}")
            return data

    def correct_social_handles(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تصحيح معرفات وسائل التواصل"""
        try:
            corrected_data = data.copy()

            def fix_social_handle(value, key_path=""):
                if isinstance(value, str):
                    # إزالة الرموز غير المرغوبة وتنسيق المعرف
                    cleaned = re.sub(r'[^\w@._-]', '', value)

                    # إضافة @ إذا لم تكن موجودة
                    if cleaned and not cleaned.startswith('@'):
                        cleaned = '@' + cleaned

                    return cleaned
                elif isinstance(value, dict):
                    return {k: fix_social_handle(v, f"{key_path}.{k}" if key_path else k) for k, v in value.items()}
                elif isinstance(value, list):
                    return [fix_social_handle(item, f"{key_path}[{i}]" if key_path else f"[{i}]") for i, item in enumerate(value)]
                else:
                    return value

            corrected_data = fix_social_handle(corrected_data)
            return corrected_data

        except Exception as e:
            self.logger.error(f"خطأ في تصحيح معرفات وسائل التواصل: {str(e)}")
            return data

    def _is_video_file(self, file_path: Path) -> bool:
        """التحقق من كون الملف فيديو"""
        try:
            video_extensions = self.validation_settings.get("allowed_video_formats", [])
            return file_path.suffix.lower() in video_extensions
        except Exception:
            return False

    def _log_validation_result(self, file_path: str, errors: List[str], warnings: List[str]):
        """تسجيل نتيجة التحقق"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "file_path": file_path,
                "errors": errors,
                "warnings": warnings,
                "is_valid": len(errors) == 0
            }

            # قراءة السجل الحالي
            log_data = []
            if self.validation_log_file.exists():
                try:
                    with open(self.validation_log_file, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                except:
                    log_data = []

            # إضافة الإدخال الجديد
            log_data.append(log_entry)

            # الاحتفاظ بآخر 1000 إدخال فقط
            if len(log_data) > 1000:
                log_data = log_data[-1000:]

            # حفظ السجل
            with open(self.validation_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل نتيجة التحقق: {str(e)}")

    def get_validation_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحقق"""
        try:
            stats = self.validation_stats.copy()

            # حساب معدل النجاح
            total = stats["total_validations"]
            if total > 0:
                stats["success_rate"] = (stats["successful_validations"] / total) * 100
                stats["failure_rate"] = (stats["failed_validations"] / total) * 100
            else:
                stats["success_rate"] = 0
                stats["failure_rate"] = 0

            # تحويل datetime إلى string
            if stats["last_validation"]:
                stats["last_validation"] = stats["last_validation"].isoformat()

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات التحقق: {str(e)}")
            return {}

    def get_validation_log(self, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على سجل التحقق"""
        try:
            if not self.validation_log_file.exists():
                return []

            with open(self.validation_log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # إرجاع آخر entries حسب الحد المطلوب
            return log_data[-limit:] if limit > 0 else log_data

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سجل التحقق: {str(e)}")
            return []

    def validate_batch(self, items: List[Union[str, Dict[str, Any]]]) -> List[ValidationResult]:
        """التحقق من مجموعة من العناصر"""
        try:
            results = []

            for item in items:
                if isinstance(item, str):
                    # التحقق من ملف
                    result = self.validate_file(item)
                elif isinstance(item, dict):
                    # التحقق من بيانات المحتوى
                    result = self.validate_content_data(item)
                else:
                    result = ValidationResult(
                        False,
                        [f"نوع عنصر غير مدعوم: {type(item)}"],
                        []
                    )

                results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"خطأ في التحقق المجمع: {str(e)}")
            return []

    def update_validation_settings(self, new_settings: Dict[str, Any]):
        """تحديث إعدادات التحقق"""
        try:
            self.validation_settings.update(new_settings)

            # حفظ في الإعدادات
            self.config_manager.set_setting("validation", "settings", self.validation_settings)

            # إعادة تحميل قواعد التحقق
            self.validation_rules = self._load_validation_rules()

            self.logger.info("تم تحديث إعدادات التحقق")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات التحقق: {str(e)}")

    def export_validation_report(self, file_path: str, days: int = 7):
        """تصدير تقرير التحقق"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # جمع البيانات
            log_data = self.get_validation_log(limit=-1)  # جميع البيانات

            # تصفية البيانات حسب التاريخ
            filtered_data = [
                entry for entry in log_data
                if datetime.fromisoformat(entry["timestamp"]) > cutoff_date
            ]

            # إنشاء التقرير
            report = {
                "report_generated": datetime.now().isoformat(),
                "period_days": days,
                "validation_stats": self.get_validation_stats(),
                "validation_settings": self.validation_settings,
                "validation_rules": [
                    {
                        "name": rule.name,
                        "description": rule.description,
                        "severity": rule.severity,
                        "auto_correct": rule.auto_correct
                    }
                    for rule in self.validation_rules
                ],
                "validation_log": filtered_data,
                "summary": {
                    "total_validations": len(filtered_data),
                    "successful_validations": len([e for e in filtered_data if e["is_valid"]]),
                    "failed_validations": len([e for e in filtered_data if not e["is_valid"]]),
                    "total_errors": sum(len(e["errors"]) for e in filtered_data),
                    "total_warnings": sum(len(e["warnings"]) for e in filtered_data)
                }
            }

            # حفظ التقرير
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير تقرير التحقق إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير تقرير التحقق: {str(e)}")

    def cleanup_validation_log(self, days: int = 30):
        """تنظيف سجل التحقق القديم"""
        try:
            if not self.validation_log_file.exists():
                return

            cutoff_date = datetime.now() - timedelta(days=days)

            # قراءة السجل الحالي
            with open(self.validation_log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # تصفية البيانات الحديثة
            filtered_data = [
                entry for entry in log_data
                if datetime.fromisoformat(entry["timestamp"]) > cutoff_date
            ]

            # حفظ البيانات المصفاة
            with open(self.validation_log_file, 'w', encoding='utf-8') as f:
                json.dump(filtered_data, f, ensure_ascii=False, indent=2)

            removed_count = len(log_data) - len(filtered_data)
            self.logger.info(f"تم تنظيف {removed_count} إدخال من سجل التحقق")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف سجل التحقق: {str(e)}")
