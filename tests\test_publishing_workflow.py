#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات سير عمل النشر - Publishing Workflow Tests
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.publishing.publishing_workflow import (
    PublishingWorkflow, PublishingJob, PublishingStatus
)
from src.core.config_manager import ConfigManager

class TestPublishingWorkflow:
    """اختبارات سير عمل النشر"""
    
    @pytest.fixture
    def config_manager(self):
        """إعداد مدير التكوين للاختبار"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)
            yield config_manager
    
    @pytest.fixture
    def workflow(self, config_manager):
        """إعداد سير العمل للاختبار"""
        return PublishingWorkflow(config_manager)
    
    @pytest.fixture
    def sample_content_analysis(self):
        """تحليل محتوى عينة للاختبار"""
        return {
            "emotions": {"joy": 0.8, "excitement": 0.7},
            "keywords": ["مضحك", "رائع", "مذهل"],
            "transcript": "هذا فيديو رائع ومضحك",
            "audio_features": {"energy": 0.8, "tempo": 120},
            "visual_features": {"brightness": 0.7, "motion": 0.6},
            "viral_score": 85.0,
            "quality_score": 90.0
        }
    
    @pytest.mark.asyncio
    async def test_submit_publishing_job(self, workflow, sample_content_analysis):
        """اختبار إرسال مهمة نشر"""
        video_path = "test_video.mp4"
        
        job_id = await workflow.submit_publishing_job(
            video_path=video_path,
            content_analysis=sample_content_analysis,
            platform="tiktok",
            publish_immediately=False
        )
        
        assert job_id is not None
        assert job_id in workflow.active_jobs
        
        job = workflow.active_jobs[job_id]
        assert job.video_path == video_path
        assert job.platform == "tiktok"
        assert job.publish_immediately == False
        assert job.status == PublishingStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_job_status_tracking(self, workflow, sample_content_analysis):
        """اختبار تتبع حالة المهمة"""
        job_id = await workflow.submit_publishing_job(
            video_path="test_video.mp4",
            content_analysis=sample_content_analysis
        )
        
        # التحقق من الحالة الأولية
        status = workflow.get_job_status(job_id)
        assert status is not None
        assert status["status"] == PublishingStatus.PENDING.value
        
        # تحديث الحالة
        job = workflow.active_jobs[job_id]
        job.status = PublishingStatus.OPTIMIZING
        job.updated_at = datetime.now()
        
        # التحقق من التحديث
        updated_status = workflow.get_job_status(job_id)
        assert updated_status["status"] == PublishingStatus.OPTIMIZING.value
    
    def test_cancel_job(self, workflow, sample_content_analysis):
        """اختبار إلغاء المهمة"""
        # إنشاء مهمة
        job_id = f"test_job_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        job = PublishingJob(
            job_id=job_id,
            video_path="test_video.mp4",
            content_analysis=sample_content_analysis,
            platform="tiktok",
            publish_immediately=False,
            custom_settings={},
            status=PublishingStatus.PENDING,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        workflow.active_jobs[job_id] = job
        
        # إلغاء المهمة
        result = workflow.cancel_job(job_id)
        assert result == True
        assert workflow.active_jobs[job_id].status == PublishingStatus.CANCELLED
    
    def test_cleanup_completed_jobs(self, workflow, sample_content_analysis):
        """اختبار تنظيف المهام المكتملة"""
        # إنشاء مهام قديمة
        old_time = datetime.now() - timedelta(hours=25)
        
        for i in range(3):
            job_id = f"old_job_{i}"
            job = PublishingJob(
                job_id=job_id,
                video_path=f"test_video_{i}.mp4",
                content_analysis=sample_content_analysis,
                platform="tiktok",
                publish_immediately=False,
                custom_settings={},
                status=PublishingStatus.COMPLETED,
                created_at=old_time,
                updated_at=old_time
            )
            workflow.active_jobs[job_id] = job
        
        # إنشاء مهمة حديثة
        recent_job_id = "recent_job"
        recent_job = PublishingJob(
            job_id=recent_job_id,
            video_path="recent_video.mp4",
            content_analysis=sample_content_analysis,
            platform="tiktok",
            publish_immediately=False,
            custom_settings={},
            status=PublishingStatus.COMPLETED,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        workflow.active_jobs[recent_job_id] = recent_job
        
        # تنظيف المهام القديمة
        workflow.cleanup_completed_jobs(older_than_hours=24)
        
        # التحقق من النتائج
        assert len(workflow.active_jobs) == 1
        assert recent_job_id in workflow.active_jobs
    
    def test_workflow_statistics(self, workflow):
        """اختبار إحصائيات سير العمل"""
        # تحديث الإحصائيات
        workflow.workflow_stats["total_jobs_processed"] = 10
        workflow.workflow_stats["successful_publications"] = 8
        workflow.workflow_stats["failed_publications"] = 2
        
        stats = workflow.get_workflow_statistics()
        
        assert stats["total_jobs_processed"] == 10
        assert stats["successful_publications"] == 8
        assert stats["failed_publications"] == 2
        assert stats["success_rate"] == 80.0
    
    def test_update_workflow_settings(self, workflow):
        """اختبار تحديث إعدادات سير العمل"""
        new_settings = {
            "max_concurrent_jobs": 5,
            "auto_retry_failed": False,
            "quality_threshold": 80.0
        }
        
        workflow.update_workflow_settings(new_settings)
        
        assert workflow.workflow_settings["max_concurrent_jobs"] == 5
        assert workflow.workflow_settings["auto_retry_failed"] == False
        assert workflow.workflow_settings["quality_threshold"] == 80.0
    
    @pytest.mark.asyncio
    async def test_batch_publish(self, workflow, sample_content_analysis):
        """اختبار النشر المجمع"""
        video_paths = ["video1.mp4", "video2.mp4", "video3.mp4"]
        content_analyses = [sample_content_analysis] * 3
        
        with patch.object(workflow, 'submit_publishing_job', new_callable=AsyncMock) as mock_submit:
            mock_submit.side_effect = ["job1", "job2", "job3"]
            
            job_ids = await workflow.batch_publish(
                video_paths=video_paths,
                content_analyses=content_analyses,
                platform="tiktok",
                publish_immediately=False
            )
            
            assert len(job_ids) == 3
            assert mock_submit.call_count == 3
    
    def test_export_workflow_data(self, workflow):
        """اختبار تصدير بيانات سير العمل"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as temp_file:
            temp_path = temp_file.name
        
        try:
            workflow.export_workflow_data(temp_path)
            
            # التحقق من وجود الملف
            assert os.path.exists(temp_path)
            
            # قراءة البيانات المصدرة
            import json
            with open(temp_path, 'r', encoding='utf-8') as f:
                exported_data = json.load(f)
            
            assert "workflow_stats" in exported_data
            assert "workflow_settings" in exported_data
            assert "active_jobs" in exported_data
            assert "export_date" in exported_data
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, workflow, sample_content_analysis):
        """اختبار معالجة الأخطاء"""
        job_id = await workflow.submit_publishing_job(
            video_path="test_video.mp4",
            content_analysis=sample_content_analysis
        )
        
        # محاكاة خطأ
        error_message = "خطأ في المعالجة"
        await workflow._handle_job_error(job_id, error_message)
        
        job = workflow.active_jobs[job_id]
        assert job.status == PublishingStatus.FAILED
        assert job.error_message == error_message
        assert job.retry_count == 1
    
    def test_job_to_dict(self, sample_content_analysis):
        """اختبار تحويل المهمة إلى قاموس"""
        job = PublishingJob(
            job_id="test_job",
            video_path="test_video.mp4",
            content_analysis=sample_content_analysis,
            platform="tiktok",
            publish_immediately=False,
            custom_settings={},
            status=PublishingStatus.PENDING,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        job_dict = job.to_dict()
        
        assert job_dict["job_id"] == "test_job"
        assert job_dict["video_path"] == "test_video.mp4"
        assert job_dict["platform"] == "tiktok"
        assert job_dict["status"] == PublishingStatus.PENDING.value
        assert "created_at" in job_dict
        assert "updated_at" in job_dict

if __name__ == "__main__":
    pytest.main([__file__])
