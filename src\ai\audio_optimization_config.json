{
  "audio_processing": {
    "sample_rate": 16000,
    "chunk_duration": 1.0,
    "overlap_duration": 0.25,
    "normalize_audio": True,
    "remove_silence": True
  },
  "feature_extraction": {
    "mfcc_features": 13,
    "spectral_features": True,
    "temporal_features": True,
    "cache_features": True
  },
  "speech_recognition": {
    "model_size": "base",
    "language": "ar",
    "use_gpu": True,
    "batch_processing": True
  },
  "emotion_detection": {
    "model_type": "lightweight",
    "confidence_threshold": 0.6,
    "cache_results": True
  }
}
