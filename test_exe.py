#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الملف التنفيذي
"""

import os
import subprocess
import sys
from pathlib import Path

def test_exe():
    """اختبار الملف التنفيذي"""
    
    print("🔍 البحث عن الملفات التنفيذية...")
    
    # البحث في مجلد dist
    dist_path = Path("dist")
    if not dist_path.exists():
        print("❌ مجلد dist غير موجود")
        return
    
    # البحث عن الملفات التنفيذية
    exe_files = list(dist_path.glob("*.exe"))
    
    print(f"📁 محتويات مجلد dist:")
    for item in dist_path.iterdir():
        if item.is_file():
            size = item.stat().st_size / (1024 * 1024)  # MB
            print(f"  📄 {item.name} ({size:.1f} MB)")
        else:
            print(f"  📁 {item.name}/")
    
    if exe_files:
        print(f"\n✅ تم العثور على {len(exe_files)} ملف تنفيذي:")
        for exe_file in exe_files:
            size = exe_file.stat().st_size / (1024 * 1024)  # MB
            print(f"  🎯 {exe_file.name} ({size:.1f} MB)")
            
            # اختبار تشغيل الملف
            print(f"\n🧪 اختبار تشغيل {exe_file.name}...")
            try:
                # تشغيل الملف مع timeout
                result = subprocess.run(
                    [str(exe_file), "--version"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    cwd=dist_path
                )
                
                if result.returncode == 0:
                    print(f"✅ {exe_file.name} يعمل بشكل صحيح")
                    if result.stdout:
                        print(f"📤 الإخراج: {result.stdout.strip()}")
                else:
                    print(f"⚠️ {exe_file.name} انتهى برمز خطأ: {result.returncode}")
                    if result.stderr:
                        print(f"❌ الخطأ: {result.stderr.strip()}")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ انتهت مهلة اختبار {exe_file.name}")
            except Exception as e:
                print(f"❌ خطأ في اختبار {exe_file.name}: {e}")
    else:
        print("❌ لم يتم العثور على أي ملفات تنفيذية")
    
    # البحث في المجلدات الفرعية
    for subdir in dist_path.iterdir():
        if subdir.is_dir():
            sub_exe_files = list(subdir.glob("*.exe"))
            if sub_exe_files:
                print(f"\n📁 ملفات تنفيذية في {subdir.name}:")
                for exe_file in sub_exe_files:
                    size = exe_file.stat().st_size / (1024 * 1024)  # MB
                    print(f"  🎯 {exe_file.name} ({size:.1f} MB)")

def check_build_logs():
    """فحص سجلات البناء"""
    print("\n📋 فحص سجلات البناء...")
    
    # فحص مجلد build
    build_path = Path("build")
    if build_path.exists():
        print("✅ مجلد build موجود")
        
        # البحث عن ملفات التحذير
        warn_files = list(build_path.rglob("warn-*.txt"))
        if warn_files:
            print(f"⚠️ تم العثور على {len(warn_files)} ملف تحذير")
            for warn_file in warn_files[-1:]:  # آخر ملف فقط
                print(f"\n📄 محتوى {warn_file.name}:")
                try:
                    with open(warn_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        # عرض آخر 10 أسطر
                        for line in lines[-10:]:
                            if line.strip():
                                print(f"  {line.strip()}")
                except Exception as e:
                    print(f"❌ خطأ في قراءة {warn_file}: {e}")
    else:
        print("❌ مجلد build غير موجود")

if __name__ == "__main__":
    print("🔧 اختبار الملفات التنفيذية")
    print("=" * 50)
    
    test_exe()
    check_build_logs()
    
    print("\n✅ انتهى الاختبار")
