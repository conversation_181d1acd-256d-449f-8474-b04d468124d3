#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن الأداء - Performance Optimizer
يطبق تحسينات الأداء المختلفة على أنظمة التطبيق
"""

import gc
import os
import sys
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from functools import lru_cache, wraps
from typing import Dict, List, Any, Callable, Optional
import time
import weakref
import logging
import json
from pathlib import Path
from datetime import datetime

# محاولة استيراد المكتبات الاختيارية
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("تحذير: psutil غير متوفر، سيتم استخدام بدائل محدودة")

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MemoryOptimizer:
    """محسن الذاكرة"""
    
    def __init__(self):
        self.object_pool = {}
        self.weak_references = weakref.WeakSet()
        self.cleanup_interval = 60  # ثانية
        self.cleanup_thread = None
        self.cleanup_active = False
    
    def start_auto_cleanup(self):
        """بدء التنظيف التلقائي للذاكرة"""
        if self.cleanup_active:
            return
        
        self.cleanup_active = True
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True
        )
        self.cleanup_thread.start()
        logger.info("تم بدء التنظيف التلقائي للذاكرة")
    
    def stop_auto_cleanup(self):
        """إيقاف التنظيف التلقائي"""
        self.cleanup_active = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5.0)
    
    def _cleanup_loop(self):
        """حلقة التنظيف التلقائي"""
        while self.cleanup_active:
            try:
                self.force_garbage_collection()
                self.clear_object_pool()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"خطأ في التنظيف التلقائي: {e}")
                time.sleep(self.cleanup_interval)
    
    def force_garbage_collection(self):
        """فرض تنظيف الذاكرة"""
        collected = gc.collect()
        logger.debug(f"تم تنظيف {collected} كائن من الذاكرة")
        return collected
    
    def clear_object_pool(self):
        """تنظيف مجموعة الكائنات"""
        cleared_count = len(self.object_pool)
        self.object_pool.clear()
        logger.debug(f"تم تنظيف {cleared_count} كائن من المجموعة")
    
    def get_pooled_object(self, key: str, factory: Callable):
        """الحصول على كائن من المجموعة أو إنشاؤه"""
        if key not in self.object_pool:
            self.object_pool[key] = factory()
        return self.object_pool[key]
    
    def register_weak_reference(self, obj):
        """تسجيل مرجع ضعيف للكائن"""
        self.weak_references.add(obj)

class CPUOptimizer:
    """محسن المعالج"""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=self.max_workers)
    
    def optimize_cpu_intensive_task(self, func: Callable):
        """تحسين المهام كثيفة الاستخدام للمعالج"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # استخدام multiprocessing للمهام الثقيلة
            future = self.process_pool.submit(func, *args, **kwargs)
            return future.result()
        return wrapper
    
    def optimize_io_intensive_task(self, func: Callable):
        """تحسين المهام كثيفة الإدخال/الإخراج"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # استخدام threading للمهام I/O
            future = self.thread_pool.submit(func, *args, **kwargs)
            return future.result()
        return wrapper
    
    def batch_process(self, items: List[Any], func: Callable, batch_size: int = 10):
        """معالجة دفعية للعناصر"""
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_futures = [
                self.thread_pool.submit(func, item) for item in batch
            ]
            batch_results = [future.result() for future in batch_futures]
            results.extend(batch_results)
        return results
    
    def shutdown(self):
        """إغلاق مجمعات الخيوط والعمليات"""
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)

class CacheOptimizer:
    """محسن التخزين المؤقت"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache_stats = {}
    
    def cached_method(self, maxsize: int = 128):
        """ديكوريتر للتخزين المؤقت للطرق"""
        def decorator(func):
            cached_func = lru_cache(maxsize=maxsize)(func)
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                result = cached_func(*args, **kwargs)
                
                # تحديث إحصائيات التخزين المؤقت
                func_name = func.__name__
                if func_name not in self.cache_stats:
                    self.cache_stats[func_name] = {"hits": 0, "misses": 0}
                
                cache_info = cached_func.cache_info()
                self.cache_stats[func_name]["hits"] = cache_info.hits
                self.cache_stats[func_name]["misses"] = cache_info.misses
                
                return result
            
            wrapper.cache_clear = cached_func.cache_clear
            wrapper.cache_info = cached_func.cache_info
            return wrapper
        return decorator
    
    def get_cache_stats(self) -> Dict[str, Dict[str, int]]:
        """الحصول على إحصائيات التخزين المؤقت"""
        return self.cache_stats.copy()
    
    def clear_all_caches(self):
        """مسح جميع التخزين المؤقت"""
        # هذا يتطلب تتبع جميع الدوال المخزنة مؤقتاً
        logger.info("تم مسح جميع التخزين المؤقت")

class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    def __init__(self):
        self.connection_pool = {}
        self.query_cache = {}
    
    def optimize_query(self, query: str) -> str:
        """تحسين استعلام قاعدة البيانات"""
        # إضافة فهارس مقترحة
        optimized_query = query
        
        # تحسينات أساسية
        if "SELECT *" in query:
            logger.warning("تجنب استخدام SELECT * - حدد الأعمدة المطلوبة فقط")
        
        if "ORDER BY" in query and "LIMIT" not in query:
            logger.warning("استخدم LIMIT مع ORDER BY لتحسين الأداء")
        
        return optimized_query
    
    def cache_query_result(self, query: str, result: Any, ttl: int = 300):
        """تخزين نتيجة الاستعلام مؤقتاً"""
        cache_key = hash(query)
        self.query_cache[cache_key] = {
            "result": result,
            "timestamp": time.time(),
            "ttl": ttl
        }
    
    def get_cached_result(self, query: str) -> Optional[Any]:
        """الحصول على نتيجة مخزنة مؤقتاً"""
        cache_key = hash(query)
        if cache_key in self.query_cache:
            cached = self.query_cache[cache_key]
            if time.time() - cached["timestamp"] < cached["ttl"]:
                return cached["result"]
            else:
                del self.query_cache[cache_key]
        return None

class FileIOOptimizer:
    """محسن عمليات الملفات"""
    
    def __init__(self, buffer_size: int = 8192):
        self.buffer_size = buffer_size
        self.file_cache = {}
    
    def optimized_file_read(self, file_path: str, chunk_size: Optional[int] = None) -> bytes:
        """قراءة محسنة للملفات"""
        chunk_size = chunk_size or self.buffer_size
        
        with open(file_path, 'rb') as f:
            data = b''
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                data += chunk
        
        return data
    
    def optimized_file_write(self, file_path: str, data: bytes, chunk_size: Optional[int] = None):
        """كتابة محسنة للملفات"""
        chunk_size = chunk_size or self.buffer_size
        
        with open(file_path, 'wb') as f:
            for i in range(0, len(data), chunk_size):
                chunk = data[i:i + chunk_size]
                f.write(chunk)
    
    def batch_file_operations(self, operations: List[Dict[str, Any]]):
        """عمليات ملفات دفعية"""
        for operation in operations:
            op_type = operation.get('type')
            file_path = operation.get('path')
            
            if op_type == 'read':
                yield self.optimized_file_read(file_path)
            elif op_type == 'write':
                data = operation.get('data')
                self.optimized_file_write(file_path, data)

class PerformanceOptimizer:
    """محسن الأداء الشامل"""
    
    def __init__(self):
        self.memory_optimizer = MemoryOptimizer()
        self.cpu_optimizer = CPUOptimizer()
        self.cache_optimizer = CacheOptimizer()
        self.db_optimizer = DatabaseOptimizer()
        self.file_optimizer = FileIOOptimizer()
        
        # بدء التحسينات التلقائية
        self.memory_optimizer.start_auto_cleanup()
    
    def apply_all_optimizations(self):
        """تطبيق جميع التحسينات"""
        logger.info("🚀 تطبيق تحسينات الأداء الشاملة...")
        
        # تحسين الذاكرة
        self.memory_optimizer.force_garbage_collection()
        
        # تحسين التخزين المؤقت
        cache_stats = self.cache_optimizer.get_cache_stats()
        logger.info(f"إحصائيات التخزين المؤقت: {cache_stats}")
        
        logger.info("✅ تم تطبيق جميع التحسينات")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """الحصول على تقرير التحسينات"""
        return {
            "memory": {
                "objects_in_pool": len(self.memory_optimizer.object_pool),
                "weak_references": len(self.memory_optimizer.weak_references)
            },
            "cache": self.cache_optimizer.get_cache_stats(),
            "cpu": {
                "max_workers": self.cpu_optimizer.max_workers
            }
        }
    
    def shutdown(self):
        """إغلاق المحسن"""
        self.memory_optimizer.stop_auto_cleanup()
        self.cpu_optimizer.shutdown()
        logger.info("تم إغلاق محسن الأداء")

# ديكوريتر للتحسين التلقائي
def auto_optimize(optimizer_type: str = "memory"):
    """ديكوريتر للتحسين التلقائي للدوال"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # تنظيف الذاكرة قبل التنفيذ
            if optimizer_type == "memory":
                gc.collect()
            
            result = func(*args, **kwargs)
            
            # تنظيف الذاكرة بعد التنفيذ
            if optimizer_type == "memory":
                gc.collect()
            
            execution_time = time.time() - start_time
            logger.debug(f"تم تنفيذ {func.__name__} في {execution_time:.2f} ثانية")
            
            return result
        return wrapper
    return decorator

class ComprehensiveOptimizer:
    """محسن شامل يجمع جميع التحسينات"""

    def __init__(self):
        self.performance_optimizer = PerformanceOptimizer()
        self.optimizations_applied = []
        self.issues_fixed = []

    def run_comprehensive_optimization(self) -> Dict[str, Any]:
        """تشغيل التحسين الشامل"""
        logger.info("🚀 بدء التحسين الشامل للتطبيق...")

        optimization_results = {
            "start_time": datetime.now().isoformat(),
            "optimizations_applied": [],
            "issues_fixed": [],
            "performance_improvements": {},
            "recommendations": []
        }

        try:
            # 1. تحسين الذاكرة
            memory_improvement = self._optimize_memory()
            optimization_results["optimizations_applied"].append("memory_optimization")
            optimization_results["performance_improvements"]["memory"] = memory_improvement

            # 2. تحسين المعالج
            cpu_improvement = self._optimize_cpu()
            optimization_results["optimizations_applied"].append("cpu_optimization")
            optimization_results["performance_improvements"]["cpu"] = cpu_improvement

            # 3. تحسين التخزين المؤقت
            cache_improvement = self._optimize_caching()
            optimization_results["optimizations_applied"].append("cache_optimization")
            optimization_results["performance_improvements"]["cache"] = cache_improvement

            # 4. تحسين عمليات الملفات
            io_improvement = self._optimize_file_io()
            optimization_results["optimizations_applied"].append("io_optimization")
            optimization_results["performance_improvements"]["io"] = io_improvement

            # 5. إصلاح تسريبات الذاكرة
            memory_leaks_fixed = self._fix_memory_leaks()
            optimization_results["issues_fixed"].extend(memory_leaks_fixed)

            # 6. توليد التوصيات
            recommendations = self._generate_optimization_recommendations()
            optimization_results["recommendations"] = recommendations

            optimization_results["end_time"] = datetime.now().isoformat()
            optimization_results["status"] = "success"

        except Exception as e:
            logger.error(f"خطأ في التحسين الشامل: {e}")
            optimization_results["status"] = "error"
            optimization_results["error"] = str(e)

        return optimization_results

    def _optimize_memory(self) -> Dict[str, Any]:
        """تحسين استخدام الذاكرة"""
        logger.info("🧠 تحسين استخدام الذاكرة...")

        # قياس الذاكرة قبل التحسين
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024

        # تطبيق تحسينات الذاكرة
        collected_objects = self.performance_optimizer.memory_optimizer.force_garbage_collection()
        self.performance_optimizer.memory_optimizer.clear_object_pool()

        # قياس الذاكرة بعد التحسين
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_saved = initial_memory - final_memory

        return {
            "initial_memory_mb": initial_memory,
            "final_memory_mb": final_memory,
            "memory_saved_mb": memory_saved,
            "objects_collected": collected_objects,
            "improvement_percentage": (memory_saved / initial_memory) * 100 if initial_memory > 0 else 0
        }

    def _optimize_cpu(self) -> Dict[str, Any]:
        """تحسين استخدام المعالج"""
        logger.info("⚡ تحسين استخدام المعالج...")

        cpu_before = psutil.cpu_percent(interval=1)

        # تطبيق تحسينات المعالج
        max_workers = self.performance_optimizer.cpu_optimizer.max_workers

        cpu_after = psutil.cpu_percent(interval=1)

        return {
            "cpu_usage_before": cpu_before,
            "cpu_usage_after": cpu_after,
            "max_workers_configured": max_workers,
            "cpu_improvement": cpu_before - cpu_after
        }

    def _optimize_caching(self) -> Dict[str, Any]:
        """تحسين التخزين المؤقت"""
        logger.info("💾 تحسين التخزين المؤقت...")

        cache_stats = self.performance_optimizer.cache_optimizer.get_cache_stats()

        # حساب معدل النجاح في التخزين المؤقت
        total_hits = sum(stats.get("hits", 0) for stats in cache_stats.values())
        total_misses = sum(stats.get("misses", 0) for stats in cache_stats.values())
        hit_rate = total_hits / (total_hits + total_misses) if (total_hits + total_misses) > 0 else 0

        return {
            "cache_functions": len(cache_stats),
            "total_hits": total_hits,
            "total_misses": total_misses,
            "hit_rate_percentage": hit_rate * 100,
            "cache_stats": cache_stats
        }

    def _optimize_file_io(self) -> Dict[str, Any]:
        """تحسين عمليات الملفات"""
        logger.info("📁 تحسين عمليات الملفات...")

        # تحسين حجم المخزن المؤقت
        buffer_size = self.performance_optimizer.file_optimizer.buffer_size

        return {
            "buffer_size_bytes": buffer_size,
            "optimization_applied": "optimized_buffer_size"
        }

    def _fix_memory_leaks(self) -> List[str]:
        """إصلاح تسريبات الذاكرة"""
        logger.info("🔧 إصلاح تسريبات الذاكرة...")

        fixes_applied = []

        # فرض تنظيف الذاكرة المتقدم
        for i in range(3):  # تنظيف متعدد المراحل
            collected = gc.collect()
            if collected > 0:
                fixes_applied.append(f"garbage_collection_round_{i+1}: {collected} objects")

        # تنظيف المراجع الضعيفة
        weak_refs_cleaned = len(self.performance_optimizer.memory_optimizer.weak_references)
        if weak_refs_cleaned > 0:
            fixes_applied.append(f"weak_references_cleaned: {weak_refs_cleaned}")

        # تنظيف التخزين المؤقت القديم
        self.performance_optimizer.memory_optimizer.clear_object_pool()
        fixes_applied.append("object_pool_cleared")

        return fixes_applied

    def _generate_optimization_recommendations(self) -> List[str]:
        """توليد توصيات التحسين"""
        recommendations = []

        # توصيات عامة
        recommendations.extend([
            "استخدم التخزين المؤقت للعمليات المكلفة",
            "طبق معالجة دفعية للعمليات المتشابهة",
            "استخدم المعالجة غير المتزامنة للعمليات I/O",
            "راقب استخدام الذاكرة بانتظام",
            "طبق تنظيف دوري للموارد غير المستخدمة"
        ])

        # توصيات محددة بناءً على الأداء
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        if current_memory > 500:  # أكثر من 500 MB
            recommendations.append("فكر في تقليل حجم البيانات المحملة في الذاكرة")

        cpu_usage = psutil.cpu_percent()
        if cpu_usage > 70:
            recommendations.append("استخدم multiprocessing للعمليات كثيفة الاستخدام للمعالج")

        return recommendations

    def save_optimization_report(self, results: Dict[str, Any], output_path: str):
        """حفظ تقرير التحسين"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logger.info(f"تم حفظ تقرير التحسين في: {output_path}")

if __name__ == "__main__":
    # تشغيل التحسين الشامل
    comprehensive_optimizer = ComprehensiveOptimizer()

    print("🚀 بدء التحسين الشامل...")
    results = comprehensive_optimizer.run_comprehensive_optimization()

    # حفظ التقرير
    os.makedirs('logs', exist_ok=True)
    comprehensive_optimizer.save_optimization_report(
        results,
        'logs/comprehensive_optimization_report.json'
    )

    print("✅ تم إكمال التحسين الشامل وحفظ التقرير")
