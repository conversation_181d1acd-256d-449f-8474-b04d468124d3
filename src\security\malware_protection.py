#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحماية من البرمجيات الخبيثة - Malware Protection System
نظام شامل للحماية من البرمجيات الخبيثة والتهديدات الأمنية
"""

import os
import sys
import time
import hashlib
import threading
import subprocess
import psutil
import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import tempfile
import shutil

class ThreatLevel(Enum):
    """مستوى التهديد"""
    LOW = "low"           # منخفض
    MEDIUM = "medium"     # متوسط
    HIGH = "high"         # عالي
    CRITICAL = "critical" # حرج

class ThreatType(Enum):
    """نوع التهديد"""
    MALWARE = "malware"           # برمجية خبيثة
    VIRUS = "virus"               # فيروس
    TROJAN = "trojan"             # حصان طروادة
    SPYWARE = "spyware"           # برمجية تجسس
    ADWARE = "adware"             # برمجية إعلانية
    RANSOMWARE = "ransomware"     # برمجية فدية
    ROOTKIT = "rootkit"           # روت كيت
    SUSPICIOUS_PROCESS = "suspicious_process"  # عملية مشبوهة
    SUSPICIOUS_FILE = "suspicious_file"        # ملف مشبوه
    NETWORK_THREAT = "network_threat"          # تهديد شبكة

class ScanStatus(Enum):
    """حالة الفحص"""
    IDLE = "idle"             # خامل
    SCANNING = "scanning"     # يفحص
    COMPLETED = "completed"   # مكتمل
    STOPPED = "stopped"       # متوقف
    ERROR = "error"           # خطأ

@dataclass
class ThreatDetection:
    """كشف التهديد"""
    detection_id: str
    threat_type: ThreatType
    threat_level: ThreatLevel
    file_path: str
    process_name: str
    description: str
    detected_at: datetime
    hash_value: str
    file_size: int
    quarantined: bool = False
    resolved: bool = False
    false_positive: bool = False

@dataclass
class ScanResult:
    """نتيجة الفحص"""
    scan_id: str
    scan_type: str
    start_time: datetime
    end_time: Optional[datetime]
    status: ScanStatus
    scanned_files: int
    threats_found: int
    threats_quarantined: int
    scan_path: str
    duration_seconds: float = 0.0

@dataclass
class ProcessInfo:
    """معلومات العملية"""
    pid: int
    name: str
    exe_path: str
    cmdline: List[str]
    cpu_percent: float
    memory_percent: float
    create_time: datetime
    connections: List[Dict[str, Any]]
    suspicious_score: int = 0

class MalwareProtectionSystem:
    """نظام الحماية من البرمجيات الخبيثة"""
    
    def __init__(self, base_dir: str = "data"):
        """تهيئة نظام الحماية من البرمجيات الخبيثة"""
        self.base_dir = Path(base_dir)
        self.protection_dir = self.base_dir / "malware_protection"
        self.quarantine_dir = self.protection_dir / "quarantine"
        self.logs_dir = self.protection_dir / "logs"
        self.signatures_dir = self.protection_dir / "signatures"
        self.whitelist_dir = self.protection_dir / "whitelist"
        
        # إنشاء المجلدات
        for directory in [self.protection_dir, self.quarantine_dir, 
                         self.logs_dir, self.signatures_dir, self.whitelist_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # ملفات النظام
        self.detections_file = self.protection_dir / "threat_detections.json"
        self.scan_results_file = self.protection_dir / "scan_results.json"
        self.config_file = self.protection_dir / "protection_config.json"
        self.whitelist_file = self.whitelist_dir / "whitelist.json"
        
        # قواعد البيانات
        self.threat_detections: List[ThreatDetection] = []
        self.scan_results: List[ScanResult] = []
        self.whitelisted_files: Set[str] = set()
        self.whitelisted_processes: Set[str] = set()
        
        # حالة النظام
        self.is_real_time_protection_enabled = True
        self.is_scanning = False
        self.current_scan: Optional[ScanResult] = None
        self.monitoring_threads: List[threading.Thread] = []
        self.stop_monitoring = threading.Event()
        
        # إعدادات النظام
        self.config = {
            "real_time_protection": True,
            "auto_quarantine": True,
            "scan_archives": True,
            "scan_email": False,
            "scan_network_drives": False,
            "max_file_size_mb": 100,
            "scan_timeout_seconds": 300,
            "quarantine_retention_days": 30,
            "log_retention_days": 90,
            "cpu_usage_limit": 50,
            "memory_usage_limit": 80,
            "suspicious_process_threshold": 70,
            "network_monitoring": True,
            "file_monitoring": True,
            "process_monitoring": True
        }
        
        # قواعد الكشف
        self.malware_signatures = self._load_malware_signatures()
        self.suspicious_patterns = self._load_suspicious_patterns()
        self.known_malware_hashes = self._load_known_malware_hashes()
        
        # إعداد نظام السجلات
        self.logger = logging.getLogger("MalwareProtection")
        self.logger.setLevel(logging.INFO)
        
        # إعداد معالج السجلات
        log_file = self.logs_dir / "malware_protection.log"
        handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            # تحميل الإعدادات
            self._load_config()
            
            # تحميل قواعد البيانات
            self._load_threat_detections()
            self._load_scan_results()
            self._load_whitelist()
            
            # بدء المراقبة في الوقت الفعلي
            if self.config["real_time_protection"]:
                self.start_real_time_protection()
            
            self.logger.info("تم تهيئة نظام الحماية من البرمجيات الخبيثة بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة النظام: {str(e)}")
    
    def _load_config(self):
        """تحميل الإعدادات"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def _save_config(self):
        """حفظ الإعدادات"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def _load_malware_signatures(self) -> List[Dict[str, Any]]:
        """تحميل توقيعات البرمجيات الخبيثة"""
        signatures = []
        try:
            # توقيعات أساسية للبرمجيات الخبيثة الشائعة
            basic_signatures = [
                {
                    "name": "Generic Trojan Pattern",
                    "pattern": rb"\x4d\x5a.*\x50\x45\x00\x00.*CreateRemoteThread",
                    "type": ThreatType.TROJAN.value,
                    "level": ThreatLevel.HIGH.value
                },
                {
                    "name": "Suspicious Registry Access",
                    "pattern": rb"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                    "type": ThreatType.MALWARE.value,
                    "level": ThreatLevel.MEDIUM.value
                },
                {
                    "name": "Keylogger Pattern",
                    "pattern": rb"GetAsyncKeyState|SetWindowsHookEx",
                    "type": ThreatType.SPYWARE.value,
                    "level": ThreatLevel.HIGH.value
                },
                {
                    "name": "Ransomware Extension",
                    "pattern": rb"\.encrypted|\.locked|\.crypto",
                    "type": ThreatType.RANSOMWARE.value,
                    "level": ThreatLevel.CRITICAL.value
                }
            ]
            signatures.extend(basic_signatures)
            
            # تحميل توقيعات إضافية من ملف
            signatures_file = self.signatures_dir / "malware_signatures.json"
            if signatures_file.exists():
                with open(signatures_file, 'r', encoding='utf-8') as f:
                    additional_signatures = json.load(f)
                    signatures.extend(additional_signatures)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل توقيعات البرمجيات الخبيثة: {str(e)}")
        
        return signatures
    
    def _load_suspicious_patterns(self) -> List[str]:
        """تحميل الأنماط المشبوهة"""
        patterns = [
            # أسماء ملفات مشبوهة
            r".*\.exe\.exe$",
            r".*\.(scr|pif|com|bat|cmd)$",
            r"^(svchost|winlogon|explorer)\.exe$",
            
            # مسارات مشبوهة
            r".*\\Temp\\.*\.exe$",
            r".*\\AppData\\Roaming\\.*\.exe$",
            r".*\\Users\\Public\\.*\.exe$",
            
            # أنماط شبكة مشبوهة
            r".*\.(tk|ml|ga|cf)$",  # نطاقات مجانية مشبوهة
            r".*\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}.*",  # عناوين IP مباشرة
        ]
        
        try:
            patterns_file = self.signatures_dir / "suspicious_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    additional_patterns = json.load(f)
                    patterns.extend(additional_patterns)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الأنماط المشبوهة: {str(e)}")
        
        return patterns
    
    def _load_known_malware_hashes(self) -> Set[str]:
        """تحميل هاشات البرمجيات الخبيثة المعروفة"""
        hashes = set()
        try:
            hashes_file = self.signatures_dir / "malware_hashes.json"
            if hashes_file.exists():
                with open(hashes_file, 'r', encoding='utf-8') as f:
                    hash_list = json.load(f)
                    hashes.update(hash_list)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل هاشات البرمجيات الخبيثة: {str(e)}")
        
        return hashes

    def scan_file(self, file_path: str) -> Tuple[bool, Optional[ThreatDetection]]:
        """فحص ملف واحد"""
        try:
            file_path = Path(file_path)

            # التحقق من وجود الملف
            if not file_path.exists() or not file_path.is_file():
                return False, None

            # التحقق من القائمة البيضاء
            if str(file_path) in self.whitelisted_files:
                return False, None

            # التحقق من حجم الملف
            file_size = file_path.stat().st_size
            max_size = self.config["max_file_size_mb"] * 1024 * 1024
            if file_size > max_size:
                self.logger.warning(f"تجاهل ملف كبير الحجم: {file_path}")
                return False, None

            # حساب الهاش
            file_hash = self._calculate_file_hash(file_path)

            # فحص الهاشات المعروفة
            if file_hash in self.known_malware_hashes:
                detection = ThreatDetection(
                    detection_id=self._generate_detection_id(),
                    threat_type=ThreatType.MALWARE,
                    threat_level=ThreatLevel.CRITICAL,
                    file_path=str(file_path),
                    process_name="",
                    description=f"ملف خبيث معروف: {file_path.name}",
                    detected_at=datetime.now(),
                    hash_value=file_hash,
                    file_size=file_size
                )
                return True, detection

            # فحص التوقيعات
            threat_found = self._scan_file_signatures(file_path)
            if threat_found:
                return True, threat_found

            # فحص الأنماط المشبوهة
            if self._is_file_suspicious(file_path):
                detection = ThreatDetection(
                    detection_id=self._generate_detection_id(),
                    threat_type=ThreatType.SUSPICIOUS_FILE,
                    threat_level=ThreatLevel.MEDIUM,
                    file_path=str(file_path),
                    process_name="",
                    description=f"ملف مشبوه: {file_path.name}",
                    detected_at=datetime.now(),
                    hash_value=file_hash,
                    file_size=file_size
                )
                return True, detection

            return False, None

        except Exception as e:
            self.logger.error(f"خطأ في فحص الملف {file_path}: {str(e)}")
            return False, None

    def _calculate_file_hash(self, file_path: Path) -> str:
        """حساب هاش الملف"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.logger.error(f"خطأ في حساب هاش الملف {file_path}: {str(e)}")
            return ""

    def _scan_file_signatures(self, file_path: Path) -> Optional[ThreatDetection]:
        """فحص توقيعات الملف"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()

            for signature in self.malware_signatures:
                pattern = signature.get("pattern", "")
                if isinstance(pattern, str):
                    pattern = pattern.encode()

                if re.search(pattern, file_content, re.IGNORECASE):
                    detection = ThreatDetection(
                        detection_id=self._generate_detection_id(),
                        threat_type=ThreatType(signature["type"]),
                        threat_level=ThreatLevel(signature["level"]),
                        file_path=str(file_path),
                        process_name="",
                        description=f"كشف توقيع: {signature['name']}",
                        detected_at=datetime.now(),
                        hash_value=self._calculate_file_hash(file_path),
                        file_size=file_path.stat().st_size
                    )
                    return detection

            return None

        except Exception as e:
            self.logger.error(f"خطأ في فحص توقيعات الملف {file_path}: {str(e)}")
            return None

    def _is_file_suspicious(self, file_path: Path) -> bool:
        """فحص إذا كان الملف مشبوهاً"""
        try:
            file_name = file_path.name.lower()
            file_path_str = str(file_path).lower()

            for pattern in self.suspicious_patterns:
                if re.search(pattern, file_name) or re.search(pattern, file_path_str):
                    return True

            # فحص امتدادات مشبوهة
            suspicious_extensions = ['.scr', '.pif', '.com', '.bat', '.cmd', '.vbs', '.js']
            if file_path.suffix.lower() in suspicious_extensions:
                return True

            # فحص ملفات مخفية في مجلدات النظام
            system_paths = ['windows', 'system32', 'syswow64']
            if any(path in file_path_str for path in system_paths) and file_path.name.startswith('.'):
                return True

            return False

        except Exception as e:
            self.logger.error(f"خطأ في فحص الملف المشبوه {file_path}: {str(e)}")
            return False

    def scan_directory(self, directory_path: str, recursive: bool = True) -> ScanResult:
        """فحص مجلد"""
        try:
            scan_id = self._generate_scan_id()
            start_time = datetime.now()

            scan_result = ScanResult(
                scan_id=scan_id,
                scan_type="directory",
                start_time=start_time,
                end_time=None,
                status=ScanStatus.SCANNING,
                scanned_files=0,
                threats_found=0,
                threats_quarantined=0,
                scan_path=directory_path
            )

            self.current_scan = scan_result
            self.is_scanning = True

            directory = Path(directory_path)
            if not directory.exists() or not directory.is_dir():
                scan_result.status = ScanStatus.ERROR
                return scan_result

            # فحص الملفات
            pattern = "**/*" if recursive else "*"
            for file_path in directory.glob(pattern):
                if not file_path.is_file():
                    continue

                scan_result.scanned_files += 1

                # فحص الملف
                is_threat, detection = self.scan_file(str(file_path))

                if is_threat and detection:
                    scan_result.threats_found += 1
                    self.threat_detections.append(detection)

                    # الحجر الصحي التلقائي
                    if self.config["auto_quarantine"]:
                        if self.quarantine_file(detection.file_path):
                            detection.quarantined = True
                            scan_result.threats_quarantined += 1

                    self.logger.warning(f"تم كشف تهديد: {detection.description}")

                # فحص التوقف
                if self.stop_monitoring.is_set():
                    scan_result.status = ScanStatus.STOPPED
                    break

            # إنهاء الفحص
            end_time = datetime.now()
            scan_result.end_time = end_time
            scan_result.duration_seconds = (end_time - start_time).total_seconds()

            if scan_result.status == ScanStatus.SCANNING:
                scan_result.status = ScanStatus.COMPLETED

            self.scan_results.append(scan_result)
            self.current_scan = None
            self.is_scanning = False

            # حفظ النتائج
            self._save_threat_detections()
            self._save_scan_results()

            self.logger.info(f"اكتمل فحص المجلد: {scan_result.scanned_files} ملف، {scan_result.threats_found} تهديد")

            return scan_result

        except Exception as e:
            self.logger.error(f"خطأ في فحص المجلد {directory_path}: {str(e)}")
            scan_result.status = ScanStatus.ERROR
            return scan_result

    def quick_scan(self) -> ScanResult:
        """فحص سريع للمجلدات المهمة"""
        try:
            scan_id = self._generate_scan_id()
            start_time = datetime.now()

            scan_result = ScanResult(
                scan_id=scan_id,
                scan_type="quick",
                start_time=start_time,
                end_time=None,
                status=ScanStatus.SCANNING,
                scanned_files=0,
                threats_found=0,
                threats_quarantined=0,
                scan_path="Quick Scan"
            )

            self.current_scan = scan_result
            self.is_scanning = True

            # مجلدات الفحص السريع
            quick_scan_paths = [
                os.path.expanduser("~\\Desktop"),
                os.path.expanduser("~\\Downloads"),
                os.path.expanduser("~\\Documents"),
                os.path.expanduser("~\\AppData\\Roaming"),
                "C:\\Windows\\Temp",
                "C:\\Temp"
            ]

            for scan_path in quick_scan_paths:
                if os.path.exists(scan_path):
                    # فحص المجلد (غير تكراري للفحص السريع)
                    dir_result = self.scan_directory(scan_path, recursive=False)
                    scan_result.scanned_files += dir_result.scanned_files
                    scan_result.threats_found += dir_result.threats_found
                    scan_result.threats_quarantined += dir_result.threats_quarantined

                # فحص التوقف
                if self.stop_monitoring.is_set():
                    scan_result.status = ScanStatus.STOPPED
                    break

            # إنهاء الفحص
            end_time = datetime.now()
            scan_result.end_time = end_time
            scan_result.duration_seconds = (end_time - start_time).total_seconds()

            if scan_result.status == ScanStatus.SCANNING:
                scan_result.status = ScanStatus.COMPLETED

            self.scan_results.append(scan_result)
            self.current_scan = None
            self.is_scanning = False

            self._save_scan_results()

            self.logger.info(f"اكتمل الفحص السريع: {scan_result.scanned_files} ملف، {scan_result.threats_found} تهديد")

            return scan_result

        except Exception as e:
            self.logger.error(f"خطأ في الفحص السريع: {str(e)}")
            scan_result.status = ScanStatus.ERROR
            return scan_result

    def start_real_time_protection(self):
        """بدء الحماية في الوقت الفعلي"""
        try:
            if not self.is_real_time_protection_enabled:
                return

            self.stop_monitoring.clear()

            # بدء مراقبة العمليات
            if self.config["process_monitoring"]:
                process_thread = threading.Thread(
                    target=self._monitor_processes,
                    daemon=True,
                    name="ProcessMonitor"
                )
                process_thread.start()
                self.monitoring_threads.append(process_thread)

            # بدء مراقبة الملفات
            if self.config["file_monitoring"]:
                file_thread = threading.Thread(
                    target=self._monitor_files,
                    daemon=True,
                    name="FileMonitor"
                )
                file_thread.start()
                self.monitoring_threads.append(file_thread)

            # بدء مراقبة الشبكة
            if self.config["network_monitoring"]:
                network_thread = threading.Thread(
                    target=self._monitor_network,
                    daemon=True,
                    name="NetworkMonitor"
                )
                network_thread.start()
                self.monitoring_threads.append(network_thread)

            self.logger.info("تم بدء الحماية في الوقت الفعلي")

        except Exception as e:
            self.logger.error(f"خطأ في بدء الحماية في الوقت الفعلي: {str(e)}")

    def stop_real_time_protection(self):
        """إيقاف الحماية في الوقت الفعلي"""
        try:
            self.stop_monitoring.set()
            self.is_real_time_protection_enabled = False

            # انتظار إنهاء الخيوط
            for thread in self.monitoring_threads:
                if thread.is_alive():
                    thread.join(timeout=5)

            self.monitoring_threads.clear()
            self.logger.info("تم إيقاف الحماية في الوقت الفعلي")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف الحماية في الوقت الفعلي: {str(e)}")

    def _monitor_processes(self):
        """مراقبة العمليات"""
        try:
            while not self.stop_monitoring.is_set():
                try:
                    # فحص العمليات الجارية
                    for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                        try:
                            proc_info = proc.info

                            # تجاهل العمليات في القائمة البيضاء
                            if proc_info['name'] in self.whitelisted_processes:
                                continue

                            # فحص العملية
                            if self._is_process_suspicious(proc):
                                self._handle_suspicious_process(proc)

                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    # انتظار قبل الفحص التالي
                    time.sleep(5)

                except Exception as e:
                    self.logger.error(f"خطأ في مراقبة العمليات: {str(e)}")
                    time.sleep(10)

        except Exception as e:
            self.logger.error(f"خطأ في خيط مراقبة العمليات: {str(e)}")

    def _is_process_suspicious(self, proc: psutil.Process) -> bool:
        """فحص إذا كانت العملية مشبوهة"""
        try:
            # الحصول على معلومات العملية
            proc_info = proc.as_dict(['pid', 'name', 'exe', 'cmdline', 'cpu_percent', 'memory_percent'])

            suspicious_score = 0

            # فحص اسم العملية
            proc_name = proc_info.get('name', '').lower()
            suspicious_names = ['svchost.exe', 'winlogon.exe', 'explorer.exe']
            if proc_name in suspicious_names and proc_info.get('exe'):
                # فحص إذا كانت العملية في مكان غير صحيح
                exe_path = proc_info['exe'].lower()
                if 'system32' not in exe_path and 'syswow64' not in exe_path:
                    suspicious_score += 30

            # فحص استخدام المعالج والذاكرة
            cpu_percent = proc_info.get('cpu_percent', 0)
            memory_percent = proc_info.get('memory_percent', 0)

            if cpu_percent > self.config["cpu_usage_limit"]:
                suspicious_score += 20

            if memory_percent > self.config["memory_usage_limit"]:
                suspicious_score += 20

            # فحص سطر الأوامر
            cmdline = proc_info.get('cmdline', [])
            if cmdline:
                cmdline_str = ' '.join(cmdline).lower()
                suspicious_keywords = ['powershell', 'cmd', 'wscript', 'cscript', 'regsvr32']
                for keyword in suspicious_keywords:
                    if keyword in cmdline_str:
                        suspicious_score += 15

            # فحص مسار التنفيذ
            exe_path = proc_info.get('exe', '').lower()
            suspicious_paths = ['temp', 'appdata', 'users\\public']
            for path in suspicious_paths:
                if path in exe_path:
                    suspicious_score += 25

            return suspicious_score >= self.config["suspicious_process_threshold"]

        except Exception as e:
            self.logger.error(f"خطأ في فحص العملية المشبوهة: {str(e)}")
            return False

    def _handle_suspicious_process(self, proc: psutil.Process):
        """التعامل مع العملية المشبوهة"""
        try:
            proc_info = proc.as_dict(['pid', 'name', 'exe', 'cmdline'])

            detection = ThreatDetection(
                detection_id=self._generate_detection_id(),
                threat_type=ThreatType.SUSPICIOUS_PROCESS,
                threat_level=ThreatLevel.HIGH,
                file_path=proc_info.get('exe', ''),
                process_name=proc_info.get('name', ''),
                description=f"عملية مشبوهة: {proc_info.get('name', '')} (PID: {proc_info.get('pid', '')})",
                detected_at=datetime.now(),
                hash_value="",
                file_size=0
            )

            self.threat_detections.append(detection)
            self._save_threat_detections()

            self.logger.warning(f"تم كشف عملية مشبوهة: {detection.description}")

            # إيقاف العملية إذا كانت خطيرة جداً
            if detection.threat_level == ThreatLevel.CRITICAL:
                try:
                    proc.terminate()
                    self.logger.info(f"تم إيقاف العملية المشبوهة: {proc_info.get('name', '')}")
                except Exception as e:
                    self.logger.error(f"فشل في إيقاف العملية: {str(e)}")

        except Exception as e:
            self.logger.error(f"خطأ في التعامل مع العملية المشبوهة: {str(e)}")

    def _monitor_files(self):
        """مراقبة الملفات"""
        try:
            # مراقبة مجلدات مهمة
            important_dirs = [
                os.path.expanduser("~\\Desktop"),
                os.path.expanduser("~\\Downloads"),
                "C:\\Windows\\System32",
                "C:\\Program Files",
                "C:\\Program Files (x86)"
            ]

            # حفظ حالة الملفات الحالية
            file_states = {}
            for directory in important_dirs:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                stat = os.stat(file_path)
                                file_states[file_path] = {
                                    'size': stat.st_size,
                                    'mtime': stat.st_mtime
                                }
                            except:
                                continue

            while not self.stop_monitoring.is_set():
                try:
                    # فحص التغييرات
                    for directory in important_dirs:
                        if not os.path.exists(directory):
                            continue

                        for root, dirs, files in os.walk(directory):
                            for file in files:
                                file_path = os.path.join(root, file)

                                try:
                                    stat = os.stat(file_path)
                                    current_state = {
                                        'size': stat.st_size,
                                        'mtime': stat.st_mtime
                                    }

                                    # فحص الملفات الجديدة أو المعدلة
                                    if (file_path not in file_states or
                                        file_states[file_path] != current_state):

                                        # فحص الملف
                                        is_threat, detection = self.scan_file(file_path)
                                        if is_threat and detection:
                                            self.threat_detections.append(detection)

                                            if self.config["auto_quarantine"]:
                                                if self.quarantine_file(file_path):
                                                    detection.quarantined = True

                                            self.logger.warning(f"تم كشف تهديد في الملف: {detection.description}")

                                        file_states[file_path] = current_state

                                except:
                                    continue

                    # انتظار قبل الفحص التالي
                    time.sleep(30)

                except Exception as e:
                    self.logger.error(f"خطأ في مراقبة الملفات: {str(e)}")
                    time.sleep(60)

        except Exception as e:
            self.logger.error(f"خطأ في خيط مراقبة الملفات: {str(e)}")

    def _monitor_network(self):
        """مراقبة الشبكة"""
        try:
            while not self.stop_monitoring.is_set():
                try:
                    # فحص الاتصالات الشبكية
                    connections = psutil.net_connections()

                    for conn in connections:
                        if conn.status == 'ESTABLISHED' and conn.raddr:
                            # فحص عنوان IP المتصل
                            remote_ip = conn.raddr.ip

                            # فحص إذا كان IP مشبوهاً
                            if self._is_ip_suspicious(remote_ip):
                                self._handle_suspicious_connection(conn)

                    # انتظار قبل الفحص التالي
                    time.sleep(10)

                except Exception as e:
                    self.logger.error(f"خطأ في مراقبة الشبكة: {str(e)}")
                    time.sleep(30)

        except Exception as e:
            self.logger.error(f"خطأ في خيط مراقبة الشبكة: {str(e)}")

    def _is_ip_suspicious(self, ip: str) -> bool:
        """فحص إذا كان عنوان IP مشبوهاً"""
        try:
            # قائمة عناوين IP المشبوهة (يمكن تحديثها من قواعد بيانات خارجية)
            suspicious_ranges = [
                '10.0.0.0/8',      # شبكات خاصة
                '**********/12',   # شبكات خاصة
                '***********/16',  # شبكات خاصة
            ]

            # فحص نطاقات معروفة بالخطر (مثال)
            known_malicious = [
                '*************/24',  # مثال على نطاق خبيث
                '*************/24',  # مثال على نطاق خبيث
            ]

            # يمكن إضافة منطق أكثر تعقيداً هنا
            return False

        except Exception as e:
            self.logger.error(f"خطأ في فحص IP المشبوه: {str(e)}")
            return False

    def _handle_suspicious_connection(self, connection):
        """التعامل مع الاتصال المشبوه"""
        try:
            detection = ThreatDetection(
                detection_id=self._generate_detection_id(),
                threat_type=ThreatType.NETWORK_THREAT,
                threat_level=ThreatLevel.MEDIUM,
                file_path="",
                process_name="",
                description=f"اتصال شبكي مشبوه: {connection.raddr.ip}:{connection.raddr.port}",
                detected_at=datetime.now(),
                hash_value="",
                file_size=0
            )

            self.threat_detections.append(detection)
            self._save_threat_detections()

            self.logger.warning(f"تم كشف اتصال مشبوه: {detection.description}")

        except Exception as e:
            self.logger.error(f"خطأ في التعامل مع الاتصال المشبوه: {str(e)}")

    def quarantine_file(self, file_path: str) -> bool:
        """وضع ملف في الحجر الصحي"""
        try:
            source_path = Path(file_path)
            if not source_path.exists():
                return False

            # إنشاء اسم فريد للملف في الحجر الصحي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            quarantine_name = f"{timestamp}_{source_path.name}"
            quarantine_path = self.quarantine_dir / quarantine_name

            # نسخ الملف إلى الحجر الصحي
            shutil.copy2(source_path, quarantine_path)

            # حذف الملف الأصلي
            source_path.unlink()

            # حفظ معلومات الحجر الصحي
            quarantine_info = {
                'original_path': str(source_path),
                'quarantine_path': str(quarantine_path),
                'quarantine_date': datetime.now().isoformat(),
                'file_hash': self._calculate_file_hash(quarantine_path),
                'file_size': quarantine_path.stat().st_size
            }

            quarantine_info_file = self.quarantine_dir / f"{quarantine_name}.info"
            with open(quarantine_info_file, 'w', encoding='utf-8') as f:
                json.dump(quarantine_info, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم وضع الملف في الحجر الصحي: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في وضع الملف في الحجر الصحي {file_path}: {str(e)}")
            return False

    def restore_from_quarantine(self, quarantine_name: str) -> bool:
        """استعادة ملف من الحجر الصحي"""
        try:
            quarantine_path = self.quarantine_dir / quarantine_name
            quarantine_info_file = self.quarantine_dir / f"{quarantine_name}.info"

            if not quarantine_path.exists() or not quarantine_info_file.exists():
                return False

            # تحميل معلومات الحجر الصحي
            with open(quarantine_info_file, 'r', encoding='utf-8') as f:
                quarantine_info = json.load(f)

            original_path = Path(quarantine_info['original_path'])

            # التأكد من عدم وجود الملف الأصلي
            if original_path.exists():
                self.logger.warning(f"الملف الأصلي موجود بالفعل: {original_path}")
                return False

            # إنشاء المجلد الأصلي إذا لم يكن موجوداً
            original_path.parent.mkdir(parents=True, exist_ok=True)

            # استعادة الملف
            shutil.copy2(quarantine_path, original_path)

            # حذف الملف من الحجر الصحي
            quarantine_path.unlink()
            quarantine_info_file.unlink()

            self.logger.info(f"تم استعادة الملف من الحجر الصحي: {original_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في استعادة الملف من الحجر الصحي {quarantine_name}: {str(e)}")
            return False

    def delete_from_quarantine(self, quarantine_name: str) -> bool:
        """حذف ملف من الحجر الصحي نهائياً"""
        try:
            quarantine_path = self.quarantine_dir / quarantine_name
            quarantine_info_file = self.quarantine_dir / f"{quarantine_name}.info"

            # حذف الملف ومعلوماته
            if quarantine_path.exists():
                quarantine_path.unlink()

            if quarantine_info_file.exists():
                quarantine_info_file.unlink()

            self.logger.info(f"تم حذف الملف من الحجر الصحي نهائياً: {quarantine_name}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في حذف الملف من الحجر الصحي {quarantine_name}: {str(e)}")
            return False

    def get_quarantine_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة الملفات في الحجر الصحي"""
        try:
            quarantine_list = []

            for info_file in self.quarantine_dir.glob("*.info"):
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        quarantine_info = json.load(f)

                    quarantine_name = info_file.stem
                    quarantine_info['quarantine_name'] = quarantine_name
                    quarantine_list.append(quarantine_info)

                except Exception as e:
                    self.logger.error(f"خطأ في قراءة معلومات الحجر الصحي {info_file}: {str(e)}")

            return quarantine_list

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة الحجر الصحي: {str(e)}")
            return []

    def cleanup_old_quarantine(self, days: int = None) -> Tuple[bool, int]:
        """تنظيف الحجر الصحي من الملفات القديمة"""
        try:
            if days is None:
                days = self.config["quarantine_retention_days"]

            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0

            for info_file in self.quarantine_dir.glob("*.info"):
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        quarantine_info = json.load(f)

                    quarantine_date = datetime.fromisoformat(quarantine_info['quarantine_date'])

                    if quarantine_date < cutoff_date:
                        quarantine_name = info_file.stem
                        if self.delete_from_quarantine(quarantine_name):
                            deleted_count += 1

                except Exception as e:
                    self.logger.error(f"خطأ في تنظيف ملف الحجر الصحي {info_file}: {str(e)}")

            self.logger.info(f"تم تنظيف {deleted_count} ملف من الحجر الصحي")
            return True, deleted_count

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الحجر الصحي: {str(e)}")
            return False, 0

    def add_to_whitelist(self, file_path: str = None, process_name: str = None) -> bool:
        """إضافة ملف أو عملية إلى القائمة البيضاء"""
        try:
            if file_path:
                self.whitelisted_files.add(file_path)
                self.logger.info(f"تم إضافة الملف إلى القائمة البيضاء: {file_path}")

            if process_name:
                self.whitelisted_processes.add(process_name)
                self.logger.info(f"تم إضافة العملية إلى القائمة البيضاء: {process_name}")

            self._save_whitelist()
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إضافة إلى القائمة البيضاء: {str(e)}")
            return False

    def remove_from_whitelist(self, file_path: str = None, process_name: str = None) -> bool:
        """إزالة ملف أو عملية من القائمة البيضاء"""
        try:
            if file_path and file_path in self.whitelisted_files:
                self.whitelisted_files.remove(file_path)
                self.logger.info(f"تم إزالة الملف من القائمة البيضاء: {file_path}")

            if process_name and process_name in self.whitelisted_processes:
                self.whitelisted_processes.remove(process_name)
                self.logger.info(f"تم إزالة العملية من القائمة البيضاء: {process_name}")

            self._save_whitelist()
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إزالة من القائمة البيضاء: {str(e)}")
            return False

    def get_protection_statistics(self, days: int = 30) -> Dict[str, Any]:
        """الحصول على إحصائيات الحماية"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # فلترة التهديدات حسب التاريخ
            recent_threats = [
                threat for threat in self.threat_detections
                if threat.detected_at >= cutoff_date
            ]

            # فلترة نتائج الفحص حسب التاريخ
            recent_scans = [
                scan for scan in self.scan_results
                if scan.start_time >= cutoff_date
            ]

            # حساب الإحصائيات
            stats = {
                "period_days": days,
                "total_threats_detected": len(recent_threats),
                "threats_by_type": {},
                "threats_by_level": {},
                "total_scans": len(recent_scans),
                "total_files_scanned": sum(scan.scanned_files for scan in recent_scans),
                "quarantined_files": len([t for t in recent_threats if t.quarantined]),
                "resolved_threats": len([t for t in recent_threats if t.resolved]),
                "false_positives": len([t for t in recent_threats if t.false_positive]),
                "real_time_protection_status": self.is_real_time_protection_enabled,
                "current_scan_status": self.current_scan.status.value if self.current_scan else "idle",
                "quarantine_files_count": len(self.get_quarantine_list()),
                "whitelist_files_count": len(self.whitelisted_files),
                "whitelist_processes_count": len(self.whitelisted_processes)
            }

            # إحصائيات حسب نوع التهديد
            for threat in recent_threats:
                threat_type = threat.threat_type.value
                stats["threats_by_type"][threat_type] = stats["threats_by_type"].get(threat_type, 0) + 1

            # إحصائيات حسب مستوى التهديد
            for threat in recent_threats:
                threat_level = threat.threat_level.value
                stats["threats_by_level"][threat_level] = stats["threats_by_level"].get(threat_level, 0) + 1

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الحماية: {str(e)}")
            return {}

    def get_system_health(self) -> Dict[str, Any]:
        """الحصول على حالة صحة النظام"""
        try:
            health = {
                "overall_status": "healthy",
                "real_time_protection": self.is_real_time_protection_enabled,
                "monitoring_threads": len([t for t in self.monitoring_threads if t.is_alive()]),
                "last_scan": None,
                "threats_pending": len([t for t in self.threat_detections if not t.resolved]),
                "quarantine_size_mb": 0,
                "database_size_mb": 0,
                "log_size_mb": 0,
                "uptime_hours": 0
            }

            # آخر فحص
            if self.scan_results:
                last_scan = max(self.scan_results, key=lambda x: x.start_time)
                health["last_scan"] = {
                    "date": last_scan.start_time.isoformat(),
                    "type": last_scan.scan_type,
                    "status": last_scan.status.value,
                    "threats_found": last_scan.threats_found
                }

            # حجم الحجر الصحي
            quarantine_size = 0
            for file_path in self.quarantine_dir.glob("*"):
                if file_path.is_file() and not file_path.name.endswith('.info'):
                    quarantine_size += file_path.stat().st_size
            health["quarantine_size_mb"] = round(quarantine_size / (1024 * 1024), 2)

            # حجم قواعد البيانات
            db_files = [self.detections_file, self.scan_results_file, self.config_file]
            db_size = sum(f.stat().st_size for f in db_files if f.exists())
            health["database_size_mb"] = round(db_size / (1024 * 1024), 2)

            # حجم السجلات
            log_size = 0
            for log_file in self.logs_dir.glob("*.log"):
                log_size += log_file.stat().st_size
            health["log_size_mb"] = round(log_size / (1024 * 1024), 2)

            # تحديد الحالة العامة
            if health["threats_pending"] > 10:
                health["overall_status"] = "warning"
            elif not health["real_time_protection"]:
                health["overall_status"] = "warning"
            elif health["threats_pending"] > 50:
                health["overall_status"] = "critical"

            return health

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حالة صحة النظام: {str(e)}")
            return {"overall_status": "error"}

    def _generate_detection_id(self) -> str:
        """توليد معرف فريد للكشف"""
        return f"DET_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.threat_detections):04d}"

    def _generate_scan_id(self) -> str:
        """توليد معرف فريد للفحص"""
        return f"SCAN_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.scan_results):04d}"

    def _load_threat_detections(self):
        """تحميل كشوفات التهديدات"""
        try:
            if self.detections_file.exists():
                with open(self.detections_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.threat_detections = []
                for item in data:
                    # تحويل التواريخ
                    item['detected_at'] = datetime.fromisoformat(item['detected_at'])

                    # تحويل الـ enums
                    item['threat_type'] = ThreatType(item['threat_type'])
                    item['threat_level'] = ThreatLevel(item['threat_level'])

                    detection = ThreatDetection(**item)
                    self.threat_detections.append(detection)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل كشوفات التهديدات: {str(e)}")

    def _save_threat_detections(self):
        """حفظ كشوفات التهديدات"""
        try:
            data = []
            for detection in self.threat_detections:
                item = asdict(detection)
                # تحويل التواريخ إلى نص
                item['detected_at'] = detection.detected_at.isoformat()
                # تحويل الـ enums إلى نص
                item['threat_type'] = detection.threat_type.value
                item['threat_level'] = detection.threat_level.value
                data.append(item)

            with open(self.detections_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ كشوفات التهديدات: {str(e)}")

    def _load_scan_results(self):
        """تحميل نتائج الفحص"""
        try:
            if self.scan_results_file.exists():
                with open(self.scan_results_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.scan_results = []
                for item in data:
                    # تحويل التواريخ
                    item['start_time'] = datetime.fromisoformat(item['start_time'])
                    if item['end_time']:
                        item['end_time'] = datetime.fromisoformat(item['end_time'])

                    # تحويل الـ enum
                    item['status'] = ScanStatus(item['status'])

                    scan_result = ScanResult(**item)
                    self.scan_results.append(scan_result)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل نتائج الفحص: {str(e)}")

    def _save_scan_results(self):
        """حفظ نتائج الفحص"""
        try:
            data = []
            for scan_result in self.scan_results:
                item = asdict(scan_result)
                # تحويل التواريخ إلى نص
                item['start_time'] = scan_result.start_time.isoformat()
                if scan_result.end_time:
                    item['end_time'] = scan_result.end_time.isoformat()
                # تحويل الـ enum إلى نص
                item['status'] = scan_result.status.value
                data.append(item)

            with open(self.scan_results_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ نتائج الفحص: {str(e)}")

    def _load_whitelist(self):
        """تحميل القائمة البيضاء"""
        try:
            if self.whitelist_file.exists():
                with open(self.whitelist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.whitelisted_files = set(data.get('files', []))
                self.whitelisted_processes = set(data.get('processes', []))

        except Exception as e:
            self.logger.error(f"خطأ في تحميل القائمة البيضاء: {str(e)}")

    def _save_whitelist(self):
        """حفظ القائمة البيضاء"""
        try:
            data = {
                'files': list(self.whitelisted_files),
                'processes': list(self.whitelisted_processes)
            }

            with open(self.whitelist_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ القائمة البيضاء: {str(e)}")

    def update_malware_signatures(self, signatures: List[Dict[str, Any]]) -> bool:
        """تحديث توقيعات البرمجيات الخبيثة"""
        try:
            # حفظ التوقيعات الجديدة
            signatures_file = self.signatures_dir / "malware_signatures.json"
            with open(signatures_file, 'w', encoding='utf-8') as f:
                json.dump(signatures, f, ensure_ascii=False, indent=2)

            # إعادة تحميل التوقيعات
            self.malware_signatures = self._load_malware_signatures()

            self.logger.info(f"تم تحديث {len(signatures)} توقيع للبرمجيات الخبيثة")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في تحديث توقيعات البرمجيات الخبيثة: {str(e)}")
            return False

    def update_malware_hashes(self, hashes: List[str]) -> bool:
        """تحديث هاشات البرمجيات الخبيثة"""
        try:
            # حفظ الهاشات الجديدة
            hashes_file = self.signatures_dir / "malware_hashes.json"
            with open(hashes_file, 'w', encoding='utf-8') as f:
                json.dump(hashes, f, ensure_ascii=False, indent=2)

            # إعادة تحميل الهاشات
            self.known_malware_hashes = self._load_known_malware_hashes()

            self.logger.info(f"تم تحديث {len(hashes)} هاش للبرمجيات الخبيثة")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في تحديث هاشات البرمجيات الخبيثة: {str(e)}")
            return False

    def export_protection_data(self, export_path: str = None) -> Tuple[bool, str]:
        """تصدير بيانات الحماية"""
        try:
            if export_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_path = self.protection_dir / f"protection_export_{timestamp}.json"

            export_data = {
                "export_date": datetime.now().isoformat(),
                "config": self.config,
                "threat_detections": [],
                "scan_results": [],
                "whitelist": {
                    "files": list(self.whitelisted_files),
                    "processes": list(self.whitelisted_processes)
                },
                "statistics": self.get_protection_statistics(),
                "system_health": self.get_system_health()
            }

            # تحويل كشوفات التهديدات
            for detection in self.threat_detections:
                item = asdict(detection)
                item['detected_at'] = detection.detected_at.isoformat()
                item['threat_type'] = detection.threat_type.value
                item['threat_level'] = detection.threat_level.value
                export_data["threat_detections"].append(item)

            # تحويل نتائج الفحص
            for scan_result in self.scan_results:
                item = asdict(scan_result)
                item['start_time'] = scan_result.start_time.isoformat()
                if scan_result.end_time:
                    item['end_time'] = scan_result.end_time.isoformat()
                item['status'] = scan_result.status.value
                export_data["scan_results"].append(item)

            # حفظ البيانات
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير بيانات الحماية إلى: {export_path}")
            return True, str(export_path)

        except Exception as e:
            error_msg = f"خطأ في تصدير بيانات الحماية: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def cleanup_old_data(self, days: int = None) -> Tuple[bool, Dict[str, int]]:
        """تنظيف البيانات القديمة"""
        try:
            if days is None:
                days = self.config["log_retention_days"]

            cutoff_date = datetime.now() - timedelta(days=days)
            cleanup_stats = {
                "threat_detections_removed": 0,
                "scan_results_removed": 0,
                "log_files_cleaned": 0,
                "quarantine_files_removed": 0
            }

            # تنظيف كشوفات التهديدات القديمة
            old_detections = [
                d for d in self.threat_detections
                if d.detected_at < cutoff_date and d.resolved
            ]
            for detection in old_detections:
                self.threat_detections.remove(detection)
                cleanup_stats["threat_detections_removed"] += 1

            # تنظيف نتائج الفحص القديمة
            old_scans = [
                s for s in self.scan_results
                if s.start_time < cutoff_date
            ]
            for scan in old_scans:
                self.scan_results.remove(scan)
                cleanup_stats["scan_results_removed"] += 1

            # تنظيف ملفات السجلات القديمة
            for log_file in self.logs_dir.glob("*.log"):
                try:
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        log_file.unlink()
                        cleanup_stats["log_files_cleaned"] += 1
                except:
                    continue

            # تنظيف الحجر الصحي
            success, quarantine_removed = self.cleanup_old_quarantine(days)
            if success:
                cleanup_stats["quarantine_files_removed"] = quarantine_removed

            # حفظ البيانات المحدثة
            self._save_threat_detections()
            self._save_scan_results()

            self.logger.info(f"تم تنظيف البيانات القديمة: {cleanup_stats}")
            return True, cleanup_stats

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")
            return False, {}

    def shutdown(self):
        """إيقاف النظام بأمان"""
        try:
            # إيقاف المراقبة في الوقت الفعلي
            self.stop_real_time_protection()

            # إيقاف الفحص الحالي إذا كان جارياً
            if self.is_scanning:
                self.stop_monitoring.set()
                time.sleep(2)  # انتظار قصير لإنهاء الفحص

            # حفظ جميع البيانات
            self._save_config()
            self._save_threat_detections()
            self._save_scan_results()
            self._save_whitelist()

            self.logger.info("تم إيقاف نظام الحماية من البرمجيات الخبيثة بأمان")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف النظام: {str(e)}")


# دوال مساعدة للاستخدام الخارجي
def create_malware_protection_system(base_dir: str = "data") -> MalwareProtectionSystem:
    """إنشاء نظام حماية من البرمجيات الخبيثة"""
    return MalwareProtectionSystem(base_dir)


def scan_file_for_malware(file_path: str, protection_system: MalwareProtectionSystem = None) -> Tuple[bool, Optional[ThreatDetection]]:
    """فحص ملف واحد للبرمجيات الخبيثة"""
    if protection_system is None:
        protection_system = MalwareProtectionSystem()

    return protection_system.scan_file(file_path)


def scan_directory_for_malware(directory_path: str, recursive: bool = True, protection_system: MalwareProtectionSystem = None) -> ScanResult:
    """فحص مجلد للبرمجيات الخبيثة"""
    if protection_system is None:
        protection_system = MalwareProtectionSystem()

    return protection_system.scan_directory(directory_path, recursive)


if __name__ == "__main__":
    # مثال على الاستخدام
    print("🛡️ نظام الحماية من البرمجيات الخبيثة")
    print("=" * 50)

    # إنشاء النظام
    protection = MalwareProtectionSystem()

    # فحص سريع
    print("🔍 بدء الفحص السريع...")
    result = protection.quick_scan()

    print(f"✅ اكتمل الفحص: {result.scanned_files} ملف، {result.threats_found} تهديد")

    # عرض الإحصائيات
    stats = protection.get_protection_statistics()
    print(f"📊 إحصائيات الحماية: {stats['total_threats_detected']} تهديد في آخر {stats['period_days']} يوم")

    # إيقاف النظام
    protection.shutdown()
    print("🛑 تم إيقاف النظام بأمان")
