#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المؤثرات - Effects Manager
يدير المؤثرات البصرية والصوتية
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import cv2
import moviepy.editor as mp
from PIL import Image, ImageFilter, ImageEnhance
import json

class VideoEffect:
    """مؤثر بصري"""
    
    def __init__(self, effect_type: str, parameters: Dict[str, Any] = None):
        self.effect_type = effect_type
        self.parameters = parameters or {}
        self.start_time = 0.0
        self.end_time = 0.0
        self.intensity = 1.0
        self.enabled = True
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "effect_type": self.effect_type,
            "parameters": self.parameters,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "intensity": self.intensity,
            "enabled": self.enabled
        }

class AudioEffect:
    """مؤثر صوتي"""
    
    def __init__(self, effect_type: str, parameters: Dict[str, Any] = None):
        self.effect_type = effect_type
        self.parameters = parameters or {}
        self.start_time = 0.0
        self.end_time = 0.0
        self.intensity = 1.0
        self.enabled = True
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "effect_type": self.effect_type,
            "parameters": self.parameters,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "intensity": self.intensity,
            "enabled": self.enabled
        }

class EffectsManager:
    """مدير المؤثرات"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات المؤثرات
        self.settings = self._load_effects_settings()
        
        # تحميل مكتبة المؤثرات
        self.video_effects_library = self._load_video_effects_library()
        self.audio_effects_library = self._load_audio_effects_library()
        
        # إحصائيات
        self.stats = {
            "video_effects_applied": 0,
            "audio_effects_applied": 0,
            "custom_effects_created": 0
        }
    
    def _load_effects_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المؤثرات"""
        try:
            return self.config_manager.get_setting("editing_settings", "effects_manager", {
                "auto_color_correction": True,
                "auto_stabilization": False,
                "default_transition_duration": 0.5,
                "max_effect_intensity": 1.0,
                "enable_gpu_acceleration": False,
                "quality_preset": "high",
                "cache_effects": True,
                "parallel_processing": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المؤثرات: {str(e)}")
            return {
                "auto_color_correction": True,
                "auto_stabilization": False,
                "default_transition_duration": 0.5,
                "max_effect_intensity": 1.0,
                "enable_gpu_acceleration": False,
                "quality_preset": "high",
                "cache_effects": True,
                "parallel_processing": True
            }
    
    def _load_video_effects_library(self) -> Dict[str, Dict[str, Any]]:
        """تحميل مكتبة المؤثرات البصرية"""
        return {
            "blur": {
                "name": "تشويش",
                "description": "تطبيق تشويش على الفيديو",
                "parameters": {
                    "radius": {"type": "float", "min": 0.1, "max": 10.0, "default": 2.0},
                    "type": {"type": "choice", "options": ["gaussian", "motion"], "default": "gaussian"}
                }
            },
            "sharpen": {
                "name": "تحسين الحدة",
                "description": "تحسين حدة الصورة",
                "parameters": {
                    "strength": {"type": "float", "min": 0.1, "max": 3.0, "default": 1.0}
                }
            },
            "brightness": {
                "name": "السطوع",
                "description": "تعديل سطوع الفيديو",
                "parameters": {
                    "factor": {"type": "float", "min": 0.1, "max": 3.0, "default": 1.0}
                }
            },
            "contrast": {
                "name": "التباين",
                "description": "تعديل تباين الفيديو",
                "parameters": {
                    "factor": {"type": "float", "min": 0.1, "max": 3.0, "default": 1.0}
                }
            },
            "saturation": {
                "name": "التشبع",
                "description": "تعديل تشبع الألوان",
                "parameters": {
                    "factor": {"type": "float", "min": 0.0, "max": 3.0, "default": 1.0}
                }
            },
            "vintage": {
                "name": "كلاسيكي",
                "description": "تأثير كلاسيكي قديم",
                "parameters": {
                    "sepia_strength": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.7},
                    "vignette_strength": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.5}
                }
            },
            "glow": {
                "name": "توهج",
                "description": "تأثير التوهج",
                "parameters": {
                    "radius": {"type": "float", "min": 1.0, "max": 20.0, "default": 5.0},
                    "intensity": {"type": "float", "min": 0.1, "max": 2.0, "default": 0.8}
                }
            },
            "zoom": {
                "name": "تكبير",
                "description": "تأثير التكبير التدريجي",
                "parameters": {
                    "start_scale": {"type": "float", "min": 0.5, "max": 2.0, "default": 1.0},
                    "end_scale": {"type": "float", "min": 0.5, "max": 2.0, "default": 1.2}
                }
            },
            "shake": {
                "name": "اهتزاز",
                "description": "تأثير الاهتزاز",
                "parameters": {
                    "intensity": {"type": "float", "min": 1.0, "max": 20.0, "default": 5.0},
                    "frequency": {"type": "float", "min": 0.1, "max": 10.0, "default": 2.0}
                }
            },
            "color_filter": {
                "name": "مرشح لوني",
                "description": "تطبيق مرشح لوني",
                "parameters": {
                    "color": {"type": "color", "default": "#FF6B6B"},
                    "blend_mode": {"type": "choice", "options": ["multiply", "overlay", "screen"], "default": "overlay"},
                    "opacity": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.3}
                }
            }
        }
    
    def _load_audio_effects_library(self) -> Dict[str, Dict[str, Any]]:
        """تحميل مكتبة المؤثرات الصوتية"""
        return {
            "reverb": {
                "name": "صدى",
                "description": "تأثير الصدى",
                "parameters": {
                    "room_size": {"type": "float", "min": 0.1, "max": 1.0, "default": 0.5},
                    "damping": {"type": "float", "min": 0.1, "max": 1.0, "default": 0.5},
                    "wet_level": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.3}
                }
            },
            "echo": {
                "name": "صدى بسيط",
                "description": "تأثير الصدى البسيط",
                "parameters": {
                    "delay_ms": {"type": "int", "min": 50, "max": 2000, "default": 500},
                    "feedback": {"type": "float", "min": 0.0, "max": 0.9, "default": 0.3},
                    "mix": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.5}
                }
            },
            "pitch_shift": {
                "name": "تغيير الطبقة",
                "description": "تغيير طبقة الصوت",
                "parameters": {
                    "semitones": {"type": "int", "min": -12, "max": 12, "default": 0}
                }
            },
            "distortion": {
                "name": "تشويه",
                "description": "تأثير التشويه",
                "parameters": {
                    "drive": {"type": "float", "min": 1.0, "max": 10.0, "default": 2.0},
                    "tone": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.5}
                }
            },
            "chorus": {
                "name": "كورس",
                "description": "تأثير الكورس",
                "parameters": {
                    "rate": {"type": "float", "min": 0.1, "max": 5.0, "default": 1.0},
                    "depth": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.5},
                    "mix": {"type": "float", "min": 0.0, "max": 1.0, "default": 0.5}
                }
            }
        }
    
    def apply_video_effect(self, video_clip, effect: VideoEffect) -> mp.VideoClip:
        """تطبيق مؤثر بصري"""
        try:
            if not effect.enabled:
                return video_clip
            
            self.logger.info(f"تطبيق المؤثر البصري: {effect.effect_type}")
            
            effect_type = effect.effect_type
            parameters = effect.parameters
            intensity = effect.intensity
            
            if effect_type == "blur":
                return self._apply_blur_effect(video_clip, parameters, intensity)
            elif effect_type == "sharpen":
                return self._apply_sharpen_effect(video_clip, parameters, intensity)
            elif effect_type == "brightness":
                return self._apply_brightness_effect(video_clip, parameters, intensity)
            elif effect_type == "contrast":
                return self._apply_contrast_effect(video_clip, parameters, intensity)
            elif effect_type == "saturation":
                return self._apply_saturation_effect(video_clip, parameters, intensity)
            elif effect_type == "vintage":
                return self._apply_vintage_effect(video_clip, parameters, intensity)
            elif effect_type == "glow":
                return self._apply_glow_effect(video_clip, parameters, intensity)
            elif effect_type == "zoom":
                return self._apply_zoom_effect(video_clip, parameters, intensity)
            elif effect_type == "shake":
                return self._apply_shake_effect(video_clip, parameters, intensity)
            elif effect_type == "color_filter":
                return self._apply_color_filter_effect(video_clip, parameters, intensity)
            else:
                self.logger.warning(f"مؤثر غير مدعوم: {effect_type}")
                return video_clip
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق المؤثر البصري {effect.effect_type}: {str(e)}")
            return video_clip
        finally:
            self.stats["video_effects_applied"] += 1
    
    def _apply_blur_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير التشويش"""
        radius = parameters.get("radius", 2.0) * intensity
        blur_type = parameters.get("type", "gaussian")
        
        def blur_frame(get_frame, t):
            frame = get_frame(t)
            if blur_type == "gaussian":
                # تحويل إلى PIL للتشويش
                pil_image = Image.fromarray(frame)
                blurred = pil_image.filter(ImageFilter.GaussianBlur(radius=radius))
                return np.array(blurred)
            else:  # motion blur
                # تطبيق تشويش الحركة باستخدام OpenCV
                kernel_size = int(radius * 2) + 1
                kernel = np.zeros((kernel_size, kernel_size))
                kernel[kernel_size//2, :] = 1.0
                kernel = kernel / kernel_size
                return cv2.filter2D(frame, -1, kernel)
        
        return video_clip.fl(blur_frame)
    
    def _apply_sharpen_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير تحسين الحدة"""
        strength = parameters.get("strength", 1.0) * intensity
        
        def sharpen_frame(get_frame, t):
            frame = get_frame(t)
            # تطبيق مرشح تحسين الحدة
            kernel = np.array([[-1, -1, -1],
                              [-1, 9, -1],
                              [-1, -1, -1]]) * strength
            kernel[1, 1] = 8 + strength
            sharpened = cv2.filter2D(frame, -1, kernel)
            return np.clip(sharpened, 0, 255).astype(np.uint8)
        
        return video_clip.fl(sharpen_frame)
    
    def _apply_brightness_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير السطوع"""
        factor = parameters.get("factor", 1.0)
        # تطبيق التدرج بناءً على الكثافة
        adjusted_factor = 1.0 + (factor - 1.0) * intensity
        
        return video_clip.fx(mp.colorx, adjusted_factor)
    
    def _apply_contrast_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير التباين"""
        factor = parameters.get("factor", 1.0)
        adjusted_factor = 1.0 + (factor - 1.0) * intensity
        
        def contrast_frame(get_frame, t):
            frame = get_frame(t)
            # تطبيق التباين
            pil_image = Image.fromarray(frame)
            enhancer = ImageEnhance.Contrast(pil_image)
            enhanced = enhancer.enhance(adjusted_factor)
            return np.array(enhanced)
        
        return video_clip.fl(contrast_frame)
    
    def _apply_saturation_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير التشبع"""
        factor = parameters.get("factor", 1.0)
        adjusted_factor = 1.0 + (factor - 1.0) * intensity
        
        def saturation_frame(get_frame, t):
            frame = get_frame(t)
            # تحويل إلى HSV
            hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV).astype(np.float32)
            # تعديل التشبع
            hsv[:, :, 1] = hsv[:, :, 1] * adjusted_factor
            hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)
            # تحويل العودة إلى RGB
            rgb = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
            return rgb
        
        return video_clip.fl(saturation_frame)
    
    def _apply_vintage_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير كلاسيكي"""
        sepia_strength = parameters.get("sepia_strength", 0.7) * intensity
        vignette_strength = parameters.get("vignette_strength", 0.5) * intensity
        
        def vintage_frame(get_frame, t):
            frame = get_frame(t)
            h, w = frame.shape[:2]
            
            # تطبيق تأثير السيبيا
            sepia_kernel = np.array([[0.272, 0.534, 0.131],
                                   [0.349, 0.686, 0.168],
                                   [0.393, 0.769, 0.189]])
            
            sepia_frame = cv2.transform(frame, sepia_kernel)
            frame = frame * (1 - sepia_strength) + sepia_frame * sepia_strength
            
            # تطبيق تأثير الفينيت
            if vignette_strength > 0:
                # إنشاء قناع الفينيت
                center_x, center_y = w // 2, h // 2
                Y, X = np.ogrid[:h, :w]
                dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
                max_dist = np.sqrt(center_x**2 + center_y**2)
                vignette_mask = 1 - (dist_from_center / max_dist) * vignette_strength
                vignette_mask = np.clip(vignette_mask, 0, 1)
                
                # تطبيق القناع
                for i in range(3):
                    frame[:, :, i] = frame[:, :, i] * vignette_mask
            
            return np.clip(frame, 0, 255).astype(np.uint8)
        
        return video_clip.fl(vintage_frame)
    
    def _apply_glow_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير التوهج"""
        radius = parameters.get("radius", 5.0) * intensity
        glow_intensity = parameters.get("intensity", 0.8) * intensity
        
        def glow_frame(get_frame, t):
            frame = get_frame(t)
            
            # إنشاء طبقة التوهج
            blurred = cv2.GaussianBlur(frame, (int(radius*2)+1, int(radius*2)+1), radius)
            
            # مزج مع الإطار الأصلي
            glowed = cv2.addWeighted(frame, 1.0, blurred, glow_intensity, 0)
            
            return np.clip(glowed, 0, 255).astype(np.uint8)
        
        return video_clip.fl(glow_frame)
    
    def _apply_zoom_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير التكبير"""
        start_scale = parameters.get("start_scale", 1.0)
        end_scale = parameters.get("end_scale", 1.2)
        
        # تطبيق التدرج بناءً على الكثافة
        scale_diff = (end_scale - start_scale) * intensity
        actual_end_scale = start_scale + scale_diff
        
        def zoom_frame(get_frame, t):
            frame = get_frame(t)
            progress = t / video_clip.duration if video_clip.duration > 0 else 0
            current_scale = start_scale + (actual_end_scale - start_scale) * progress
            
            h, w = frame.shape[:2]
            center_x, center_y = w // 2, h // 2
            
            # حساب الحجم الجديد
            new_w, new_h = int(w * current_scale), int(h * current_scale)
            
            # تغيير الحجم
            resized = cv2.resize(frame, (new_w, new_h))
            
            # قص من المركز
            if current_scale > 1.0:
                start_x = (new_w - w) // 2
                start_y = (new_h - h) // 2
                cropped = resized[start_y:start_y+h, start_x:start_x+w]
                return cropped
            else:
                # إضافة حدود سوداء
                result = np.zeros_like(frame)
                start_x = (w - new_w) // 2
                start_y = (h - new_h) // 2
                result[start_y:start_y+new_h, start_x:start_x+new_w] = resized
                return result
        
        return video_clip.fl(zoom_frame)
    
    def _apply_shake_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق تأثير الاهتزاز"""
        shake_intensity = parameters.get("intensity", 5.0) * intensity
        frequency = parameters.get("frequency", 2.0)
        
        def shake_frame(get_frame, t):
            frame = get_frame(t)
            
            # حساب الإزاحة
            offset_x = int(shake_intensity * np.sin(2 * np.pi * frequency * t))
            offset_y = int(shake_intensity * np.cos(2 * np.pi * frequency * t * 1.3))
            
            # تطبيق الإزاحة
            h, w = frame.shape[:2]
            M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
            shaken = cv2.warpAffine(frame, M, (w, h))
            
            return shaken
        
        return video_clip.fl(shake_frame)
    
    def _apply_color_filter_effect(self, video_clip, parameters: Dict[str, Any], intensity: float):
        """تطبيق مرشح لوني"""
        color = parameters.get("color", "#FF6B6B")
        blend_mode = parameters.get("blend_mode", "overlay")
        opacity = parameters.get("opacity", 0.3) * intensity
        
        # تحويل اللون من hex إلى RGB
        color = color.lstrip('#')
        r, g, b = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        
        def color_filter_frame(get_frame, t):
            frame = get_frame(t)
            
            # إنشاء طبقة اللون
            color_layer = np.full_like(frame, [r, g, b])
            
            # تطبيق وضع المزج
            if blend_mode == "multiply":
                result = (frame.astype(np.float32) * color_layer.astype(np.float32)) / 255.0
            elif blend_mode == "overlay":
                result = frame.astype(np.float32)
                mask = result < 128
                result[mask] = 2 * result[mask] * color_layer[mask] / 255.0
                result[~mask] = 255 - 2 * (255 - result[~mask]) * (255 - color_layer[~mask]) / 255.0
            else:  # screen
                result = 255 - (255 - frame.astype(np.float32)) * (255 - color_layer.astype(np.float32)) / 255.0
            
            # مزج مع الإطار الأصلي
            final = frame.astype(np.float32) * (1 - opacity) + result * opacity
            
            return np.clip(final, 0, 255).astype(np.uint8)
        
        return video_clip.fl(color_filter_frame)
    
    def get_available_effects(self) -> Dict[str, Any]:
        """الحصول على قائمة المؤثرات المتاحة"""
        return {
            "video_effects": self.video_effects_library,
            "audio_effects": self.audio_effects_library
        }
    
    def create_custom_effect(self, name: str, effect_type: str, 
                           parameters: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء مؤثر مخصص"""
        try:
            custom_effect = {
                "name": name,
                "type": effect_type,
                "parameters": parameters,
                "created_at": "now",
                "custom": True
            }
            
            # حفظ المؤثر المخصص
            custom_effects = self.config_manager.get_setting("custom_effects", "effects", {})
            custom_effects[name] = custom_effect
            self.config_manager.set_setting("custom_effects", "effects", custom_effects)
            
            self.stats["custom_effects_created"] += 1
            
            self.logger.info(f"تم إنشاء مؤثر مخصص: {name}")
            return custom_effect
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المؤثر المخصص: {str(e)}")
            raise
    
    def get_effects_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المؤثرات"""
        return {
            "video_effects_applied": self.stats["video_effects_applied"],
            "audio_effects_applied": self.stats["audio_effects_applied"],
            "custom_effects_created": self.stats["custom_effects_created"],
            "available_video_effects": len(self.video_effects_library),
            "available_audio_effects": len(self.audio_effects_library)
        }
