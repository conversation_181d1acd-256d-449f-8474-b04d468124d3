#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق سطح مكتب ذكي لجمع ومونتاج ونشر المحتوى تلقائياً
Smart Desktop App for Content Collection, Editing and Auto-Publishing

المطور: Augment Agent
التاريخ: 2025-07-01
"""

import sys
import os
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import QThread, QTimer
from PyQt6.QtGui import QIcon

# إضافة مجلد المشروع إلى المسار
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# استيراد الوحدات الرئيسية
from src.gui.main_window import MainWindow
from src.core.config_manager import ConfigManager
from src.core.logger import setup_logger
from src.core.security import SecurityManager

class SmartContentApp:
    """الفئة الرئيسية للتطبيق"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.config_manager = None
        self.security_manager = None
        self.logger = None
        
    def initialize(self):
        """تهيئة التطبيق"""
        try:
            # إعداد نظام التسجيل
            self.logger = setup_logger()
            self.logger.info("بدء تشغيل التطبيق...")
            
            # إنشاء تطبيق Qt
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Smart Content Creator")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("Augment Code")
            
            # تهيئة مدير الإعدادات
            self.config_manager = ConfigManager()
            
            # تهيئة مدير الأمان
            self.security_manager = SecurityManager()
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow()
            
            # عرض النافذة
            self.main_window.show()
            
            self.logger.info("تم تشغيل التطبيق بنجاح")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"خطأ في تهيئة التطبيق: {str(e)}")
            else:
                print(f"خطأ في تهيئة التطبيق: {str(e)}")
            return False
    
    def run(self):
        """تشغيل التطبيق"""
        if self.initialize():
            return self.app.exec()
        return 1

def main():
    """النقطة الرئيسية لدخول التطبيق"""
    app = SmartContentApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
