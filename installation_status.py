#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير حالة تثبيت المكتبات - منشئ المحتوى الذكي
"""

import importlib
import sys
from required_libraries import REQUIRED_LIBRARIES, get_essential_libraries

def check_library(lib_name):
    """فحص مكتبة واحدة"""
    try:
        importlib.import_module(lib_name)
        return True, "✅ مثبت"
    except ImportError:
        return False, "❌ غير مثبت"
    except Exception as e:
        return False, f"⚠️ خطأ: {str(e)[:50]}"

def check_special_libraries():
    """فحص المكتبات ذات الأسماء الخاصة"""
    special_mapping = {
        'opencv-python': 'cv2',
        'pillow': 'PIL',
        'python-dotenv': 'dotenv',
        'scikit-learn': 'sklearn',
        'beautifulsoup4': 'bs4',
        'python-dateutil': 'dateutil',
        'pyyaml': 'yaml',
        'python-jose': 'jose',
        'python-magic': 'magic',
        'python-barcode': 'barcode',
        'mysql-connector-python': 'mysql.connector',
        'psycopg2-binary': 'psycopg2',
        'opencv-python-headless': 'cv2',
        'PyQt6-tools': 'qt6_applications',
        'openai-whisper': 'whisper',
        'face-recognition': 'face_recognition',
        'python-socketio': 'socketio',
        'websockets': 'websockets',
        'ffmpeg-python': 'ffmpeg',
    }
    return special_mapping

def generate_installation_report():
    """إنشاء تقرير شامل عن حالة التثبيت"""
    print("🔍 تقرير حالة تثبيت المكتبات")
    print("=" * 60)
    
    special_mapping = check_special_libraries()
    
    total_installed = 0
    total_missing = 0
    category_results = {}
    
    for category, libraries in REQUIRED_LIBRARIES.items():
        print(f"\n📦 {category.replace('_', ' ').title()}:")
        print("-" * 40)
        
        installed_count = 0
        missing_libs = []
        
        for lib in libraries:
            # تحديد اسم المكتبة للفحص
            import_name = special_mapping.get(lib, lib)
            
            # محاولة فحص المكتبة
            is_installed, status = check_library(import_name)
            
            if is_installed:
                print(f"  {status} {lib}")
                installed_count += 1
                total_installed += 1
            else:
                print(f"  {status} {lib}")
                missing_libs.append(lib)
                total_missing += 1
        
        category_results[category] = {
            'installed': installed_count,
            'total': len(libraries),
            'missing': missing_libs
        }
        
        percentage = (installed_count / len(libraries)) * 100
        print(f"  📊 مثبت: {installed_count}/{len(libraries)} ({percentage:.1f}%)")
    
    # ملخص عام
    print(f"\n📊 الملخص العام:")
    print("=" * 60)
    total_libs = total_installed + total_missing
    overall_percentage = (total_installed / total_libs) * 100 if total_libs > 0 else 0
    
    print(f"✅ مثبت: {total_installed}")
    print(f"❌ مفقود: {total_missing}")
    print(f"📈 النسبة: {overall_percentage:.1f}%")
    
    # المكتبات الأساسية
    print(f"\n🎯 حالة المكتبات الأساسية:")
    print("-" * 40)
    essential_libs = get_essential_libraries()
    essential_installed = 0
    
    for lib in essential_libs:
        import_name = special_mapping.get(lib, lib)
        is_installed, status = check_library(import_name)
        print(f"  {status} {lib}")
        if is_installed:
            essential_installed += 1
    
    essential_percentage = (essential_installed / len(essential_libs)) * 100
    print(f"  📊 الأساسية: {essential_installed}/{len(essential_libs)} ({essential_percentage:.1f}%)")
    
    # توصيات
    print(f"\n💡 التوصيات:")
    print("-" * 40)
    
    if essential_percentage == 100:
        print("  ✅ المكتبات الأساسية مكتملة - يمكن تشغيل التطبيق")
    elif essential_percentage >= 80:
        print("  ⚠️ معظم المكتبات الأساسية مثبتة - قد يعمل التطبيق جزئياً")
    else:
        print("  ❌ المكتبات الأساسية ناقصة - يجب تثبيت المزيد")
    
    if overall_percentage >= 90:
        print("  🎉 التثبيت شبه مكتمل - جميع الميزات متاحة")
    elif overall_percentage >= 70:
        print("  👍 التثبيت جيد - معظم الميزات متاحة")
    elif overall_percentage >= 50:
        print("  ⚠️ التثبيت متوسط - بعض الميزات قد لا تعمل")
    else:
        print("  ❌ التثبيت ناقص - يجب تثبيت المزيد من المكتبات")
    
    # المكتبات المفقودة الأكثر أهمية
    important_missing = []
    for category, result in category_results.items():
        if category in ['gui', 'video_audio', 'social_media', 'ai_ml']:
            important_missing.extend(result['missing'])
    
    if important_missing:
        print(f"\n🚨 المكتبات المفقودة المهمة:")
        print("-" * 40)
        for lib in important_missing[:10]:  # أول 10 فقط
            print(f"  • {lib}")
        if len(important_missing) > 10:
            print(f"  ... و {len(important_missing) - 10} أخرى")
    
    return category_results

def test_core_functionality():
    """اختبار الوظائف الأساسية"""
    print(f"\n🧪 اختبار الوظائف الأساسية:")
    print("-" * 40)
    
    tests = [
        ("PyQt6 GUI", "PyQt6.QtWidgets", "QApplication([])"),
        ("معالجة الصور", "PIL.Image", "Image.new('RGB', (100, 100))"),
        ("طلبات HTTP", "requests", "requests.get"),
        ("معالجة البيانات", "pandas", "pandas.DataFrame()"),
        ("العمليات الرياضية", "numpy", "numpy.array([1, 2, 3])"),
        ("التشفير", "cryptography.fernet", "Fernet.generate_key()"),
    ]
    
    working_tests = 0
    
    for test_name, module_name, test_code in tests:
        try:
            module = importlib.import_module(module_name)
            if test_code:
                exec(f"import {module_name}; {test_code}")
            print(f"  ✅ {test_name}")
            working_tests += 1
        except Exception as e:
            print(f"  ❌ {test_name}: {str(e)[:50]}")
    
    test_percentage = (working_tests / len(tests)) * 100
    print(f"  📊 الاختبارات: {working_tests}/{len(tests)} ({test_percentage:.1f}%)")
    
    return test_percentage

def main():
    """الدالة الرئيسية"""
    print("🎯 تقرير حالة تثبيت مكتبات منشئ المحتوى الذكي")
    print("Smart Content Creator Libraries Installation Status")
    print("=" * 80)
    
    # تقرير التثبيت
    category_results = generate_installation_report()
    
    # اختبار الوظائف
    test_percentage = test_core_functionality()
    
    # النتيجة النهائية
    print(f"\n🏆 النتيجة النهائية:")
    print("=" * 60)
    
    if test_percentage >= 80:
        print("  🎉 التطبيق جاهز للاستخدام!")
        print("  💡 يمكنك الآن تشغيل: python main_fixed.py")
        print("  🔧 أو بناء EXE: python -m PyInstaller smart_content_creator_simple.spec")
    elif test_percentage >= 60:
        print("  ⚠️ التطبيق يعمل جزئياً")
        print("  💡 ثبت المكتبات المفقودة لتحسين الأداء")
    else:
        print("  ❌ التطبيق يحتاج مكتبات إضافية")
        print("  💡 استخدم: pip install -r requirements_essential.txt")
    
    print(f"\n📋 للمساعدة:")
    print("  • python install_libraries.py - أداة التثبيت التفاعلية")
    print("  • python required_libraries.py - عرض قائمة المكتبات")
    print("  • INSTALLATION_GUIDE.md - دليل التثبيت الشامل")

if __name__ == "__main__":
    main()
