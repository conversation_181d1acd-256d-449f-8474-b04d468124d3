#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات pytest للاختبارات
"""

import pytest
import tempfile
import shutil
from pathlib import Path
import json
import os

@pytest.fixture(scope="session")
def test_data_dir():
    """مجلد بيانات الاختبار"""
    return Path(__file__).parent / "test_data"

@pytest.fixture(scope="session")
def temp_dir():
    """مجلد مؤقت للاختبارات"""
    temp_path = Path(tempfile.mkdtemp(prefix="smart_content_test_"))
    yield temp_path
    # تنظيف المجلد المؤقت بعد الاختبارات
    if temp_path.exists():
        shutil.rmtree(temp_path)

@pytest.fixture
def sample_config():
    """إعدادات تجريبية للاختبار"""
    return {
        "app_name": "Smart Content Creator Test",
        "version": "1.0.0-test",
        "debug": True,
        "test_mode": True,
        "data_dir": "test_data",
        "log_level": "DEBUG"
    }

@pytest.fixture
def sample_video_metadata():
    """بيانات وصفية تجريبية للفيديو"""
    return {
        "title": "فيديو تجريبي",
        "description": "وصف الفيديو التجريبي",
        "duration": 30.5,
        "resolution": "1920x1080",
        "fps": 30,
        "format": "mp4",
        "size_mb": 15.2,
        "created_at": "2024-01-01T12:00:00Z"
    }

@pytest.fixture
def sample_user_data():
    """بيانات مستخدم تجريبية"""
    return {
        "username": "test_user",
        "email": "<EMAIL>",
        "platform": "tiktok",
        "api_key": "test_api_key_123",
        "settings": {
            "auto_publish": False,
            "quality": "high",
            "language": "ar"
        }
    }

@pytest.fixture(autouse=True)
def setup_test_environment(temp_dir):
    """إعداد بيئة الاختبار تلقائياً"""
    # إنشاء مجلدات الاختبار
    (temp_dir / "data").mkdir(exist_ok=True)
    (temp_dir / "logs").mkdir(exist_ok=True)
    (temp_dir / "temp").mkdir(exist_ok=True)
    (temp_dir / "output").mkdir(exist_ok=True)
    
    # تعيين متغيرات البيئة
    os.environ['TEST_TEMP_DIR'] = str(temp_dir)
    os.environ['TEST_DATA_DIR'] = str(temp_dir / "data")
    
    yield
    
    # تنظيف متغيرات البيئة
    if 'TEST_TEMP_DIR' in os.environ:
        del os.environ['TEST_TEMP_DIR']
    if 'TEST_DATA_DIR' in os.environ:
        del os.environ['TEST_DATA_DIR']

@pytest.fixture
def mock_api_response():
    """استجابة API وهمية للاختبار"""
    return {
        "status": "success",
        "data": {
            "id": "test_123",
            "url": "https://example.com/test",
            "title": "Test Content",
            "views": 1000,
            "likes": 50
        },
        "timestamp": "2024-01-01T12:00:00Z"
    }

def pytest_configure(config):
    """إعداد pytest"""
    # إضافة علامات مخصصة
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )
    config.addinivalue_line(
        "markers", "gui: marks tests as GUI tests"
    )

def pytest_collection_modifyitems(config, items):
    """تعديل عناصر الاختبار"""
    # إضافة علامة slow للاختبارات البطيئة
    for item in items:
        if "slow" in item.nodeid:
            item.add_marker(pytest.mark.slow)
