#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التشفير المتقدم
"""

import sys
import os
import json
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from src.security.advanced_encryption import (
    AdvancedEncryptionSystem,
    EncryptionLevel,
    KeyType
)

def test_encryption_system():
    """اختبار شامل لنظام التشفير"""
    print("🔐 اختبار نظام التشفير المتقدم")
    print("=" * 50)
    
    try:
        # إنشاء نظام التشفير
        encryption_system = AdvancedEncryptionSystem()
        print("✅ تم إنشاء نظام التشفير بنجاح")
        
        # اختبار البيانات
        test_data = {
            "username": "test_user",
            "password": "super_secret_password",
            "api_token": "sk-1234567890abcdef",
            "sensitive_info": "معلومات حساسة جداً"
        }
        
        print(f"\n📝 البيانات الأصلية:")
        print(json.dumps(test_data, ensure_ascii=False, indent=2))
        
        # اختبار مستويات التشفير المختلفة
        encryption_levels = [
            EncryptionLevel.BASIC,
            EncryptionLevel.STANDARD,
            EncryptionLevel.ADVANCED,
            EncryptionLevel.MILITARY
        ]
        
        for level in encryption_levels:
            print(f"\n🔒 اختبار التشفير: {level.value}")
            
            # تشفير البيانات
            encrypted_data = encryption_system.encrypt_data(test_data, level)
            print(f"✅ تم التشفير - معرف البيانات: {encrypted_data.data_id}")
            print(f"📊 حجم البيانات الأصلية: {encrypted_data.metadata['original_size']} bytes")
            print(f"📊 حجم البيانات المشفرة: {encrypted_data.metadata['encrypted_size']} bytes")
            print(f"📊 نسبة الضغط: {encrypted_data.metadata['compression_ratio']:.2f}")
            
            # فك التشفير
            decrypted_data = encryption_system.decrypt_data(encrypted_data)
            print(f"✅ تم فك التشفير بنجاح")
            
            # التحقق من صحة البيانات
            if decrypted_data == test_data:
                print("✅ البيانات المفكوكة التشفير مطابقة للأصل")
            else:
                print("❌ خطأ: البيانات المفكوكة التشفير غير مطابقة!")
                return False
        
        # اختبار تشفير الملفات
        print(f"\n📁 اختبار تشفير الملفات")
        
        # إنشاء ملف تجريبي
        test_file = Path("test_file.txt")
        test_content = "هذا ملف تجريبي يحتوي على معلومات حساسة\nThis is a test file with sensitive information"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # تشفير الملف
        encrypted_file_path = encryption_system.encrypt_file(test_file, EncryptionLevel.ADVANCED)
        print(f"✅ تم تشفير الملف: {encrypted_file_path}")
        
        # فك تشفير الملف
        decrypted_file_path = encryption_system.decrypt_file(encrypted_file_path, "decrypted_test_file.txt")
        print(f"✅ تم فك تشفير الملف: {decrypted_file_path}")
        
        # التحقق من محتوى الملف
        with open(decrypted_file_path, 'r', encoding='utf-8') as f:
            decrypted_content = f.read()
        
        if decrypted_content == test_content:
            print("✅ محتوى الملف المفكوك التشفير مطابق للأصل")
        else:
            print("❌ خطأ: محتوى الملف المفكوك التشفير غير مطابق!")
            return False
        
        # اختبار إحصائيات النظام
        print(f"\n📊 إحصائيات نظام التشفير:")
        stats = encryption_system.get_encryption_statistics()
        print(f"📈 إجمالي عمليات التشفير: {stats['total_encryptions']}")
        print(f"📈 إجمالي عمليات فك التشفير: {stats['total_decryptions']}")
        print(f"📈 إجمالي المفاتيح: {stats['total_keys']}")
        print(f"📈 العمليات الفاشلة: {stats['failed_operations']}")
        
        # اختبار تدوير المفاتيح
        print(f"\n🔄 اختبار تدوير المفاتيح:")
        rotated_keys = encryption_system.rotate_keys(force_rotation=False)
        print(f"🔄 تم تدوير {len(rotated_keys)} مفتاح")
        
        # اختبار النسخ الاحتياطي
        print(f"\n💾 اختبار النسخ الاحتياطي:")
        backup_path = Path("encryption_backup")
        backup_success = encryption_system.backup_encryption_system(backup_path)
        if backup_success:
            print(f"✅ تم إنشاء النسخة الاحتياطية في: {backup_path}")
        else:
            print("❌ فشل في إنشاء النسخة الاحتياطية")
        
        # تنظيف الملفات التجريبية
        test_file.unlink(missing_ok=True)
        Path(encrypted_file_path).unlink(missing_ok=True)
        Path(decrypted_file_path).unlink(missing_ok=True)
        
        print(f"\n🎉 تم اجتياز جميع اختبارات نظام التشفير بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار نظام التشفير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_encryption_system()
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n❌ فشلت بعض الاختبارات!")
        sys.exit(1)
