#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
متنبئ الانتشار الفيروسي - Viral Predictor
يتنبأ بإمكانية انتشار المحتوى بناءً على عوامل متعددة
"""

import logging
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import re
from ..content.base_fetcher import ContentItem

class ViralFactor:
    """عامل من عوامل الانتشار"""

    def __init__(self, name: str, weight: float, score: float = 0.0):
        self.name = name
        self.weight = weight
        self.score = score
        self.contribution = weight * score
        self.description = ""

    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "weight": self.weight,
            "score": self.score,
            "contribution": self.contribution,
            "description": self.description
        }

class ViralPrediction:
    """تنبؤ الانتشار الفيروسي"""

    def __init__(self, content_item: ContentItem):
        self.content_item = content_item
        self.overall_score = 0.0
        self.confidence = 0.0
        self.factors = {}
        self.recommendations = []
        self.risk_factors = []
        self.boost_factors = []
        self.predicted_reach = 0
        self.predicted_engagement = 0.0
        self.optimal_posting_time = None
        self.target_audience = []
        self.hashtag_suggestions = []

    def to_dict(self) -> Dict[str, Any]:
        return {
            "content_id": self.content_item.id,
            "overall_score": self.overall_score,
            "confidence": self.confidence,
            "factors": {name: factor.to_dict() for name, factor in self.factors.items()},
            "recommendations": self.recommendations,
            "risk_factors": self.risk_factors,
            "boost_factors": self.boost_factors,
            "predicted_reach": self.predicted_reach,
            "predicted_engagement": self.predicted_engagement,
            "optimal_posting_time": self.optimal_posting_time.isoformat() if self.optimal_posting_time else None,
            "target_audience": self.target_audience,
            "hashtag_suggestions": self.hashtag_suggestions
        }

class ViralPredictor:
    """متنبئ الانتشار الفيروسي"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # تحميل إعدادات التنبؤ
        self.settings = self._load_prediction_settings()

        # تحميل قواعد الانتشار
        self.viral_patterns = self._load_viral_patterns()
        self.trending_keywords = self._load_trending_keywords()
        self.audience_preferences = self._load_audience_preferences()

        # أوزان العوامل
        self.factor_weights = self._load_factor_weights()

    def _load_prediction_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات التنبؤ"""
        try:
            return self.config_manager.get_setting("ai_settings", "viral_prediction", {
                "min_confidence_threshold": 0.6,
                "viral_score_threshold": 0.7,
                "enable_trend_analysis": True,
                "enable_audience_analysis": True,
                "enable_timing_optimization": True,
                "historical_data_weight": 0.3,
                "content_quality_weight": 0.4,
                "trend_alignment_weight": 0.3,
                "max_hashtag_suggestions": 10,
                "prediction_horizon_hours": 24
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات التنبؤ: {str(e)}")
            return {
                "min_confidence_threshold": 0.6,
                "viral_score_threshold": 0.7,
                "enable_trend_analysis": True,
                "enable_audience_analysis": True,
                "enable_timing_optimization": True,
                "historical_data_weight": 0.3,
                "content_quality_weight": 0.4,
                "trend_alignment_weight": 0.3,
                "max_hashtag_suggestions": 10,
                "prediction_horizon_hours": 24
            }

    def _load_viral_patterns(self) -> Dict[str, Any]:
        """تحميل أنماط المحتوى الفيروسي"""
        return {
            "emotional_triggers": {
                "humor": {"keywords": ["مضحك", "كوميدي", "ضحك", "مرح"], "weight": 0.8},
                "surprise": {"keywords": ["مفاجأة", "صدمة", "لا يصدق", "مذهل"], "weight": 0.7},
                "inspiration": {"keywords": ["ملهم", "رائع", "نجاح", "تحفيز"], "weight": 0.6},
                "controversy": {"keywords": ["جدل", "خلاف", "رأي", "نقاش"], "weight": 0.5}
            },
            "content_types": {
                "challenges": {"patterns": [r"تحدي", r"challenge"], "multiplier": 1.5},
                "tutorials": {"patterns": [r"طريقة", r"كيف", r"تعلم"], "multiplier": 1.2},
                "reactions": {"patterns": [r"ردة فعل", r"reaction"], "multiplier": 1.3},
                "behind_scenes": {"patterns": [r"خلف الكواليس", r"behind"], "multiplier": 1.1}
            },
            "visual_elements": {
                "fast_cuts": {"threshold": 2.0, "weight": 0.3},
                "bright_colors": {"threshold": 0.7, "weight": 0.2},
                "faces": {"min_count": 1, "weight": 0.4},
                "text_overlays": {"presence": True, "weight": 0.3}
            }
        }

    def _load_trending_keywords(self) -> List[str]:
        """تحميل الكلمات الرائجة"""
        # في التطبيق الحقيقي، هذه ستأتي من API أو قاعدة بيانات محدثة
        return [
            "ترند", "فيروسي", "شائع", "رائج", "مشهور",
            "جديد", "حصري", "عاجل", "مباشر", "لايف",
            "تحدي", "مسابقة", "جائزة", "مفاجأة", "هدية",
            "سر", "خفي", "مكشوف", "حقيقة", "واقع"
        ]

    def _load_audience_preferences(self) -> Dict[str, Any]:
        """تحميل تفضيلات الجمهور"""
        return {
            "age_groups": {
                "13-17": {"preferences": ["ألعاب", "موسيقى", "رقص", "مدرسة"], "peak_hours": [16, 17, 18, 19, 20]},
                "18-24": {"preferences": ["جامعة", "عمل", "علاقات", "سفر"], "peak_hours": [19, 20, 21, 22]},
                "25-34": {"preferences": ["عمل", "أسرة", "صحة", "تطوير"], "peak_hours": [20, 21, 22]},
                "35+": {"preferences": ["أسرة", "صحة", "أخبار", "طبخ"], "peak_hours": [19, 20, 21]}
            },
            "interests": {
                "entertainment": ["كوميديا", "أفلام", "مسلسلات", "ألعاب"],
                "lifestyle": ["موضة", "جمال", "صحة", "رياضة"],
                "education": ["تعلم", "تطوير", "مهارات", "علوم"],
                "technology": ["تقنية", "هواتف", "تطبيقات", "ذكي"]
            }
        }

    def _load_factor_weights(self) -> Dict[str, float]:
        """تحميل أوزان عوامل التنبؤ"""
        return {
            "content_quality": 0.25,
            "emotional_appeal": 0.20,
            "trend_alignment": 0.15,
            "timing": 0.10,
            "audience_match": 0.10,
            "platform_optimization": 0.10,
            "novelty": 0.10
        }

    def predict_virality(self, audio_analysis: Dict[str, Any] = None,
                        video_analysis: Dict[str, Any] = None,
                        content_item: ContentItem = None) -> Dict[str, Any]:
        """التنبؤ بإمكانية الانتشار الفيروسي"""
        try:
            self.logger.info(f"بدء التنبؤ بالانتشار للمحتوى: {content_item.title if content_item else 'غير محدد'}")

            # إنشاء تنبؤ
            prediction = ViralPrediction(content_item) if content_item else ViralPrediction(None)

            # تحليل العوامل المختلفة
            self._analyze_content_quality(prediction, audio_analysis, video_analysis)
            self._analyze_emotional_appeal(prediction, audio_analysis)
            self._analyze_trend_alignment(prediction, audio_analysis, content_item)
            self._analyze_timing_factors(prediction, content_item)
            self._analyze_audience_match(prediction, content_item)
            self._analyze_platform_optimization(prediction, content_item)
            self._analyze_novelty_factor(prediction, content_item)

            # حساب النقاط الإجمالية
            self._calculate_overall_score(prediction)

            # إنشاء التوصيات
            self._generate_recommendations(prediction)

            # تحديد الجمهور المستهدف
            self._identify_target_audience(prediction)

            # اقتراح الهاشتاغات
            self._suggest_hashtags(prediction)

            # تحديد التوقيت الأمثل
            self._optimize_posting_time(prediction)

            result = {
                "score": prediction.overall_score,
                "confidence": prediction.confidence,
                "factors": {name: factor.to_dict() for name, factor in prediction.factors.items()},
                "recommendations": prediction.recommendations,
                "predicted_reach": prediction.predicted_reach,
                "predicted_engagement": prediction.predicted_engagement,
                "optimal_posting_time": prediction.optimal_posting_time.isoformat() if prediction.optimal_posting_time else None,
                "target_audience": prediction.target_audience,
                "hashtag_suggestions": prediction.hashtag_suggestions,
                "risk_factors": prediction.risk_factors,
                "boost_factors": prediction.boost_factors
            }

            self.logger.info(f"انتهى التنبؤ - النقاط: {prediction.overall_score:.2f}")
            return result

        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالانتشار: {str(e)}")
            return {
                "score": 0.0,
                "confidence": 0.0,
                "factors": {},
                "recommendations": ["خطأ في التحليل"],
                "error": str(e)
            }

    def _analyze_content_quality(self, prediction: ViralPrediction,
                               audio_analysis: Dict[str, Any] = None,
                               video_analysis: Dict[str, Any] = None):
        """تحليل جودة المحتوى"""
        try:
            quality_score = 0.0

            # جودة الصوت
            if audio_analysis:
                audio_quality = audio_analysis.get("quality_score", 0.0)
                quality_score += audio_quality * 0.4

            # جودة الفيديو
            if video_analysis:
                video_quality = video_analysis.get("quality_score", 0.0)
                quality_score += video_quality * 0.6

            # إذا لم توجد تحليلات، استخدم قيمة متوسطة
            if not audio_analysis and not video_analysis:
                quality_score = 0.5

            factor = ViralFactor("content_quality", self.factor_weights["content_quality"], quality_score)
            factor.description = f"جودة المحتوى: {quality_score:.2f}"
            prediction.factors["content_quality"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل جودة المحتوى: {str(e)}")

    def _analyze_emotional_appeal(self, prediction: ViralPrediction,
                                audio_analysis: Dict[str, Any] = None):
        """تحليل الجاذبية العاطفية"""
        try:
            emotional_score = 0.0
            emotions_found = []

            if audio_analysis and "transcription" in audio_analysis:
                text = audio_analysis["transcription"].get("text", "")

                # تحليل المحفزات العاطفية
                for emotion, data in self.viral_patterns["emotional_triggers"].items():
                    emotion_count = 0
                    for keyword in data["keywords"]:
                        emotion_count += text.lower().count(keyword)

                    if emotion_count > 0:
                        emotions_found.append(emotion)
                        emotional_score += emotion_count * data["weight"] * 0.1

                # تحليل علامات الإثارة
                excitement_patterns = [r'[!]{2,}', r'[؟]{2,}', r'ه{3,}', r'و{3,}']
                for pattern in excitement_patterns:
                    matches = len(re.findall(pattern, text))
                    emotional_score += matches * 0.05

            emotional_score = min(1.0, emotional_score)

            factor = ViralFactor("emotional_appeal", self.factor_weights["emotional_appeal"], emotional_score)
            factor.description = f"جاذبية عاطفية: {', '.join(emotions_found) if emotions_found else 'محدودة'}"
            prediction.factors["emotional_appeal"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل الجاذبية العاطفية: {str(e)}")

    def _analyze_trend_alignment(self, prediction: ViralPrediction,
                               audio_analysis: Dict[str, Any] = None,
                               content_item: ContentItem = None):
        """تحليل مواءمة الاتجاهات"""
        try:
            trend_score = 0.0
            found_trends = []

            # تحليل النص للكلمات الرائجة
            text_sources = []
            if audio_analysis and "transcription" in audio_analysis:
                text_sources.append(audio_analysis["transcription"].get("text", ""))
            if content_item:
                text_sources.extend([content_item.title, content_item.description])

            combined_text = " ".join(text_sources).lower()

            for keyword in self.trending_keywords:
                if keyword in combined_text:
                    found_trends.append(keyword)
                    trend_score += 0.1

            # تحليل أنواع المحتوى الرائجة
            for content_type, data in self.viral_patterns["content_types"].items():
                for pattern in data["patterns"]:
                    if re.search(pattern, combined_text):
                        trend_score *= data["multiplier"]
                        found_trends.append(content_type)
                        break

            trend_score = min(1.0, trend_score)

            factor = ViralFactor("trend_alignment", self.factor_weights["trend_alignment"], trend_score)
            factor.description = f"مواءمة الاتجاهات: {', '.join(found_trends) if found_trends else 'محدودة'}"
            prediction.factors["trend_alignment"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل مواءمة الاتجاهات: {str(e)}")

    def _analyze_timing_factors(self, prediction: ViralPrediction,
                              content_item: ContentItem = None):
        """تحليل عوامل التوقيت"""
        try:
            timing_score = 0.5  # قيمة افتراضية

            if content_item and content_item.timestamp:
                # تحليل وقت النشر
                post_time = content_item.timestamp
                hour = post_time.hour
                day_of_week = post_time.weekday()

                # أوقات الذروة العامة (7-9 مساءً)
                if 19 <= hour <= 21:
                    timing_score += 0.3
                elif 16 <= hour <= 18:  # بعد الظهر
                    timing_score += 0.2
                elif 12 <= hour <= 14:  # وقت الغداء
                    timing_score += 0.1

                # أيام نهاية الأسبوع
                if day_of_week in [4, 5]:  # الجمعة والسبت
                    timing_score += 0.2

                timing_score = min(1.0, timing_score)

            factor = ViralFactor("timing", self.factor_weights["timing"], timing_score)
            factor.description = f"توقيت النشر: {'مثالي' if timing_score > 0.7 else 'جيد' if timing_score > 0.5 else 'متوسط'}"
            prediction.factors["timing"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل عوامل التوقيت: {str(e)}")

    def _analyze_audience_match(self, prediction: ViralPrediction,
                              content_item: ContentItem = None):
        """تحليل مطابقة الجمهور"""
        try:
            audience_score = 0.5  # قيمة افتراضية

            if content_item:
                # تحليل المنصة
                platform = content_item.platform.lower()

                # TikTok - جمهور أصغر سناً
                if platform == "tiktok":
                    audience_score = 0.8
                # Snapchat - جمهور شاب
                elif platform == "snapchat":
                    audience_score = 0.7
                # Kick - جمهور الألعاب
                elif platform == "kick":
                    audience_score = 0.6

            factor = ViralFactor("audience_match", self.factor_weights["audience_match"], audience_score)
            factor.description = f"مطابقة الجمهور: {audience_score:.2f}"
            prediction.factors["audience_match"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل مطابقة الجمهور: {str(e)}")

    def _analyze_platform_optimization(self, prediction: ViralPrediction,
                                     content_item: ContentItem = None):
        """تحليل تحسين المنصة"""
        try:
            optimization_score = 0.5

            if content_item:
                platform = content_item.platform.lower()

                # عوامل تحسين خاصة بكل منصة
                if platform == "tiktok":
                    # TikTok يفضل الفيديوهات القصيرة العمودية
                    optimization_score = 0.8
                elif platform == "snapchat":
                    # Snapchat يفضل المحتوى التفاعلي
                    optimization_score = 0.7
                elif platform == "kick":
                    # Kick يفضل المحتوى المباشر
                    optimization_score = 0.6

            factor = ViralFactor("platform_optimization", self.factor_weights["platform_optimization"], optimization_score)
            factor.description = f"تحسين المنصة: {optimization_score:.2f}"
            prediction.factors["platform_optimization"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل تحسين المنصة: {str(e)}")

    def _analyze_novelty_factor(self, prediction: ViralPrediction,
                              content_item: ContentItem = None):
        """تحليل عامل الجدة"""
        try:
            novelty_score = 0.5

            if content_item:
                # تحليل حداثة المحتوى
                if content_item.timestamp:
                    time_diff = datetime.now() - content_item.timestamp
                    hours_old = time_diff.total_seconds() / 3600

                    # المحتوى الأحدث يحصل على نقاط أعلى
                    if hours_old < 1:
                        novelty_score = 1.0
                    elif hours_old < 6:
                        novelty_score = 0.8
                    elif hours_old < 24:
                        novelty_score = 0.6
                    else:
                        novelty_score = 0.3

            factor = ViralFactor("novelty", self.factor_weights["novelty"], novelty_score)
            factor.description = f"عامل الجدة: {novelty_score:.2f}"
            prediction.factors["novelty"] = factor

        except Exception as e:
            self.logger.error(f"خطأ في تحليل عامل الجدة: {str(e)}")

    def _calculate_overall_score(self, prediction: ViralPrediction):
        """حساب النقاط الإجمالية"""
        try:
            total_score = 0.0
            total_weight = 0.0

            for factor in prediction.factors.values():
                total_score += factor.contribution
                total_weight += factor.weight

            if total_weight > 0:
                prediction.overall_score = total_score / total_weight
            else:
                prediction.overall_score = 0.0

            # حساب الثقة بناءً على توفر البيانات
            available_factors = len(prediction.factors)
            total_factors = len(self.factor_weights)
            prediction.confidence = available_factors / total_factors

            # تقدير الوصول والتفاعل
            base_reach = 1000  # وصول أساسي
            prediction.predicted_reach = int(base_reach * (1 + prediction.overall_score * 10))
            prediction.predicted_engagement = prediction.overall_score * 0.1  # 10% كحد أقصى

        except Exception as e:
            self.logger.error(f"خطأ في حساب النقاط الإجمالية: {str(e)}")
            prediction.overall_score = 0.0
            prediction.confidence = 0.0

    def _generate_recommendations(self, prediction: ViralPrediction):
        """إنشاء التوصيات"""
        try:
            recommendations = []

            # توصيات بناءً على النقاط
            if prediction.overall_score < 0.3:
                recommendations.append("المحتوى يحتاج تحسين كبير قبل النشر")
                recommendations.append("فكر في إعادة إنتاج المحتوى بطريقة أكثر جاذبية")
            elif prediction.overall_score < 0.6:
                recommendations.append("المحتوى جيد لكن يمكن تحسينه")
                recommendations.append("أضف عناصر أكثر إثارة أو تفاعلية")
            else:
                recommendations.append("المحتوى ممتاز ومرشح للانتشار")
                recommendations.append("انشر في الوقت المناسب لتحقيق أقصى وصول")

            # توصيات خاصة بالعوامل
            for factor_name, factor in prediction.factors.items():
                if factor.score < 0.4:
                    if factor_name == "content_quality":
                        recommendations.append("حسن جودة الصوت والفيديو")
                    elif factor_name == "emotional_appeal":
                        recommendations.append("أضف عناصر عاطفية أكثر جاذبية")
                    elif factor_name == "trend_alignment":
                        recommendations.append("استخدم الكلمات والمواضيع الرائجة")
                    elif factor_name == "timing":
                        recommendations.append("اختر وقت نشر أفضل")

            prediction.recommendations = recommendations

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            prediction.recommendations = ["خطأ في إنشاء التوصيات"]

    def _identify_target_audience(self, prediction: ViralPrediction):
        """تحديد الجمهور المستهدف"""
        try:
            target_audience = []

            # تحليل المحتوى لتحديد الجمهور
            if prediction.content_item:
                platform = prediction.content_item.platform.lower()

                if platform == "tiktok":
                    target_audience.extend(["13-24", "محبي الترفيه", "الجيل الجديد"])
                elif platform == "snapchat":
                    target_audience.extend(["16-28", "الشباب النشط", "محبي التفاعل"])
                elif platform == "kick":
                    target_audience.extend(["18-35", "محبي الألعاب", "المتابعين التقنيين"])

            # تحليل المحتوى العاطفي
            emotional_factor = prediction.factors.get("emotional_appeal")
            if emotional_factor and emotional_factor.score > 0.6:
                target_audience.append("محبي المحتوى العاطفي")

            prediction.target_audience = target_audience

        except Exception as e:
            self.logger.error(f"خطأ في تحديد الجمهور المستهدف: {str(e)}")
            prediction.target_audience = []

    def _suggest_hashtags(self, prediction: ViralPrediction):
        """اقتراح الهاشتاغات"""
        try:
            hashtags = []

            # هاشتاغات عامة رائجة
            general_hashtags = ["#فيروسي", "#ترند", "#شائع", "#مضحك", "#رائع"]
            hashtags.extend(general_hashtags[:3])

            # هاشتاغات خاصة بالمنصة
            if prediction.content_item:
                platform = prediction.content_item.platform.lower()

                if platform == "tiktok":
                    hashtags.extend(["#TikTok", "#fyp", "#viral", "#trending"])
                elif platform == "snapchat":
                    hashtags.extend(["#Snapchat", "#snap", "#story"])
                elif platform == "kick":
                    hashtags.extend(["#Kick", "#gaming", "#live"])

            # هاشتاغات بناءً على المحتوى
            trend_factor = prediction.factors.get("trend_alignment")
            if trend_factor and trend_factor.score > 0.5:
                hashtags.extend(["#جديد", "#حصري", "#عاجل"])

            # تحديد العدد الأقصى
            max_hashtags = self.settings.get("max_hashtag_suggestions", 10)
            prediction.hashtag_suggestions = hashtags[:max_hashtags]

        except Exception as e:
            self.logger.error(f"خطأ في اقتراح الهاشتاغات: {str(e)}")
            prediction.hashtag_suggestions = []

    def _optimize_posting_time(self, prediction: ViralPrediction):
        """تحديد التوقيت الأمثل للنشر"""
        try:
            # أوقات الذروة العامة
            peak_hours = [19, 20, 21]  # 7-9 مساءً

            # تحديد أفضل وقت في الـ 24 ساعة القادمة
            now = datetime.now()

            # البحث عن أقرب وقت ذروة
            for hour in peak_hours:
                optimal_time = now.replace(hour=hour, minute=0, second=0, microsecond=0)

                # إذا كان الوقت قد مضى اليوم، اختر الغد
                if optimal_time <= now:
                    optimal_time = optimal_time.replace(day=optimal_time.day + 1)

                prediction.optimal_posting_time = optimal_time
                break

            # إذا لم يتم تحديد وقت، استخدم الوقت الافتراضي
            if not prediction.optimal_posting_time:
                prediction.optimal_posting_time = now.replace(hour=20, minute=0, second=0, microsecond=0)
                if prediction.optimal_posting_time <= now:
                    prediction.optimal_posting_time = prediction.optimal_posting_time.replace(day=prediction.optimal_posting_time.day + 1)

        except Exception as e:
            self.logger.error(f"خطأ في تحديد التوقيت الأمثل: {str(e)}")
            prediction.optimal_posting_time = datetime.now()

    def get_viral_insights(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """الحصول على رؤى الانتشار من عدة تنبؤات"""
        try:
            if not predictions:
                return {}

            # حساب المتوسطات
            avg_score = sum(p.get("score", 0) for p in predictions) / len(predictions)
            avg_confidence = sum(p.get("confidence", 0) for p in predictions) / len(predictions)

            # تحليل العوامل الأكثر تأثيراً
            factor_scores = {}
            for prediction in predictions:
                factors = prediction.get("factors", {})
                for factor_name, factor_data in factors.items():
                    if factor_name not in factor_scores:
                        factor_scores[factor_name] = []
                    factor_scores[factor_name].append(factor_data.get("score", 0))

            # حساب متوسط كل عامل
            avg_factors = {}
            for factor_name, scores in factor_scores.items():
                avg_factors[factor_name] = sum(scores) / len(scores)

            # ترتيب العوامل حسب التأثير
            sorted_factors = sorted(avg_factors.items(), key=lambda x: x[1], reverse=True)

            # تحديد أفضل الأوقات للنشر
            posting_times = [p.get("optimal_posting_time") for p in predictions if p.get("optimal_posting_time")]
            most_common_hour = 20  # افتراضي

            if posting_times:
                hours = [datetime.fromisoformat(t).hour for t in posting_times if t]
                if hours:
                    most_common_hour = max(set(hours), key=hours.count)

            return {
                "average_viral_score": avg_score,
                "average_confidence": avg_confidence,
                "top_factors": sorted_factors[:5],
                "recommended_posting_hour": most_common_hour,
                "total_predictions": len(predictions),
                "high_potential_count": len([p for p in predictions if p.get("score", 0) > 0.7])
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على رؤى الانتشار: {str(e)}")
            return {}

    def update_trending_data(self, new_keywords: List[str], new_patterns: Dict[str, Any]):
        """تحديث بيانات الاتجاهات"""
        try:
            # تحديث الكلمات الرائجة
            self.trending_keywords.extend(new_keywords)
            # الاحتفاظ بأحدث 100 كلمة فقط
            self.trending_keywords = self.trending_keywords[-100:]

            # تحديث الأنماط
            if new_patterns:
                self.viral_patterns.update(new_patterns)

            # حفظ التحديثات
            self.config_manager.set_setting("viral_data", "trending_keywords", self.trending_keywords)
            self.config_manager.set_setting("viral_data", "patterns", self.viral_patterns)

            self.logger.info("تم تحديث بيانات الاتجاهات")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث بيانات الاتجاهات: {str(e)}")

    def get_prediction_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التنبؤ"""
        try:
            # في التطبيق الحقيقي، هذه ستأتي من قاعدة البيانات
            return {
                "total_predictions": 0,
                "successful_predictions": 0,
                "average_accuracy": 0.0,
                "most_viral_factor": "content_quality",
                "trending_keywords_count": len(self.trending_keywords),
                "last_update": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات التنبؤ: {str(e)}")
            return {}