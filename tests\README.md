# دليل الاختبارات - Testing Guide

## نظرة عامة
هذا المجلد يحتوي على جميع اختبارات التطبيق الذكي لإنشاء المحتوى.

## بنية الاختبارات

### اختبارات الوحدة (Unit Tests)
- `test_content_fetcher.py` - اختبارات نظام جلب المحتوى
- `test_ai_analysis.py` - اختبارات نظام الذكاء الاصطناعي
- `test_automated_editing.py` - اختبارات نظام المونتاج التلقائي
- `test_automated_publishing.py` - اختبارات نظام النشر التلقائي

### اختبارات الأمان (Security Tests)
- `test_security_systems.py` - اختبارات شاملة لجميع أنظمة الأمان
  - اختبارات التشفير المتقدم
  - اختبارات المصادقة المتقدمة
  - اختبارات الحماية من البرمجيات الخبيثة
  - اختبارات النسخ الاحتياطي الآمن
  - اختبارات الحماية من التلاعب

### اختبارات واجهة المستخدم (GUI Tests)
- `test_gui.py` - اختبارات واجهة المستخدم الرسومية
  - اختبارات النافذة الرئيسية
  - اختبارات ودجت إدارة المحتوى
  - اختبارات حوار الإعدادات
  - اختبارات ودجت المراقبة
  - اختبارات ودجت الأمان

### اختبارات الأداء (Performance Tests)
- `test_performance.py` - اختبارات الأداء والتحسين
  - اختبارات أداء جلب المحتوى
  - اختبارات أداء الذكاء الاصطناعي
  - اختبارات أداء المونتاج
  - اختبارات أداء التشفير
  - اختبارات استخدام موارد النظام

## ملفات الدعم
- `conftest.py` - إعدادات pytest والـ fixtures المشتركة
- `run_tests.py` - مشغل الاختبارات الرئيسي
- `test_simple.py` - اختبارات بسيطة للتحقق من عمل النظام

## كيفية تشغيل الاختبارات

### المتطلبات
```bash
pip install pytest pytest-cov pytest-mock psutil numpy
```

### تشغيل جميع الاختبارات
```bash
python tests/run_tests.py --all
```

### تشغيل اختبارات محددة
```bash
# اختبارات الوحدة فقط
python tests/run_tests.py --unit

# اختبارات الأمان فقط
python tests/run_tests.py --security

# اختبارات واجهة المستخدم فقط
python tests/run_tests.py --gui

# اختبارات الأداء فقط
python tests/run_tests.py --performance

# اختبارات التكامل فقط
python tests/run_tests.py --integration

# تحليل التغطية
python tests/run_tests.py --coverage
```

### تشغيل اختبارات باستخدام pytest مباشرة
```bash
# تشغيل جميع الاختبارات
pytest tests/ -v

# تشغيل اختبارات ملف محدد
pytest tests/test_security_systems.py -v

# تشغيل اختبار محدد
pytest tests/test_security_systems.py::TestAdvancedEncryption::test_initialization -v

# تشغيل اختبارات بعلامة محددة
pytest tests/ -m security -v
pytest tests/ -m performance -v
pytest tests/ -m gui -v
```

### تحليل التغطية
```bash
# تشغيل مع تحليل التغطية
pytest tests/ --cov=src --cov-report=html --cov-report=term

# عرض تقرير التغطية
coverage report --show-missing
```

## علامات الاختبارات (Test Markers)
- `@pytest.mark.unit` - اختبارات الوحدة
- `@pytest.mark.security` - اختبارات الأمان
- `@pytest.mark.gui` - اختبارات واجهة المستخدم
- `@pytest.mark.performance` - اختبارات الأداء
- `@pytest.mark.integration` - اختبارات التكامل

## بنية التقارير
- `test_report.txt` - تقرير شامل للاختبارات
- `htmlcov/` - تقرير التغطية بصيغة HTML
- `coverage.xml` - تقرير التغطية بصيغة XML

## نصائح للتطوير
1. قم بتشغيل الاختبارات قبل كل commit
2. تأكد من أن التغطية أعلى من 80%
3. اكتب اختبارات للميزات الجديدة
4. استخدم mock للاعتماديات الخارجية
5. اختبر الحالات الاستثنائية

## استكشاف الأخطاء
- تأكد من تثبيت جميع المتطلبات
- تحقق من مسار Python والمكتبات
- استخدم `-v` للحصول على تفاصيل أكثر
- راجع ملفات السجل للأخطاء

## الدعم
للحصول على المساعدة، راجع:
- ملفات السجل في مجلد `logs/`
- تقارير الاختبارات في `test_report.txt`
- وثائق pytest: https://docs.pytest.org/
