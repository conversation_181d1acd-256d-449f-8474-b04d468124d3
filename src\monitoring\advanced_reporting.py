#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير والإحصائيات المتقدم - Advanced Reporting System
يجمع ويحلل البيانات من جميع أنظمة التطبيق لإنتاج تقارير شاملة
"""

import logging
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import seaborn as sns

# تعيين الخط العربي
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class ReportSection:
    """قسم في التقرير"""
    title: str
    content: Dict[str, Any]
    charts: List[str] = None  # مسارات الرسوم البيانية
    tables: List[Dict[str, Any]] = None
    summary: str = ""
    
    def __post_init__(self):
        if self.charts is None:
            self.charts = []
        if self.tables is None:
            self.tables = []

@dataclass
class AdvancedReport:
    """تقرير متقدم"""
    report_id: str
    title: str
    description: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    sections: List[ReportSection]
    summary_stats: Dict[str, Any]
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []

class AdvancedReportingSystem:
    """نظام التقارير والإحصائيات المتقدم"""
    
    def __init__(self, config_manager, performance_monitor=None, data_validator=None, 
                 notification_system=None, publishing_workflow=None):
        self.config_manager = config_manager
        self.performance_monitor = performance_monitor
        self.data_validator = data_validator
        self.notification_system = notification_system
        self.publishing_workflow = publishing_workflow
        self.logger = logging.getLogger(__name__)
        
        # إعدادات التقارير
        self.reporting_settings = self._load_reporting_settings()
        
        # مجلدات البيانات والتقارير
        self.data_dir = Path.home() / ".smart_content_app" / "reports"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.charts_dir = self.data_dir / "charts"
        self.charts_dir.mkdir(exist_ok=True)
        self.reports_dir = self.data_dir / "generated_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # ملفات البيانات
        self.reports_index_file = self.data_dir / "reports_index.json"
        self.analytics_cache_file = self.data_dir / "analytics_cache.json"
        
        # كاش التحليلات
        self.analytics_cache = {}
        self.cache_expiry = {}
        
        # إحصائيات النظام
        self.system_stats = {
            "total_reports_generated": 0,
            "last_report_generated": None,
            "average_generation_time": 0.0,
            "most_requested_report_type": "",
            "data_sources_connected": 0
        }
        
        # تحميل البيانات المحفوظة
        self._load_saved_data()
        
        # تحديد مصادر البيانات المتصلة
        self._update_connected_sources()
        
        self.logger.info("تم تهيئة نظام التقارير والإحصائيات المتقدم")
    
    def _load_reporting_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات التقارير"""
        try:
            return self.config_manager.get_setting("reporting", "settings", {
                "default_report_period_days": 7,
                "auto_generate_daily_reports": True,
                "auto_generate_weekly_reports": True,
                "auto_generate_monthly_reports": True,
                "include_charts": True,
                "include_recommendations": True,
                "chart_style": "seaborn",
                "chart_dpi": 300,
                "max_cached_analytics": 50,
                "cache_expiry_hours": 6,
                "export_formats": ["pdf", "html", "json"],
                "email_reports": False,
                "report_recipients": []
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات التقارير: {str(e)}")
            return {}
    
    def _load_saved_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل كاش التحليلات
            if self.analytics_cache_file.exists():
                with open(self.analytics_cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    self.analytics_cache = cache_data.get("analytics", {})
                    
                    # تحويل تواريخ انتهاء الصلاحية
                    expiry_data = cache_data.get("expiry", {})
                    for key, expiry_str in expiry_data.items():
                        self.cache_expiry[key] = datetime.fromisoformat(expiry_str)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")
    
    def _update_connected_sources(self):
        """تحديث عدد مصادر البيانات المتصلة"""
        connected = 0
        if self.performance_monitor:
            connected += 1
        if self.data_validator:
            connected += 1
        if self.notification_system:
            connected += 1
        if self.publishing_workflow:
            connected += 1
        
        self.system_stats["data_sources_connected"] = connected
    
    def generate_comprehensive_report(self, period_days: int = None, 
                                    include_sections: List[str] = None) -> AdvancedReport:
        """إنتاج تقرير شامل"""
        try:
            start_time = datetime.now()
            
            # تحديد فترة التقرير
            if period_days is None:
                period_days = self.reporting_settings.get("default_report_period_days", 7)
            
            period_end = datetime.now()
            period_start = period_end - timedelta(days=period_days)
            
            # تحديد الأقسام المطلوبة
            if include_sections is None:
                include_sections = [
                    "system_overview",
                    "performance_analysis", 
                    "content_analysis",
                    "publishing_analysis",
                    "validation_analysis",
                    "notifications_analysis",
                    "trends_analysis",
                    "recommendations"
                ]
            
            # إنشاء معرف التقرير
            report_id = f"comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # جمع البيانات وإنشاء الأقسام
            sections = []
            summary_stats = {}
            
            # قسم نظرة عامة على النظام
            if "system_overview" in include_sections:
                overview_section = self._generate_system_overview_section(period_start, period_end)
                sections.append(overview_section)
                summary_stats.update(overview_section.content.get("summary_stats", {}))
            
            # قسم تحليل الأداء
            if "performance_analysis" in include_sections and self.performance_monitor:
                performance_section = self._generate_performance_analysis_section(period_start, period_end)
                sections.append(performance_section)
                summary_stats.update(performance_section.content.get("summary_stats", {}))
            
            # قسم تحليل المحتوى
            if "content_analysis" in include_sections:
                content_section = self._generate_content_analysis_section(period_start, period_end)
                sections.append(content_section)
                summary_stats.update(content_section.content.get("summary_stats", {}))
            
            # قسم تحليل النشر
            if "publishing_analysis" in include_sections and self.publishing_workflow:
                publishing_section = self._generate_publishing_analysis_section(period_start, period_end)
                sections.append(publishing_section)
                summary_stats.update(publishing_section.content.get("summary_stats", {}))
            
            # قسم تحليل التحقق
            if "validation_analysis" in include_sections and self.data_validator:
                validation_section = self._generate_validation_analysis_section(period_start, period_end)
                sections.append(validation_section)
                summary_stats.update(validation_section.content.get("summary_stats", {}))
            
            # قسم تحليل الإشعارات
            if "notifications_analysis" in include_sections and self.notification_system:
                notifications_section = self._generate_notifications_analysis_section(period_start, period_end)
                sections.append(notifications_section)
                summary_stats.update(notifications_section.content.get("summary_stats", {}))
            
            # قسم تحليل الاتجاهات
            if "trends_analysis" in include_sections:
                trends_section = self._generate_trends_analysis_section(period_start, period_end)
                sections.append(trends_section)
                summary_stats.update(trends_section.content.get("summary_stats", {}))
            
            # إنشاء التقرير
            report = AdvancedReport(
                report_id=report_id,
                title=f"تقرير شامل - {period_days} أيام",
                description=f"تقرير شامل لأداء النظام خلال الفترة من {period_start.strftime('%Y-%m-%d')} إلى {period_end.strftime('%Y-%m-%d')}",
                generated_at=datetime.now(),
                period_start=period_start,
                period_end=period_end,
                sections=sections,
                summary_stats=summary_stats
            )
            
            # إضافة التوصيات
            if "recommendations" in include_sections:
                report.recommendations = self._generate_recommendations(summary_stats, sections)
            
            # حفظ التقرير
            self._save_report(report)
            
            # تحديث الإحصائيات
            generation_time = (datetime.now() - start_time).total_seconds()
            self._update_system_stats(generation_time, "comprehensive")
            
            self.logger.info(f"تم إنتاج التقرير الشامل: {report_id}")
            return report
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج التقرير الشامل: {str(e)}")
            raise
    
    def _generate_system_overview_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم نظرة عامة على النظام"""
        try:
            content = {
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "duration_days": (end_date - start_date).days
                },
                "system_info": {
                    "data_sources_connected": self.system_stats["data_sources_connected"],
                    "total_reports_generated": self.system_stats["total_reports_generated"],
                    "last_report_generated": self.system_stats["last_report_generated"]
                },
                "summary_stats": {
                    "system_uptime_days": (end_date - start_date).days,
                    "data_sources_active": self.system_stats["data_sources_connected"]
                }
            }
            
            # إنشاء رسم بياني للنظرة العامة
            charts = []
            if self.reporting_settings.get("include_charts", True):
                chart_path = self._create_system_overview_chart(content)
                if chart_path:
                    charts.append(chart_path)
            
            return ReportSection(
                title="نظرة عامة على النظام",
                content=content,
                charts=charts,
                summary=f"النظام نشط لمدة {(end_date - start_date).days} أيام مع {self.system_stats['data_sources_connected']} مصادر بيانات متصلة"
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم نظرة عامة النظام: {str(e)}")
            return ReportSection("نظرة عامة على النظام", {"error": str(e)})
    
    def _create_system_overview_chart(self, content: Dict[str, Any]) -> Optional[str]:
        """إنشاء رسم بياني لنظرة عامة النظام"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle('نظرة عامة على النظام', fontsize=16, fontweight='bold')
            
            # رسم بياني لمصادر البيانات
            sources = ['مراقب الأداء', 'مدقق البيانات', 'نظام الإشعارات', 'سير النشر']
            connected = [1 if self.performance_monitor else 0,
                        1 if self.data_validator else 0,
                        1 if self.notification_system else 0,
                        1 if self.publishing_workflow else 0]
            
            colors = ['green' if c else 'red' for c in connected]
            ax1.bar(sources, connected, color=colors)
            ax1.set_title('حالة مصادر البيانات')
            ax1.set_ylabel('متصل (1) / غير متصل (0)')
            ax1.tick_params(axis='x', rotation=45)
            
            # رسم بياني دائري لتوزيع التقارير
            report_types = ['شامل', 'أداء', 'محتوى', 'نشر']
            report_counts = [10, 15, 8, 12]  # أرقام تجريبية
            ax2.pie(report_counts, labels=report_types, autopct='%1.1f%%')
            ax2.set_title('توزيع أنواع التقارير')
            
            # رسم بياني لمدة التشغيل
            days = content["period"]["duration_days"]
            ax3.bar(['مدة التقرير'], [days], color='blue')
            ax3.set_title('مدة فترة التقرير (أيام)')
            ax3.set_ylabel('الأيام')
            
            # رسم بياني للإحصائيات العامة
            stats_labels = ['التقارير المنتجة', 'مصادر البيانات']
            stats_values = [self.system_stats["total_reports_generated"], 
                           self.system_stats["data_sources_connected"]]
            ax4.bar(stats_labels, stats_values, color=['orange', 'purple'])
            ax4.set_title('إحصائيات عامة')
            ax4.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            
            # حفظ الرسم البياني
            chart_path = self.charts_dir / f"system_overview_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=self.reporting_settings.get("chart_dpi", 300), bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسم بياني نظرة عامة النظام: {str(e)}")
            return None

    def _generate_performance_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل الأداء"""
        try:
            if not self.performance_monitor:
                return ReportSection("تحليل الأداء", {"error": "مراقب الأداء غير متصل"})

            # جمع بيانات الأداء
            performance_stats = self.performance_monitor.get_performance_stats()
            recent_metrics = self.performance_monitor.get_recent_metrics(limit=1000)

            # تحليل البيانات
            content = {
                "performance_summary": performance_stats,
                "metrics_analysis": self._analyze_performance_metrics(recent_metrics, start_date, end_date),
                "alerts_summary": self._analyze_performance_alerts(start_date, end_date),
                "summary_stats": {
                    "avg_cpu_usage": performance_stats.get("average_cpu_usage", 0),
                    "avg_memory_usage": performance_stats.get("average_memory_usage", 0),
                    "total_operations": performance_stats.get("total_operations", 0),
                    "success_rate": performance_stats.get("success_rate", 0)
                }
            }

            # إنشاء الرسوم البيانية
            charts = []
            if self.reporting_settings.get("include_charts", True):
                chart_path = self._create_performance_charts(recent_metrics, start_date, end_date)
                if chart_path:
                    charts.append(chart_path)

            # إنشاء الجداول
            tables = [
                {
                    "title": "ملخص الأداء",
                    "data": performance_stats
                }
            ]

            return ReportSection(
                title="تحليل الأداء",
                content=content,
                charts=charts,
                tables=tables,
                summary=f"متوسط استخدام المعالج: {performance_stats.get('average_cpu_usage', 0):.1f}%، معدل النجاح: {performance_stats.get('success_rate', 0):.1f}%"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل الأداء: {str(e)}")
            return ReportSection("تحليل الأداء", {"error": str(e)})

    def _analyze_performance_metrics(self, metrics: List[Dict], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل مقاييس الأداء"""
        try:
            if not metrics:
                return {"error": "لا توجد مقاييس أداء متاحة"}

            # تصفية المقاييس حسب الفترة الزمنية
            filtered_metrics = []
            for metric in metrics:
                metric_time = datetime.fromisoformat(metric["timestamp"])
                if start_date <= metric_time <= end_date:
                    filtered_metrics.append(metric)

            if not filtered_metrics:
                return {"error": "لا توجد مقاييس أداء في الفترة المحددة"}

            # حساب الإحصائيات
            cpu_values = [m["cpu_percent"] for m in filtered_metrics]
            memory_values = [m["memory_percent"] for m in filtered_metrics]
            response_times = [m.get("response_time_ms", 0) for m in filtered_metrics]

            analysis = {
                "total_metrics": len(filtered_metrics),
                "cpu_analysis": {
                    "average": np.mean(cpu_values),
                    "max": np.max(cpu_values),
                    "min": np.min(cpu_values),
                    "std": np.std(cpu_values)
                },
                "memory_analysis": {
                    "average": np.mean(memory_values),
                    "max": np.max(memory_values),
                    "min": np.min(memory_values),
                    "std": np.std(memory_values)
                },
                "response_time_analysis": {
                    "average": np.mean(response_times),
                    "max": np.max(response_times),
                    "min": np.min(response_times),
                    "percentile_95": np.percentile(response_times, 95)
                },
                "operation_types": Counter([m.get("operation_type", "unknown") for m in filtered_metrics]),
                "success_rate": (sum(1 for m in filtered_metrics if m.get("success", True)) / len(filtered_metrics)) * 100
            }

            return analysis

        except Exception as e:
            self.logger.error(f"خطأ في تحليل مقاييس الأداء: {str(e)}")
            return {"error": str(e)}

    def _analyze_performance_alerts(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل تنبيهات الأداء"""
        try:
            if not self.performance_monitor:
                return {"error": "مراقب الأداء غير متصل"}

            alerts = self.performance_monitor.get_recent_alerts(limit=1000)

            # تصفية التنبيهات حسب الفترة
            filtered_alerts = []
            for alert in alerts:
                alert_time = datetime.fromisoformat(alert["timestamp"])
                if start_date <= alert_time <= end_date:
                    filtered_alerts.append(alert)

            if not filtered_alerts:
                return {"total_alerts": 0, "alerts_by_severity": {}, "alerts_by_category": {}}

            analysis = {
                "total_alerts": len(filtered_alerts),
                "alerts_by_severity": Counter([a["severity"] for a in filtered_alerts]),
                "alerts_by_category": Counter([a["category"] for a in filtered_alerts]),
                "resolved_alerts": sum(1 for a in filtered_alerts if a.get("resolved", False)),
                "unresolved_alerts": sum(1 for a in filtered_alerts if not a.get("resolved", False))
            }

            analysis["resolution_rate"] = (analysis["resolved_alerts"] / analysis["total_alerts"]) * 100 if analysis["total_alerts"] > 0 else 0

            return analysis

        except Exception as e:
            self.logger.error(f"خطأ في تحليل تنبيهات الأداء: {str(e)}")
            return {"error": str(e)}

    def _create_performance_charts(self, metrics: List[Dict], start_date: datetime, end_date: datetime) -> Optional[str]:
        """إنشاء رسوم بيانية للأداء"""
        try:
            # تصفية المقاييس حسب الفترة
            filtered_metrics = []
            for metric in metrics:
                metric_time = datetime.fromisoformat(metric["timestamp"])
                if start_date <= metric_time <= end_date:
                    filtered_metrics.append(metric)

            if not filtered_metrics:
                return None

            # تحضير البيانات
            timestamps = [datetime.fromisoformat(m["timestamp"]) for m in filtered_metrics]
            cpu_values = [m["cpu_percent"] for m in filtered_metrics]
            memory_values = [m["memory_percent"] for m in filtered_metrics]
            response_times = [m.get("response_time_ms", 0) for m in filtered_metrics]

            # إنشاء الرسوم البيانية
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('تحليل الأداء', fontsize=16, fontweight='bold')

            # رسم بياني لاستخدام المعالج
            ax1.plot(timestamps, cpu_values, color='blue', linewidth=2)
            ax1.set_title('استخدام المعالج (%)')
            ax1.set_ylabel('النسبة المئوية')
            ax1.grid(True, alpha=0.3)
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

            # رسم بياني لاستخدام الذاكرة
            ax2.plot(timestamps, memory_values, color='red', linewidth=2)
            ax2.set_title('استخدام الذاكرة (%)')
            ax2.set_ylabel('النسبة المئوية')
            ax2.grid(True, alpha=0.3)
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

            # رسم بياني لأوقات الاستجابة
            ax3.plot(timestamps, response_times, color='green', linewidth=2)
            ax3.set_title('أوقات الاستجابة (مللي ثانية)')
            ax3.set_ylabel('الوقت (ms)')
            ax3.grid(True, alpha=0.3)
            ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

            # رسم بياني دائري لأنواع العمليات
            operation_types = Counter([m.get("operation_type", "unknown") for m in filtered_metrics])
            if operation_types:
                ax4.pie(operation_types.values(), labels=operation_types.keys(), autopct='%1.1f%%')
                ax4.set_title('توزيع أنواع العمليات')

            plt.tight_layout()

            # حفظ الرسم البياني
            chart_path = self.charts_dir / f"performance_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=self.reporting_settings.get("chart_dpi", 300), bbox_inches='tight')
            plt.close()

            return str(chart_path)

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسوم بيانية الأداء: {str(e)}")
            return None

    def _generate_content_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل المحتوى"""
        try:
            # جمع بيانات المحتوى من مصادر مختلفة
            content_data = self._collect_content_data(start_date, end_date)

            content = {
                "content_summary": content_data.get("summary", {}),
                "platform_breakdown": content_data.get("platforms", {}),
                "content_types": content_data.get("types", {}),
                "quality_metrics": content_data.get("quality", {}),
                "summary_stats": {
                    "total_content_processed": content_data.get("summary", {}).get("total_processed", 0),
                    "avg_quality_score": content_data.get("quality", {}).get("average_score", 0),
                    "most_active_platform": content_data.get("platforms", {}).get("most_active", "غير محدد")
                }
            }

            # إنشاء الرسوم البيانية
            charts = []
            if self.reporting_settings.get("include_charts", True):
                chart_path = self._create_content_charts(content_data)
                if chart_path:
                    charts.append(chart_path)

            return ReportSection(
                title="تحليل المحتوى",
                content=content,
                charts=charts,
                summary=f"تم معالجة {content_data.get('summary', {}).get('total_processed', 0)} محتوى بمتوسط جودة {content_data.get('quality', {}).get('average_score', 0):.1f}"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل المحتوى: {str(e)}")
            return ReportSection("تحليل المحتوى", {"error": str(e)})

    def _collect_content_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """جمع بيانات المحتوى"""
        try:
            # بيانات تجريبية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
            content_data = {
                "summary": {
                    "total_processed": 150,
                    "total_downloaded": 120,
                    "total_published": 80,
                    "processing_success_rate": 80.0
                },
                "platforms": {
                    "snapchat": {"count": 60, "success_rate": 85.0},
                    "tiktok": {"count": 50, "success_rate": 90.0},
                    "kick": {"count": 40, "success_rate": 75.0},
                    "most_active": "snapchat"
                },
                "types": {
                    "stories": 80,
                    "lives": 45,
                    "posts": 25
                },
                "quality": {
                    "average_score": 7.5,
                    "high_quality_count": 60,
                    "medium_quality_count": 70,
                    "low_quality_count": 20
                }
            }

            return content_data

        except Exception as e:
            self.logger.error(f"خطأ في جمع بيانات المحتوى: {str(e)}")
            return {}

    def _create_content_charts(self, content_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء رسوم بيانية للمحتوى"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('تحليل المحتوى', fontsize=16, fontweight='bold')

            # رسم بياني للمنصات
            platforms = content_data.get("platforms", {})
            platform_names = [k for k in platforms.keys() if k != "most_active"]
            platform_counts = [platforms[k]["count"] for k in platform_names]

            ax1.bar(platform_names, platform_counts, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
            ax1.set_title('توزيع المحتوى حسب المنصة')
            ax1.set_ylabel('عدد المحتويات')

            # رسم بياني دائري لأنواع المحتوى
            content_types = content_data.get("types", {})
            ax2.pie(content_types.values(), labels=content_types.keys(), autopct='%1.1f%%')
            ax2.set_title('توزيع أنواع المحتوى')

            # رسم بياني لجودة المحتوى
            quality = content_data.get("quality", {})
            quality_labels = ['عالية الجودة', 'متوسطة الجودة', 'منخفضة الجودة']
            quality_counts = [quality.get("high_quality_count", 0),
                            quality.get("medium_quality_count", 0),
                            quality.get("low_quality_count", 0)]
            colors = ['green', 'orange', 'red']

            ax3.bar(quality_labels, quality_counts, color=colors)
            ax3.set_title('توزيع جودة المحتوى')
            ax3.set_ylabel('عدد المحتويات')
            ax3.tick_params(axis='x', rotation=45)

            # رسم بياني لمعدل النجاح
            summary = content_data.get("summary", {})
            success_metrics = ['تم المعالجة', 'تم التحميل', 'تم النشر']
            success_counts = [summary.get("total_processed", 0),
                            summary.get("total_downloaded", 0),
                            summary.get("total_published", 0)]

            ax4.bar(success_metrics, success_counts, color=['blue', 'green', 'purple'])
            ax4.set_title('إحصائيات المعالجة')
            ax4.set_ylabel('العدد')
            ax4.tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # حفظ الرسم البياني
            chart_path = self.charts_dir / f"content_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=self.reporting_settings.get("chart_dpi", 300), bbox_inches='tight')
            plt.close()

            return str(chart_path)

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسوم بيانية المحتوى: {str(e)}")
            return None

    def _generate_publishing_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل النشر"""
        try:
            if not self.publishing_workflow:
                return ReportSection("تحليل النشر", {"error": "سير النشر غير متصل"})

            # جمع بيانات النشر
            publishing_stats = self.publishing_workflow.get_workflow_statistics()

            content = {
                "publishing_summary": publishing_stats,
                "engagement_analysis": self._analyze_engagement_data(start_date, end_date),
                "hashtag_performance": self._analyze_hashtag_performance(start_date, end_date),
                "optimal_timing": self._analyze_optimal_posting_times(start_date, end_date),
                "summary_stats": {
                    "total_published": publishing_stats.get("successful_publications", 0),
                    "success_rate": publishing_stats.get("success_rate", 0),
                    "avg_engagement": publishing_stats.get("average_engagement", 0)
                }
            }

            # إنشاء الرسوم البيانية
            charts = []
            if self.reporting_settings.get("include_charts", True):
                chart_path = self._create_publishing_charts(publishing_stats, start_date, end_date)
                if chart_path:
                    charts.append(chart_path)

            return ReportSection(
                title="تحليل النشر",
                content=content,
                charts=charts,
                summary=f"تم نشر {publishing_stats.get('successful_publications', 0)} محتوى بمعدل نجاح {publishing_stats.get('success_rate', 0):.1f}%"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل النشر: {str(e)}")
            return ReportSection("تحليل النشر", {"error": str(e)})

    def _analyze_engagement_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل بيانات التفاعل"""
        try:
            # بيانات تجريبية - في التطبيق الحقيقي ستأتي من APIs المنصات
            engagement_data = {
                "total_views": 50000,
                "total_likes": 3500,
                "total_comments": 450,
                "total_shares": 280,
                "engagement_rate": 8.5,
                "top_performing_content": [
                    {"title": "فيديو مضحك #1", "views": 5000, "likes": 400},
                    {"title": "فيديو ترند #2", "views": 4500, "likes": 350},
                    {"title": "محتوى فيرال #3", "views": 4000, "likes": 320}
                ]
            }

            return engagement_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل بيانات التفاعل: {str(e)}")
            return {}

    def _analyze_hashtag_performance(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل أداء الهاشتاغات"""
        try:
            # بيانات تجريبية
            hashtag_data = {
                "top_hashtags": [
                    {"hashtag": "#ترند", "usage_count": 25, "avg_engagement": 8.2},
                    {"hashtag": "#مضحك", "usage_count": 20, "avg_engagement": 7.8},
                    {"hashtag": "#فيرال", "usage_count": 18, "avg_engagement": 9.1}
                ],
                "hashtag_trends": {
                    "rising": ["#جديد", "#2024"],
                    "declining": ["#قديم", "#2023"]
                }
            }

            return hashtag_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل أداء الهاشتاغات: {str(e)}")
            return {}

    def _analyze_optimal_posting_times(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل أوقات النشر المثلى"""
        try:
            # بيانات تجريبية
            timing_data = {
                "best_hours": [20, 21, 22],  # 8-10 مساءً
                "best_days": ["الخميس", "الجمعة", "السبت"],
                "engagement_by_hour": {
                    str(hour): np.random.uniform(5, 10) for hour in range(24)
                },
                "recommendations": [
                    "أفضل وقت للنشر: 8-10 مساءً",
                    "أفضل أيام: نهاية الأسبوع",
                    "تجنب النشر في ساعات الصباح الباكر"
                ]
            }

            return timing_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل أوقات النشر المثلى: {str(e)}")
            return {}

    def _create_publishing_charts(self, publishing_stats: Dict[str, Any], start_date: datetime, end_date: datetime) -> Optional[str]:
        """إنشاء رسوم بيانية للنشر"""
        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('تحليل النشر', fontsize=16, fontweight='bold')

            # رسم بياني لإحصائيات النشر
            publish_labels = ['نجح', 'فشل', 'قيد الانتظار']
            publish_counts = [
                publishing_stats.get("successful_publications", 0),
                publishing_stats.get("failed_publications", 0),
                publishing_stats.get("pending_publications", 0)
            ]
            colors = ['green', 'red', 'orange']

            ax1.pie(publish_counts, labels=publish_labels, colors=colors, autopct='%1.1f%%')
            ax1.set_title('حالة المنشورات')

            # رسم بياني للتفاعل حسب الساعة
            hours = list(range(24))
            engagement_by_hour = [np.random.uniform(5, 10) for _ in hours]  # بيانات تجريبية

            ax2.plot(hours, engagement_by_hour, marker='o', linewidth=2, color='blue')
            ax2.set_title('التفاعل حسب ساعة النشر')
            ax2.set_xlabel('الساعة')
            ax2.set_ylabel('معدل التفاعل (%)')
            ax2.grid(True, alpha=0.3)

            # رسم بياني لأداء الهاشتاغات
            hashtags = ['#ترند', '#مضحك', '#فيرال', '#جديد', '#محتوى']
            hashtag_performance = [9.1, 8.2, 7.8, 7.5, 6.9]  # بيانات تجريبية

            ax3.bar(hashtags, hashtag_performance, color='purple')
            ax3.set_title('أداء أفضل الهاشتاغات')
            ax3.set_ylabel('معدل التفاعل (%)')
            ax3.tick_params(axis='x', rotation=45)

            # رسم بياني للنشر حسب اليوم
            days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
            posts_per_day = [8, 12, 10, 15, 18, 20, 16]  # بيانات تجريبية

            ax4.bar(days, posts_per_day, color='orange')
            ax4.set_title('عدد المنشورات حسب اليوم')
            ax4.set_ylabel('عدد المنشورات')
            ax4.tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # حفظ الرسم البياني
            chart_path = self.charts_dir / f"publishing_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_path, dpi=self.reporting_settings.get("chart_dpi", 300), bbox_inches='tight')
            plt.close()

            return str(chart_path)

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسوم بيانية النشر: {str(e)}")
            return None

    def _generate_validation_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل التحقق"""
        try:
            if not self.data_validator:
                return ReportSection("تحليل التحقق", {"error": "مدقق البيانات غير متصل"})

            # جمع بيانات التحقق
            validation_stats = self.data_validator.get_validation_statistics()

            content = {
                "validation_summary": validation_stats,
                "error_analysis": self._analyze_validation_errors(start_date, end_date),
                "correction_analysis": self._analyze_auto_corrections(start_date, end_date),
                "summary_stats": {
                    "total_validations": validation_stats.get("total_validations", 0),
                    "success_rate": validation_stats.get("success_rate", 0),
                    "auto_corrections": validation_stats.get("auto_corrections_applied", 0)
                }
            }

            return ReportSection(
                title="تحليل التحقق",
                content=content,
                summary=f"تم إجراء {validation_stats.get('total_validations', 0)} عملية تحقق بمعدل نجاح {validation_stats.get('success_rate', 0):.1f}%"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل التحقق: {str(e)}")
            return ReportSection("تحليل التحقق", {"error": str(e)})

    def _analyze_validation_errors(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل أخطاء التحقق"""
        try:
            # بيانات تجريبية - في التطبيق الحقيقي ستأتي من سجلات التحقق
            error_data = {
                "common_errors": [
                    {"type": "حجم الملف كبير", "count": 15, "percentage": 30},
                    {"type": "تنسيق غير مدعوم", "count": 10, "percentage": 20},
                    {"type": "بيانات وصفية ناقصة", "count": 8, "percentage": 16},
                    {"type": "ترميز نص خاطئ", "count": 7, "percentage": 14}
                ],
                "error_trends": {
                    "increasing": ["حجم الملف كبير"],
                    "decreasing": ["ترميز نص خاطئ"],
                    "stable": ["تنسيق غير مدعوم"]
                }
            }

            return error_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل أخطاء التحقق: {str(e)}")
            return {}

    def _analyze_auto_corrections(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل التصحيحات التلقائية"""
        try:
            # بيانات تجريبية
            correction_data = {
                "corrections_applied": [
                    {"type": "إكمال البيانات الوصفية", "count": 25, "success_rate": 95},
                    {"type": "تصحيح ترميز النص", "count": 18, "success_rate": 90},
                    {"type": "تصحيح معرفات وسائل التواصل", "count": 12, "success_rate": 85}
                ],
                "correction_effectiveness": 92.5,
                "time_saved_hours": 15.5
            }

            return correction_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل التصحيحات التلقائية: {str(e)}")
            return {}

    def _generate_notifications_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل الإشعارات"""
        try:
            if not self.notification_system:
                return ReportSection("تحليل الإشعارات", {"error": "نظام الإشعارات غير متصل"})

            # جمع بيانات الإشعارات
            notification_stats = self.notification_system.get_notification_statistics()

            content = {
                "notification_summary": notification_stats,
                "channel_analysis": self._analyze_notification_channels(start_date, end_date),
                "severity_analysis": self._analyze_notification_severity(start_date, end_date),
                "summary_stats": {
                    "total_notifications": notification_stats.get("total_notifications", 0),
                    "success_rate": notification_stats.get("delivery_success_rate", 0),
                    "most_used_channel": notification_stats.get("most_used_channel", "غير محدد")
                }
            }

            return ReportSection(
                title="تحليل الإشعارات",
                content=content,
                summary=f"تم إرسال {notification_stats.get('total_notifications', 0)} إشعار بمعدل نجاح {notification_stats.get('delivery_success_rate', 0):.1f}%"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل الإشعارات: {str(e)}")
            return ReportSection("تحليل الإشعارات", {"error": str(e)})

    def _analyze_notification_channels(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل قنوات الإشعارات"""
        try:
            # بيانات تجريبية
            channel_data = {
                "channel_performance": [
                    {"channel": "desktop", "sent": 150, "delivered": 145, "success_rate": 96.7},
                    {"channel": "email", "sent": 80, "delivered": 75, "success_rate": 93.8},
                    {"channel": "webhook", "sent": 60, "delivered": 58, "success_rate": 96.7},
                    {"channel": "log", "sent": 200, "delivered": 200, "success_rate": 100.0}
                ],
                "preferred_channels": ["desktop", "log", "webhook", "email"]
            }

            return channel_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل قنوات الإشعارات: {str(e)}")
            return {}

    def _analyze_notification_severity(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل شدة الإشعارات"""
        try:
            # بيانات تجريبية
            severity_data = {
                "severity_distribution": {
                    "INFO": 60,
                    "WARNING": 25,
                    "ERROR": 12,
                    "CRITICAL": 3
                },
                "severity_trends": {
                    "INFO": "stable",
                    "WARNING": "increasing",
                    "ERROR": "decreasing",
                    "CRITICAL": "stable"
                }
            }

            return severity_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل شدة الإشعارات: {str(e)}")
            return {}

    def _generate_trends_analysis_section(self, start_date: datetime, end_date: datetime) -> ReportSection:
        """إنتاج قسم تحليل الاتجاهات"""
        try:
            trends_data = self._analyze_system_trends(start_date, end_date)

            content = {
                "performance_trends": trends_data.get("performance", {}),
                "usage_trends": trends_data.get("usage", {}),
                "content_trends": trends_data.get("content", {}),
                "predictions": trends_data.get("predictions", {}),
                "summary_stats": {
                    "trend_direction": trends_data.get("overall_trend", "مستقر"),
                    "growth_rate": trends_data.get("growth_rate", 0),
                    "prediction_accuracy": trends_data.get("prediction_accuracy", 0)
                }
            }

            return ReportSection(
                title="تحليل الاتجاهات",
                content=content,
                summary=f"الاتجاه العام: {trends_data.get('overall_trend', 'مستقر')} بمعدل نمو {trends_data.get('growth_rate', 0):.1f}%"
            )

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج قسم تحليل الاتجاهات: {str(e)}")
            return ReportSection("تحليل الاتجاهات", {"error": str(e)})

    def _analyze_system_trends(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """تحليل اتجاهات النظام"""
        try:
            # بيانات تجريبية - في التطبيق الحقيقي ستحسب من البيانات التاريخية
            trends_data = {
                "performance": {
                    "cpu_trend": "decreasing",  # تحسن في الأداء
                    "memory_trend": "stable",
                    "response_time_trend": "decreasing"  # تحسن في أوقات الاستجابة
                },
                "usage": {
                    "daily_active_operations": "increasing",
                    "content_processing": "increasing",
                    "publishing_frequency": "stable"
                },
                "content": {
                    "quality_trend": "increasing",
                    "engagement_trend": "increasing",
                    "viral_content_rate": "stable"
                },
                "predictions": {
                    "next_week_load": "high",
                    "resource_requirements": "stable",
                    "maintenance_needed": False
                },
                "overall_trend": "تحسن",
                "growth_rate": 15.5,
                "prediction_accuracy": 87.3
            }

            return trends_data

        except Exception as e:
            self.logger.error(f"خطأ في تحليل اتجاهات النظام: {str(e)}")
            return {}

    def _generate_recommendations(self, summary_stats: Dict[str, Any], sections: List[ReportSection]) -> List[str]:
        """إنتاج التوصيات"""
        try:
            recommendations = []

            # توصيات الأداء
            if summary_stats.get("avg_cpu_usage", 0) > 80:
                recommendations.append("يُنصح بتحسين استخدام المعالج أو ترقية الأجهزة")

            if summary_stats.get("avg_memory_usage", 0) > 85:
                recommendations.append("يُنصح بزيادة الذاكرة المتاحة أو تحسين إدارة الذاكرة")

            # توصيات المحتوى
            if summary_stats.get("avg_quality_score", 0) < 7:
                recommendations.append("يُنصح بتحسين معايير جودة المحتوى وفلاتر الاختيار")

            # توصيات النشر
            if summary_stats.get("success_rate", 0) < 90:
                recommendations.append("يُنصح بمراجعة عملية النشر وتحسين معالجة الأخطاء")

            # توصيات التحقق
            if summary_stats.get("auto_corrections", 0) > 50:
                recommendations.append("يُنصح بتحسين جودة البيانات المدخلة لتقليل الحاجة للتصحيحات")

            # توصيات عامة
            recommendations.extend([
                "مراجعة دورية للإعدادات وتحديثها حسب الحاجة",
                "إجراء نسخ احتياطي منتظم للبيانات والإعدادات",
                "مراقبة الاتجاهات والتنبؤ بالاحتياجات المستقبلية"
            ])

            return recommendations

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج التوصيات: {str(e)}")
            return ["خطأ في إنتاج التوصيات"]

    def _save_report(self, report: AdvancedReport):
        """حفظ التقرير"""
        try:
            # حفظ التقرير كـ JSON
            report_file = self.reports_dir / f"{report.report_id}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(report), f, ensure_ascii=False, indent=2, default=str)

            # تحديث فهرس التقارير
            self._update_reports_index(report)

            self.logger.info(f"تم حفظ التقرير: {report.report_id}")

        except Exception as e:
            self.logger.error(f"خطأ في حفظ التقرير: {str(e)}")

    def _update_reports_index(self, report: AdvancedReport):
        """تحديث فهرس التقارير"""
        try:
            # تحميل الفهرس الحالي
            index = []
            if self.reports_index_file.exists():
                with open(self.reports_index_file, 'r', encoding='utf-8') as f:
                    index = json.load(f)

            # إضافة التقرير الجديد
            index.append({
                "report_id": report.report_id,
                "title": report.title,
                "generated_at": report.generated_at.isoformat(),
                "period_start": report.period_start.isoformat(),
                "period_end": report.period_end.isoformat(),
                "sections_count": len(report.sections),
                "file_path": str(self.reports_dir / f"{report.report_id}.json")
            })

            # ترتيب حسب تاريخ الإنتاج (الأحدث أولاً)
            index.sort(key=lambda x: x["generated_at"], reverse=True)

            # الاحتفاظ بآخر 100 تقرير فقط
            index = index[:100]

            # حفظ الفهرس المحدث
            with open(self.reports_index_file, 'w', encoding='utf-8') as f:
                json.dump(index, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث فهرس التقارير: {str(e)}")

    def _update_system_stats(self, generation_time: float, report_type: str):
        """تحديث إحصائيات النظام"""
        try:
            self.system_stats["total_reports_generated"] += 1
            self.system_stats["last_report_generated"] = datetime.now().isoformat()

            # تحديث متوسط وقت الإنتاج
            current_avg = self.system_stats["average_generation_time"]
            total_reports = self.system_stats["total_reports_generated"]

            new_avg = ((current_avg * (total_reports - 1)) + generation_time) / total_reports
            self.system_stats["average_generation_time"] = new_avg

            # تحديث نوع التقرير الأكثر طلباً (مبسط)
            self.system_stats["most_requested_report_type"] = report_type

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات النظام: {str(e)}")

    def export_report_to_pdf(self, report_id: str) -> Optional[str]:
        """تصدير التقرير إلى PDF"""
        try:
            # تحميل التقرير
            report_file = self.reports_dir / f"{report_id}.json"
            if not report_file.exists():
                self.logger.error(f"التقرير غير موجود: {report_id}")
                return None

            with open(report_file, 'r', encoding='utf-8') as f:
                report_data = json.load(f)

            # إنشاء ملف PDF
            pdf_file = self.reports_dir / f"{report_id}.pdf"

            with PdfPages(pdf_file) as pdf:
                # صفحة العنوان
                fig, ax = plt.subplots(figsize=(8.5, 11))
                ax.text(0.5, 0.8, report_data["title"], ha='center', va='center',
                       fontsize=20, fontweight='bold', transform=ax.transAxes)
                ax.text(0.5, 0.7, f"تاريخ الإنتاج: {report_data['generated_at'][:10]}",
                       ha='center', va='center', fontsize=12, transform=ax.transAxes)
                ax.text(0.5, 0.6, report_data["description"], ha='center', va='center',
                       fontsize=10, transform=ax.transAxes, wrap=True)
                ax.axis('off')
                pdf.savefig(fig, bbox_inches='tight')
                plt.close()

                # صفحات الأقسام
                for section in report_data["sections"]:
                    fig, ax = plt.subplots(figsize=(8.5, 11))
                    ax.text(0.5, 0.9, section["title"], ha='center', va='top',
                           fontsize=16, fontweight='bold', transform=ax.transAxes)

                    # محتوى القسم
                    content_text = section.get("summary", "لا يوجد ملخص متاح")
                    ax.text(0.1, 0.8, content_text, ha='left', va='top',
                           fontsize=10, transform=ax.transAxes, wrap=True)

                    ax.axis('off')
                    pdf.savefig(fig, bbox_inches='tight')
                    plt.close()

            self.logger.info(f"تم تصدير التقرير إلى PDF: {pdf_file}")
            return str(pdf_file)

        except Exception as e:
            self.logger.error(f"خطأ في تصدير التقرير إلى PDF: {str(e)}")
            return None

    def get_reports_list(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة التقارير"""
        try:
            if not self.reports_index_file.exists():
                return []

            with open(self.reports_index_file, 'r', encoding='utf-8') as f:
                return json.load(f)

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة التقارير: {str(e)}")
            return []

    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        return self.system_stats.copy()

    def cleanup_old_reports(self, days_to_keep: int = 30):
        """تنظيف التقارير القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            # تحميل فهرس التقارير
            reports_list = self.get_reports_list()

            # تصفية التقارير القديمة
            reports_to_keep = []
            for report in reports_list:
                report_date = datetime.fromisoformat(report["generated_at"])
                if report_date >= cutoff_date:
                    reports_to_keep.append(report)
                else:
                    # حذف ملف التقرير
                    report_file = Path(report["file_path"])
                    if report_file.exists():
                        report_file.unlink()

                    # حذف ملف PDF إذا وجد
                    pdf_file = report_file.with_suffix('.pdf')
                    if pdf_file.exists():
                        pdf_file.unlink()

            # تحديث الفهرس
            with open(self.reports_index_file, 'w', encoding='utf-8') as f:
                json.dump(reports_to_keep, f, ensure_ascii=False, indent=2)

            deleted_count = len(reports_list) - len(reports_to_keep)
            self.logger.info(f"تم حذف {deleted_count} تقرير قديم")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف التقارير القديمة: {str(e)}")
