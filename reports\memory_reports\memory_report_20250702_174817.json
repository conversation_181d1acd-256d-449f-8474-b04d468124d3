{"report_metadata": {"generated_at": "2025-07-02T17:48:17.228609", "report_version": "1.0", "optimizer_version": "Simple 1.0"}, "memory_analysis": {"timestamp": "2025-07-02T17:48:17.033165", "memory_usage": {"traced_current_mb": 0.10744857788085938, "traced_peak_mb": 0.18234729766845703}, "object_analysis": {"total_objects": 9831, "object_types": {"function": 1987, "wrapper_descriptor": 1237, "dict": 942, "tuple": 904, "method_descriptor": 879, "builtin_function_or_method": 754, "ReferenceType": 634, "getset_descriptor": 435, "member_descriptor": 365, "type": 260, "list": 212, "cell": 143, "frozenset": 100, "module": 83, "ModuleSpec": 81}, "large_objects": []}, "gc_stats": {"collections": [{"collections": 8, "collected": 157, "uncollectable": 0}, {"collections": 1, "collected": 0, "uncollectable": 0}, {"collections": 1, "collected": 0, "uncollectable": 0}], "threshold": [700, 10, 10], "counts": [470, 0, 0]}}, "resource_analysis": {"timestamp": "2025-07-02T17:48:17.073188", "threads": {"active_threads": 1, "main_thread_alive": true, "daemon_threads": 0}, "files": {"estimated_open_files": 31}, "system": {"python_version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "platform": "win32", "process_id": 9180}, "potential_leaks": []}, "detected_leaks": {"memory_leaks": [{"location": "Object accumulation: tuple", "size_mb": 0, "object_count": 2207, "leak_type": "object_accumulation", "severity": "medium"}, {"location": "Object accumulation: function", "size_mb": 0, "object_count": 1979, "leak_type": "object_accumulation", "severity": "medium"}, {"location": "Object accumulation: wrapper_descriptor", "size_mb": 0, "object_count": 1237, "leak_type": "object_accumulation", "severity": "medium"}], "total_leaks": 3}, "performance_metrics": {"memory_efficiency_score": 100.0, "resource_efficiency_score": 100.0, "overall_health_score": 70.0}}