#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أساسي لتحسين الذاكرة
Basic Memory Optimization Test
"""

import gc
import sys
import json
import tracemalloc
from pathlib import Path
from datetime import datetime

def test_memory_optimization():
    """اختبار تحسين الذاكرة الأساسي"""
    print("🧠 اختبار تحسين الذاكرة الأساسي")
    print("=" * 40)
    
    try:
        # بدء تتبع الذاكرة
        if not tracemalloc.is_tracing():
            tracemalloc.start()
        
        # قياس الذاكرة قبل التحسين
        before_current, before_peak = tracemalloc.get_traced_memory()
        
        print(f"\n📊 الذاكرة قبل التحسين:")
        print(f"   - الحالية: {before_current / 1024 / 1024:.2f} MB")
        print(f"   - الذروة: {before_peak / 1024 / 1024:.2f} MB")
        
        # تحليل الكائنات
        objects = gc.get_objects()
        print(f"   - عدد الكائنات: {len(objects)}")
        
        # تطبيق تحسينات الذاكرة
        print(f"\n⚡ تطبيق التحسينات:")
        
        # 1. تنظيف الذاكرة متعدد المراحل
        total_collected = 0
        for generation in range(3):
            collected = gc.collect(generation)
            total_collected += collected
            print(f"   - الجيل {generation}: تم تحرير {collected} كائن")
        
        # 2. تحسين إعدادات garbage collector
        old_threshold = gc.get_threshold()
        gc.set_threshold(700, 10, 10)
        print(f"   - تم تحسين عتبة GC من {old_threshold} إلى (700, 10, 10)")
        
        # 3. تنظيف الذاكرة المؤقتة
        try:
            import re
            if hasattr(re, '_cache'):
                re._cache.clear()
                print(f"   - تم تنظيف ذاكرة regex المؤقتة")
        except:
            pass
        
        # قياس الذاكرة بعد التحسين
        after_current, after_peak = tracemalloc.get_traced_memory()
        
        print(f"\n📊 الذاكرة بعد التحسين:")
        print(f"   - الحالية: {after_current / 1024 / 1024:.2f} MB")
        print(f"   - الذروة: {after_peak / 1024 / 1024:.2f} MB")
        
        # حساب التحسينات
        memory_saved = (before_current - after_current) / 1024 / 1024
        improvement_percent = (memory_saved / (before_current / 1024 / 1024)) * 100 if before_current > 0 else 0
        
        print(f"\n🎉 النتائج:")
        print(f"   💾 ذاكرة محررة: {memory_saved:.2f} MB")
        print(f"   🗑️ كائنات محررة: {total_collected}")
        print(f"   📈 تحسين الأداء: {improvement_percent:.1f}%")
        
        # إنشاء تقرير بسيط
        report = {
            "timestamp": datetime.now().isoformat(),
            "memory_before_mb": before_current / 1024 / 1024,
            "memory_after_mb": after_current / 1024 / 1024,
            "memory_saved_mb": memory_saved,
            "objects_collected": total_collected,
            "improvement_percent": improvement_percent,
            "gc_threshold_old": old_threshold,
            "gc_threshold_new": [700, 10, 10]
        }
        
        return report
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return None

def create_config_files():
    """إنشاء ملفات الإعدادات"""
    print(f"\n⚙️ إنشاء ملفات الإعدادات:")
    
    try:
        # إنشاء المجلد
        config_dir = Path("config/memory_optimization")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # إعدادات تحسين الذاكرة
        memory_config = {
            "memory_optimization": {
                "gc_enabled": True,
                "gc_threshold": [700, 10, 10],
                "auto_cleanup_enabled": True,
                "cleanup_interval_seconds": 300,
                "memory_threshold_mb": 512
            },
            "monitoring": {
                "memory_monitoring_enabled": True,
                "check_interval_seconds": 60,
                "alert_threshold_mb": 1024
            },
            "performance_targets": {
                "max_memory_usage_mb": 1024,
                "gc_frequency_seconds": 30,
                "memory_efficiency_target_percent": 80
            }
        }
        
        # إعدادات المراقبة
        monitoring_config = {
            "auto_monitoring": {
                "enabled": True,
                "check_interval_seconds": 60,
                "memory_threshold_mb": 512
            },
            "alerts": {
                "memory_alert_enabled": True,
                "resource_leak_alert_enabled": True,
                "alert_cooldown_minutes": 5
            },
            "cleanup": {
                "auto_cleanup_enabled": True,
                "cleanup_interval_minutes": 30,
                "memory_cleanup_threshold_mb": 256
            },
            "logging": {
                "log_level": "INFO",
                "log_file": "memory_monitoring.log",
                "max_log_size_mb": 100
            }
        }
        
        # حفظ الملفات
        configs = [
            ("memory_optimization_config.json", memory_config),
            ("monitoring_config.json", monitoring_config)
        ]
        
        created_files = []
        for filename, config in configs:
            config_file = config_dir / filename
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            created_files.append(str(config_file))
            print(f"   ✅ تم إنشاء: {config_file}")
        
        return created_files
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء الملفات: {e}")
        return []

def save_report(report):
    """حفظ التقرير"""
    if not report:
        return None
    
    try:
        # إنشاء مجلد التقارير
        reports_dir = Path("reports/memory_reports")
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # حفظ التقرير
        report_file = reports_dir / f"memory_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 تم حفظ التقرير: {report_file}")
        return str(report_file)
        
    except Exception as e:
        print(f"❌ خطأ في حفظ التقرير: {e}")
        return None

def main():
    """الوظيفة الرئيسية"""
    print("🚀 محسن الذاكرة الأساسي")
    print("=" * 40)
    
    try:
        # تشغيل اختبار التحسين
        report = test_memory_optimization()
        
        # إنشاء ملفات الإعدادات
        config_files = create_config_files()
        
        # حفظ التقرير
        report_file = save_report(report)
        
        # عرض النتائج النهائية
        print("\n" + "=" * 40)
        print("🎉 تم إكمال التحسين بنجاح!")
        print("=" * 40)
        
        if report:
            print(f"💾 ذاكرة محررة: {report['memory_saved_mb']:.2f} MB")
            print(f"🗑️ كائنات محررة: {report['objects_collected']}")
            print(f"📈 تحسين الأداء: {report['improvement_percent']:.1f}%")
        
        print(f"📁 ملفات إعدادات منشأة: {len(config_files)}")
        
        if report_file:
            print(f"📄 تقرير محفوظ: {Path(report_file).name}")
        
        print("\n✅ تم إكمال جميع المهام بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ نجح' if success else '❌ فشل'} التحسين")
    sys.exit(0 if success else 1)
