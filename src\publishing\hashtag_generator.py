#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الهاشتاغات - Hashtag Generator
يقوم بإنشاء هاشتاغات ذكية ومحسنة للمحتوى
"""

import logging
import json
import os
import re
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import requests
from collections import Counter
import random

@dataclass
class HashtagSet:
    """مجموعة هاشتاغات"""
    hashtags: List[str]
    category: str
    relevance_score: float
    trending_score: float
    engagement_potential: float
    platform: str
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "hashtags": self.hashtags,
            "category": self.category,
            "relevance_score": self.relevance_score,
            "trending_score": self.trending_score,
            "engagement_potential": self.engagement_potential,
            "platform": self.platform,
            "created_at": self.created_at.isoformat()
        }

class HashtagGenerator:
    """مولد الهاشتاغات الذكي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المولد
        self.settings = self._load_generator_settings()
        
        # قواعد بيانات الهاشتاغات
        self.hashtag_database = self._load_hashtag_database()
        
        # هاشتاغات شائعة حسب الفئة
        self.category_hashtags = self._load_category_hashtags()
        
        # هاشتاغات ترندية
        self.trending_hashtags = self._load_trending_hashtags()
        
        # إحصائيات الأداء
        self.performance_stats = self._load_performance_stats()
        
        # مسار ملفات البيانات
        self.data_dir = os.path.join(
            self.config_manager.get_app_data_dir(),
            "hashtags"
        )
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _load_generator_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المولد"""
        try:
            return self.config_manager.get_setting("publishing_settings", "hashtag_generator", {
                "max_hashtags_per_post": 30,
                "min_hashtags_per_post": 10,
                "relevance_weight": 0.4,
                "trending_weight": 0.3,
                "engagement_weight": 0.3,
                "use_trending_hashtags": True,
                "include_niche_hashtags": True,
                "include_broad_hashtags": True,
                "arabic_hashtags_ratio": 0.6,  # نسبة الهاشتاغات العربية
                "english_hashtags_ratio": 0.4,  # نسبة الهاشتاغات الإنجليزية
                "auto_update_trending": True,
                "trending_update_interval": 3600,  # ساعة واحدة
                "enable_ai_suggestions": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المولد: {str(e)}")
            return {"max_hashtags_per_post": 30, "min_hashtags_per_post": 10}
    
    def _load_hashtag_database(self) -> Dict[str, Any]:
        """تحميل قاعدة بيانات الهاشتاغات"""
        try:
            db_file = os.path.join(self.data_dir, "hashtag_database.json")
            
            if os.path.exists(db_file):
                with open(db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # إنشاء قاعدة بيانات افتراضية
                return self._create_default_database()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قاعدة بيانات الهاشتاغات: {str(e)}")
            return self._create_default_database()
    
    def _create_default_database(self) -> Dict[str, Any]:
        """إنشاء قاعدة بيانات افتراضية"""
        return {
            "arabic_hashtags": {
                "comedy": [
                    "#مضحك", "#كوميديا", "#ضحك", "#مرح", "#تسلية", "#ترفيه",
                    "#مقاطع_مضحكة", "#فكاهة", "#نكت", "#مزح", "#بهجة", "#سعادة"
                ],
                "viral": [
                    "#فيروسي", "#ترند", "#شائع", "#منتشر", "#مشهور", "#رائج",
                    "#موضة", "#جديد", "#حصري", "#مميز", "#استثنائي", "#لا_يفوت"
                ],
                "entertainment": [
                    "#ترفيه", "#تسلية", "#مرح", "#متعة", "#استمتاع", "#وقت_ممتع",
                    "#محتوى_ترفيهي", "#برامج", "#عروض", "#فعاليات", "#احتفال"
                ],
                "lifestyle": [
                    "#حياة", "#يومياتي", "#نمط_حياة", "#عادات", "#روتين",
                    "#صحة", "#رياضة", "#طبخ", "#سفر", "#موضة", "#جمال"
                ],
                "technology": [
                    "#تقنية", "#تكنولوجيا", "#ذكي", "#رقمي", "#ابتكار",
                    "#تطبيقات", "#هواتف", "#كمبيوتر", "#انترنت", "#مواقع"
                ]
            },
            "english_hashtags": {
                "comedy": [
                    "#funny", "#comedy", "#laugh", "#hilarious", "#humor",
                    "#lol", "#meme", "#jokes", "#fun", "#entertainment"
                ],
                "viral": [
                    "#viral", "#trending", "#fyp", "#foryou", "#popular",
                    "#hot", "#new", "#exclusive", "#amazing", "#incredible"
                ],
                "entertainment": [
                    "#entertainment", "#fun", "#show", "#performance",
                    "#music", "#dance", "#art", "#creative", "#talent"
                ],
                "lifestyle": [
                    "#lifestyle", "#daily", "#life", "#routine", "#health",
                    "#fitness", "#food", "#travel", "#fashion", "#beauty"
                ],
                "technology": [
                    "#tech", "#technology", "#digital", "#innovation",
                    "#apps", "#smartphone", "#computer", "#internet", "#ai"
                ]
            },
            "platform_specific": {
                "tiktok": [
                    "#tiktok", "#tiktokviral", "#tiktokfunny", "#tiktokmemes",
                    "#tiktokdance", "#tiktokchallenge", "#tiktoktrend"
                ],
                "instagram": [
                    "#instagram", "#insta", "#instagood", "#instadaily",
                    "#instafun", "#instamood", "#instalike", "#instafollow"
                ],
                "youtube": [
                    "#youtube", "#youtuber", "#youtubeshorts", "#subscribe",
                    "#youtubevideo", "#youtubetrending", "#youtubechannel"
                ]
            }
        }
    
    def _load_category_hashtags(self) -> Dict[str, List[str]]:
        """تحميل هاشتاغات الفئات"""
        try:
            categories_file = os.path.join(self.data_dir, "category_hashtags.json")
            
            if os.path.exists(categories_file):
                with open(categories_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل هاشتاغات الفئات: {str(e)}")
            return {}
    
    def _load_trending_hashtags(self) -> Dict[str, Any]:
        """تحميل الهاشتاغات الترندية"""
        try:
            trending_file = os.path.join(self.data_dir, "trending_hashtags.json")
            
            if os.path.exists(trending_file):
                with open(trending_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # التحقق من تاريخ التحديث
                last_update = datetime.fromisoformat(data.get("last_update", "2020-01-01"))
                update_interval = self.settings.get("trending_update_interval", 3600)
                
                if (datetime.now() - last_update).seconds > update_interval:
                    # تحديث الهاشتاغات الترندية
                    if self.settings.get("auto_update_trending", True):
                        self._update_trending_hashtags()
                
                return data.get("hashtags", {})
            else:
                return self._get_default_trending()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الهاشتاغات الترندية: {str(e)}")
            return self._get_default_trending()
    
    def _get_default_trending(self) -> Dict[str, Any]:
        """الحصول على هاشتاغات ترندية افتراضية"""
        return {
            "arabic": [
                "#ترند_اليوم", "#شائع_الآن", "#فيروسي_اليوم", "#مضحك_اليوم",
                "#جديد_اليوم", "#حصري_اليوم", "#مميز_اليوم", "#رائج_الآن"
            ],
            "english": [
                "#trending", "#viral", "#fyp", "#foryou", "#popular",
                "#hot", "#new", "#todaystrend", "#viraltoday"
            ],
            "last_update": datetime.now().isoformat()
        }
    
    def _load_performance_stats(self) -> Dict[str, Any]:
        """تحميل إحصائيات الأداء"""
        try:
            stats_file = os.path.join(self.data_dir, "performance_stats.json")
            
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "hashtag_performance": {},
                    "category_performance": {},
                    "best_combinations": [],
                    "total_posts_analyzed": 0
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إحصائيات الأداء: {str(e)}")
            return {"hashtag_performance": {}, "total_posts_analyzed": 0}
    
    def generate_hashtags(self, content_analysis: Dict[str, Any], platform: str = "tiktok",
                         target_count: Optional[int] = None) -> HashtagSet:
        """إنشاء هاشتاغات للمحتوى"""
        try:
            self.logger.info(f"بدء إنشاء هاشتاغات للمنصة: {platform}")
            
            # تحديد عدد الهاشتاغات المطلوب
            if not target_count:
                target_count = min(
                    self.settings.get("max_hashtags_per_post", 30),
                    random.randint(
                        self.settings.get("min_hashtags_per_post", 10),
                        self.settings.get("max_hashtags_per_post", 30)
                    )
                )
            
            # تحليل المحتوى لاستخراج الكلمات المفتاحية
            keywords = self._extract_keywords(content_analysis)
            
            # تحديد فئة المحتوى
            content_category = self._determine_content_category(content_analysis)
            
            # إنشاء مجموعات مختلفة من الهاشتاغات
            hashtag_groups = {
                "content_based": self._generate_content_based_hashtags(keywords, content_analysis),
                "category_based": self._generate_category_hashtags(content_category),
                "trending": self._generate_trending_hashtags(platform),
                "platform_specific": self._generate_platform_hashtags(platform),
                "engagement_boosters": self._generate_engagement_hashtags()
            }
            
            # دمج وتحسين الهاشتاغات
            final_hashtags = self._combine_and_optimize_hashtags(
                hashtag_groups, target_count, platform
            )
            
            # حساب النقاط
            relevance_score = self._calculate_relevance_score(final_hashtags, keywords)
            trending_score = self._calculate_trending_score(final_hashtags)
            engagement_potential = self._calculate_engagement_potential(final_hashtags)
            
            # إنشاء مجموعة الهاشتاغات النهائية
            hashtag_set = HashtagSet(
                hashtags=final_hashtags,
                category=content_category,
                relevance_score=relevance_score,
                trending_score=trending_score,
                engagement_potential=engagement_potential,
                platform=platform,
                created_at=datetime.now()
            )
            
            self.logger.info(f"تم إنشاء {len(final_hashtags)} هاشتاغ للفئة {content_category}")
            
            return hashtag_set
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الهاشتاغات: {str(e)}")
            # إرجاع هاشتاغات افتراضية في حالة الخطأ
            return self._get_fallback_hashtags(platform)
    
    def _extract_keywords(self, content_analysis: Dict[str, Any]) -> List[str]:
        """استخراج الكلمات المفتاحية من تحليل المحتوى"""
        try:
            keywords = []
            
            # من النسخ النصي
            if "transcription" in content_analysis:
                text = content_analysis["transcription"].get("text", "")
                if text:
                    # تنظيف النص واستخراج الكلمات المهمة
                    words = re.findall(r'\b\w+\b', text.lower())
                    # تصفية الكلمات القصيرة والشائعة
                    filtered_words = [
                        word for word in words 
                        if len(word) > 3 and word not in self._get_stop_words()
                    ]
                    keywords.extend(filtered_words[:10])  # أول 10 كلمات
            
            # من الكلمات المفتاحية المحددة مسبقاً
            if "keywords" in content_analysis:
                keywords.extend(content_analysis["keywords"][:5])
            
            # من تحليل المشاعر
            if "emotions" in content_analysis:
                emotions = content_analysis["emotions"]
                for emotion, score in emotions.items():
                    if score > 50:  # مشاعر قوية
                        keywords.append(emotion)
            
            return list(set(keywords))  # إزالة التكرار
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الكلمات المفتاحية: {str(e)}")
            return []
    
    def _get_stop_words(self) -> Set[str]:
        """الحصول على كلمات الإيقاف"""
        return {
            "في", "من", "إلى", "على", "عن", "مع", "هذا", "هذه", "ذلك", "تلك",
            "التي", "الذي", "التي", "كان", "كانت", "يكون", "تكون", "هو", "هي",
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of",
            "with", "by", "is", "are", "was", "were", "be", "been", "have", "has"
        }

    def _determine_content_category(self, content_analysis: Dict[str, Any]) -> str:
        """تحديد فئة المحتوى"""
        try:
            # من تحليل المشاعر
            if "emotions" in content_analysis:
                emotions = content_analysis["emotions"]

                # إذا كان المحتوى مضحك
                if emotions.get("joy", 0) > 60 or emotions.get("amusement", 0) > 60:
                    return "comedy"

                # إذا كان المحتوى حماسي
                if emotions.get("excitement", 0) > 60:
                    return "viral"

                # إذا كان المحتوى هادئ
                if emotions.get("calm", 0) > 60:
                    return "lifestyle"

            # من الكلمات المفتاحية
            if "keywords" in content_analysis:
                keywords = [k.lower() for k in content_analysis["keywords"]]

                comedy_words = ["مضحك", "ضحك", "كوميديا", "funny", "comedy", "laugh"]
                tech_words = ["تقنية", "تكنولوجيا", "tech", "technology", "digital"]
                lifestyle_words = ["حياة", "يومي", "lifestyle", "daily", "routine"]

                if any(word in keywords for word in comedy_words):
                    return "comedy"
                elif any(word in keywords for word in tech_words):
                    return "technology"
                elif any(word in keywords for word in lifestyle_words):
                    return "lifestyle"

            # افتراضي
            return "entertainment"

        except Exception as e:
            self.logger.error(f"خطأ في تحديد فئة المحتوى: {str(e)}")
            return "entertainment"

    def _generate_content_based_hashtags(self, keywords: List[str],
                                       content_analysis: Dict[str, Any]) -> List[str]:
        """إنشاء هاشتاغات مبنية على المحتوى"""
        try:
            hashtags = []

            # من الكلمات المفتاحية
            for keyword in keywords[:5]:  # أول 5 كلمات
                # إضافة الكلمة كهاشتاغ
                hashtags.append(f"#{keyword}")

                # البحث عن هاشتاغات مشابهة في قاعدة البيانات
                similar_hashtags = self._find_similar_hashtags(keyword)
                hashtags.extend(similar_hashtags[:2])  # أول 2 مشابهة

            # من تحليل الصوت
            if "audio_analysis" in content_analysis:
                audio = content_analysis["audio_analysis"]

                if audio.get("has_music", False):
                    hashtags.extend(["#موسيقى", "#music", "#soundtrack"])

                if audio.get("energy_level", 0) > 70:
                    hashtags.extend(["#حماس", "#طاقة", "#energy", "#exciting"])

            # من تحليل الفيديو
            if "video_analysis" in content_analysis:
                video = content_analysis["video_analysis"]

                if video.get("has_faces", False):
                    hashtags.extend(["#وجوه", "#أشخاص", "#people", "#faces"])

                if video.get("motion_level", 0) > 60:
                    hashtags.extend(["#حركة", "#نشاط", "#motion", "#active"])

            return list(set(hashtags))  # إزالة التكرار

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء هاشتاغات المحتوى: {str(e)}")
            return []

    def _find_similar_hashtags(self, keyword: str) -> List[str]:
        """البحث عن هاشتاغات مشابهة"""
        try:
            similar = []
            keyword_lower = keyword.lower()

            # البحث في قاعدة البيانات
            for lang in ["arabic_hashtags", "english_hashtags"]:
                for category, hashtags in self.hashtag_database.get(lang, {}).items():
                    for hashtag in hashtags:
                        hashtag_clean = hashtag.replace("#", "").lower()

                        # تطابق جزئي
                        if keyword_lower in hashtag_clean or hashtag_clean in keyword_lower:
                            similar.append(hashtag)

                        # تطابق الجذر (مبسط)
                        if len(keyword_lower) > 4 and len(hashtag_clean) > 4:
                            if keyword_lower[:4] == hashtag_clean[:4]:
                                similar.append(hashtag)

            return similar[:5]  # أول 5 نتائج

        except Exception as e:
            self.logger.error(f"خطأ في البحث عن هاشتاغات مشابهة: {str(e)}")
            return []

    def _generate_category_hashtags(self, category: str) -> List[str]:
        """إنشاء هاشتاغات الفئة"""
        try:
            hashtags = []

            # من قاعدة البيانات العربية
            arabic_hashtags = self.hashtag_database.get("arabic_hashtags", {})
            if category in arabic_hashtags:
                hashtags.extend(arabic_hashtags[category][:6])

            # من قاعدة البيانات الإنجليزية
            english_hashtags = self.hashtag_database.get("english_hashtags", {})
            if category in english_hashtags:
                hashtags.extend(english_hashtags[category][:4])

            return hashtags

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء هاشتاغات الفئة: {str(e)}")
            return []

    def _generate_trending_hashtags(self, platform: str) -> List[str]:
        """إنشاء هاشتاغات ترندية"""
        try:
            if not self.settings.get("use_trending_hashtags", True):
                return []

            hashtags = []

            # هاشتاغات ترندية عربية
            arabic_trending = self.trending_hashtags.get("arabic", [])
            hashtags.extend(random.sample(arabic_trending, min(3, len(arabic_trending))))

            # هاشتاغات ترندية إنجليزية
            english_trending = self.trending_hashtags.get("english", [])
            hashtags.extend(random.sample(english_trending, min(2, len(english_trending))))

            return hashtags

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء هاشتاغات ترندية: {str(e)}")
            return []

    def _generate_platform_hashtags(self, platform: str) -> List[str]:
        """إنشاء هاشتاغات خاصة بالمنصة"""
        try:
            platform_hashtags = self.hashtag_database.get("platform_specific", {})
            return platform_hashtags.get(platform, [])[:3]

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء هاشتاغات المنصة: {str(e)}")
            return []

    def _generate_engagement_hashtags(self) -> List[str]:
        """إنشاء هاشتاغات لزيادة التفاعل"""
        try:
            engagement_hashtags = [
                "#فولو", "#لايك", "#شير", "#كومنت", "#تفاعل",
                "#follow", "#like", "#share", "#comment", "#engage",
                "#fyp", "#foryou", "#viral", "#trending"
            ]

            return random.sample(engagement_hashtags, min(4, len(engagement_hashtags)))

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء هاشتاغات التفاعل: {str(e)}")
            return []

    def _combine_and_optimize_hashtags(self, hashtag_groups: Dict[str, List[str]],
                                     target_count: int, platform: str) -> List[str]:
        """دمج وتحسين الهاشتاغات"""
        try:
            all_hashtags = []

            # جمع كل الهاشتاغات
            for group_name, hashtags in hashtag_groups.items():
                all_hashtags.extend(hashtags)

            # إزالة التكرار
            unique_hashtags = list(set(all_hashtags))

            # تنظيف الهاشتاغات
            cleaned_hashtags = []
            for hashtag in unique_hashtags:
                cleaned = self._clean_hashtag(hashtag)
                if cleaned and self._is_valid_hashtag(cleaned):
                    cleaned_hashtags.append(cleaned)

            # ترتيب حسب الأولوية
            prioritized_hashtags = self._prioritize_hashtags(cleaned_hashtags, platform)

            # تطبيق نسب اللغات
            final_hashtags = self._apply_language_ratios(prioritized_hashtags, target_count)

            # التأكد من عدم تجاوز الحد الأقصى
            return final_hashtags[:target_count]

        except Exception as e:
            self.logger.error(f"خطأ في دمج الهاشتاغات: {str(e)}")
            return []

    def _clean_hashtag(self, hashtag: str) -> str:
        """تنظيف الهاشتاغ"""
        try:
            # إزالة المسافات والرموز غير المرغوبة
            cleaned = hashtag.strip()

            # التأكد من وجود #
            if not cleaned.startswith("#"):
                cleaned = "#" + cleaned

            # إزالة الرموز غير المسموحة
            cleaned = re.sub(r'[^\w\u0600-\u06FF#_]', '', cleaned)

            return cleaned

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الهاشتاغ: {str(e)}")
            return ""

    def _is_valid_hashtag(self, hashtag: str) -> bool:
        """التحقق من صحة الهاشتاغ"""
        try:
            # يجب أن يبدأ بـ #
            if not hashtag.startswith("#"):
                return False

            # يجب أن يحتوي على محتوى بعد #
            content = hashtag[1:]
            if len(content) < 2:
                return False

            # يجب ألا يحتوي على مسافات
            if " " in content:
                return False

            # يجب ألا يكون طويلاً جداً
            if len(content) > 50:
                return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من صحة الهاشتاغ: {str(e)}")
            return False

    def _prioritize_hashtags(self, hashtags: List[str], platform: str) -> List[str]:
        """ترتيب الهاشتاغات حسب الأولوية"""
        try:
            # حساب نقاط لكل هاشتاغ
            hashtag_scores = {}

            for hashtag in hashtags:
                score = 0

                # نقاط الأداء السابق
                performance = self.performance_stats.get("hashtag_performance", {})
                if hashtag in performance:
                    score += performance[hashtag].get("engagement_rate", 0) * 10

                # نقاط الترند
                if hashtag in self.trending_hashtags.get("arabic", []):
                    score += 20
                elif hashtag in self.trending_hashtags.get("english", []):
                    score += 15

                # نقاط المنصة
                platform_hashtags = self.hashtag_database.get("platform_specific", {})
                if hashtag in platform_hashtags.get(platform, []):
                    score += 25

                hashtag_scores[hashtag] = score

            # ترتيب حسب النقاط
            sorted_hashtags = sorted(
                hashtag_scores.items(),
                key=lambda x: x[1],
                reverse=True
            )

            return [hashtag for hashtag, score in sorted_hashtags]

        except Exception as e:
            self.logger.error(f"خطأ في ترتيب الهاشتاغات: {str(e)}")
            return hashtags

    def _apply_language_ratios(self, hashtags: List[str], target_count: int) -> List[str]:
        """تطبيق نسب اللغات"""
        try:
            arabic_ratio = self.settings.get("arabic_hashtags_ratio", 0.6)
            english_ratio = self.settings.get("english_hashtags_ratio", 0.4)

            # تصنيف الهاشتاغات حسب اللغة
            arabic_hashtags = []
            english_hashtags = []

            for hashtag in hashtags:
                content = hashtag[1:]  # إزالة #

                # التحقق من وجود أحرف عربية
                if re.search(r'[\u0600-\u06FF]', content):
                    arabic_hashtags.append(hashtag)
                else:
                    english_hashtags.append(hashtag)

            # حساب العدد المطلوب لكل لغة
            arabic_count = int(target_count * arabic_ratio)
            english_count = target_count - arabic_count

            # اختيار الهاشتاغات
            final_hashtags = []
            final_hashtags.extend(arabic_hashtags[:arabic_count])
            final_hashtags.extend(english_hashtags[:english_count])

            # إذا لم نصل للعدد المطلوب، أضف من المتبقي
            if len(final_hashtags) < target_count:
                remaining = target_count - len(final_hashtags)
                all_remaining = arabic_hashtags[arabic_count:] + english_hashtags[english_count:]
                final_hashtags.extend(all_remaining[:remaining])

            return final_hashtags

        except Exception as e:
            self.logger.error(f"خطأ في تطبيق نسب اللغات: {str(e)}")
            return hashtags[:target_count]

    def _calculate_relevance_score(self, hashtags: List[str], keywords: List[str]) -> float:
        """حساب نقاط الصلة"""
        try:
            if not hashtags or not keywords:
                return 0.0

            relevant_count = 0

            for hashtag in hashtags:
                hashtag_content = hashtag[1:].lower()  # إزالة # وتحويل لأحرف صغيرة

                for keyword in keywords:
                    keyword_lower = keyword.lower()

                    # تطابق مباشر
                    if keyword_lower in hashtag_content or hashtag_content in keyword_lower:
                        relevant_count += 1
                        break

                    # تطابق جزئي
                    if len(keyword_lower) > 4 and len(hashtag_content) > 4:
                        if keyword_lower[:4] == hashtag_content[:4]:
                            relevant_count += 0.5
                            break

            return (relevant_count / len(hashtags)) * 100

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط الصلة: {str(e)}")
            return 0.0

    def _calculate_trending_score(self, hashtags: List[str]) -> float:
        """حساب نقاط الترند"""
        try:
            if not hashtags:
                return 0.0

            trending_count = 0

            for hashtag in hashtags:
                # التحقق من الهاشتاغات الترندية
                if (hashtag in self.trending_hashtags.get("arabic", []) or
                    hashtag in self.trending_hashtags.get("english", [])):
                    trending_count += 1

                # التحقق من الهاشتاغات الشائعة
                performance = self.performance_stats.get("hashtag_performance", {})
                if hashtag in performance:
                    if performance[hashtag].get("usage_count", 0) > 100:
                        trending_count += 0.5

            return (trending_count / len(hashtags)) * 100

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط الترند: {str(e)}")
            return 0.0

    def _calculate_engagement_potential(self, hashtags: List[str]) -> float:
        """حساب إمكانية التفاعل"""
        try:
            if not hashtags:
                return 0.0

            total_engagement = 0
            hashtag_count = 0

            for hashtag in hashtags:
                performance = self.performance_stats.get("hashtag_performance", {})

                if hashtag in performance:
                    engagement_rate = performance[hashtag].get("engagement_rate", 0)
                    total_engagement += engagement_rate
                    hashtag_count += 1
                else:
                    # تقدير افتراضي للهاشتاغات الجديدة
                    if hashtag in self.trending_hashtags.get("arabic", []):
                        total_engagement += 70  # نقاط عالية للترندية
                    elif hashtag in self.trending_hashtags.get("english", []):
                        total_engagement += 60
                    else:
                        total_engagement += 30  # نقاط متوسطة
                    hashtag_count += 1

            if hashtag_count > 0:
                return total_engagement / hashtag_count
            else:
                return 50.0  # قيمة افتراضية

        except Exception as e:
            self.logger.error(f"خطأ في حساب إمكانية التفاعل: {str(e)}")
            return 50.0

    def _get_fallback_hashtags(self, platform: str) -> HashtagSet:
        """الحصول على هاشتاغات احتياطية"""
        try:
            fallback_hashtags = [
                "#محتوى", "#فيديو", "#ترفيه", "#مضحك", "#جديد",
                "#content", "#video", "#entertainment", "#funny", "#new"
            ]

            # إضافة هاشتاغات المنصة
            platform_hashtags = self.hashtag_database.get("platform_specific", {})
            if platform in platform_hashtags:
                fallback_hashtags.extend(platform_hashtags[platform][:3])

            return HashtagSet(
                hashtags=fallback_hashtags[:15],
                category="general",
                relevance_score=30.0,
                trending_score=20.0,
                engagement_potential=40.0,
                platform=platform,
                created_at=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على هاشتاغات احتياطية: {str(e)}")
            return HashtagSet(
                hashtags=["#محتوى", "#content"],
                category="general",
                relevance_score=0.0,
                trending_score=0.0,
                engagement_potential=0.0,
                platform=platform,
                created_at=datetime.now()
            )

    def update_hashtag_performance(self, hashtags: List[str], engagement_data: Dict[str, Any]):
        """تحديث أداء الهاشتاغات"""
        try:
            performance = self.performance_stats.get("hashtag_performance", {})

            engagement_rate = engagement_data.get("engagement_rate", 0)
            likes = engagement_data.get("likes", 0)
            comments = engagement_data.get("comments", 0)
            shares = engagement_data.get("shares", 0)

            for hashtag in hashtags:
                if hashtag not in performance:
                    performance[hashtag] = {
                        "usage_count": 0,
                        "total_engagement": 0,
                        "total_likes": 0,
                        "total_comments": 0,
                        "total_shares": 0,
                        "engagement_rate": 0
                    }

                # تحديث الإحصائيات
                performance[hashtag]["usage_count"] += 1
                performance[hashtag]["total_engagement"] += engagement_rate
                performance[hashtag]["total_likes"] += likes
                performance[hashtag]["total_comments"] += comments
                performance[hashtag]["total_shares"] += shares

                # حساب متوسط معدل التفاعل
                usage_count = performance[hashtag]["usage_count"]
                performance[hashtag]["engagement_rate"] = (
                    performance[hashtag]["total_engagement"] / usage_count
                )

            # حفظ الإحصائيات المحدثة
            self.performance_stats["hashtag_performance"] = performance
            self.performance_stats["total_posts_analyzed"] += 1
            self._save_performance_stats()

            self.logger.info(f"تم تحديث أداء {len(hashtags)} هاشتاغ")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث أداء الهاشتاغات: {str(e)}")

    def get_best_performing_hashtags(self, category: Optional[str] = None,
                                   limit: int = 20) -> List[Dict[str, Any]]:
        """الحصول على أفضل الهاشتاغات أداءً"""
        try:
            performance = self.performance_stats.get("hashtag_performance", {})

            # تصفية حسب الفئة إذا تم تحديدها
            filtered_hashtags = []

            for hashtag, stats in performance.items():
                if stats.get("usage_count", 0) >= 5:  # على الأقل 5 استخدامات
                    hashtag_data = {
                        "hashtag": hashtag,
                        "engagement_rate": stats.get("engagement_rate", 0),
                        "usage_count": stats.get("usage_count", 0),
                        "total_likes": stats.get("total_likes", 0),
                        "total_comments": stats.get("total_comments", 0),
                        "total_shares": stats.get("total_shares", 0)
                    }

                    # تصفية حسب الفئة
                    if category:
                        category_hashtags = self.hashtag_database.get("arabic_hashtags", {})
                        category_hashtags.update(self.hashtag_database.get("english_hashtags", {}))

                        if category in category_hashtags:
                            if hashtag in category_hashtags[category]:
                                filtered_hashtags.append(hashtag_data)
                    else:
                        filtered_hashtags.append(hashtag_data)

            # ترتيب حسب معدل التفاعل
            sorted_hashtags = sorted(
                filtered_hashtags,
                key=lambda x: x["engagement_rate"],
                reverse=True
            )

            return sorted_hashtags[:limit]

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أفضل الهاشتاغات: {str(e)}")
            return []

    def _update_trending_hashtags(self):
        """تحديث الهاشتاغات الترندية"""
        try:
            self.logger.info("بدء تحديث الهاشتاغات الترندية")

            # هنا يمكن إضافة استدعاءات API للحصول على الهاشتاغات الترندية
            # لكن سنستخدم قائمة محدثة يدوياً لتجنب مشاكل API

            updated_trending = {
                "arabic": [
                    "#ترند_اليوم", "#شائع_الآن", "#فيروسي_اليوم", "#مضحك_اليوم",
                    "#جديد_اليوم", "#حصري_اليوم", "#مميز_اليوم", "#رائج_الآن",
                    "#موضة_اليوم", "#تحدي_اليوم", "#مقطع_اليوم", "#فيديو_اليوم"
                ],
                "english": [
                    "#trending", "#viral", "#fyp", "#foryou", "#popular",
                    "#hot", "#new", "#todaystrend", "#viraltoday", "#challenge",
                    "#tiktoktrend", "#instareels", "#shorts", "#explore"
                ],
                "last_update": datetime.now().isoformat()
            }

            # حفظ الهاشتاغات المحدثة
            trending_file = os.path.join(self.data_dir, "trending_hashtags.json")
            with open(trending_file, 'w', encoding='utf-8') as f:
                json.dump(updated_trending, f, ensure_ascii=False, indent=2)

            # تحديث الذاكرة
            self.trending_hashtags = updated_trending

            self.logger.info("تم تحديث الهاشتاغات الترندية بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الهاشتاغات الترندية: {str(e)}")

    def _save_performance_stats(self):
        """حفظ إحصائيات الأداء"""
        try:
            stats_file = os.path.join(self.data_dir, "performance_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.performance_stats, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ إحصائيات الأداء: {str(e)}")

    def add_custom_hashtags(self, category: str, hashtags: List[str], language: str = "arabic"):
        """إضافة هاشتاغات مخصصة"""
        try:
            lang_key = f"{language}_hashtags"

            if lang_key not in self.hashtag_database:
                self.hashtag_database[lang_key] = {}

            if category not in self.hashtag_database[lang_key]:
                self.hashtag_database[lang_key][category] = []

            # إضافة الهاشتاغات الجديدة
            for hashtag in hashtags:
                cleaned_hashtag = self._clean_hashtag(hashtag)
                if cleaned_hashtag and self._is_valid_hashtag(cleaned_hashtag):
                    if cleaned_hashtag not in self.hashtag_database[lang_key][category]:
                        self.hashtag_database[lang_key][category].append(cleaned_hashtag)

            # حفظ قاعدة البيانات المحدثة
            self._save_hashtag_database()

            self.logger.info(f"تم إضافة {len(hashtags)} هاشتاغ للفئة {category}")

        except Exception as e:
            self.logger.error(f"خطأ في إضافة هاشتاغات مخصصة: {str(e)}")

    def _save_hashtag_database(self):
        """حفظ قاعدة بيانات الهاشتاغات"""
        try:
            db_file = os.path.join(self.data_dir, "hashtag_database.json")
            with open(db_file, 'w', encoding='utf-8') as f:
                json.dump(self.hashtag_database, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات الهاشتاغات: {str(e)}")

    def get_hashtag_suggestions(self, text: str, max_suggestions: int = 10) -> List[str]:
        """الحصول على اقتراحات هاشتاغات من النص"""
        try:
            suggestions = []
            text_lower = text.lower()

            # البحث في قاعدة البيانات
            for lang in ["arabic_hashtags", "english_hashtags"]:
                for category, hashtags in self.hashtag_database.get(lang, {}).items():
                    for hashtag in hashtags:
                        hashtag_content = hashtag[1:].lower()  # إزالة #

                        # البحث عن تطابق في النص
                        if hashtag_content in text_lower or any(
                            word in hashtag_content for word in text_lower.split()
                        ):
                            suggestions.append(hashtag)

            # إزالة التكرار وترتيب
            unique_suggestions = list(set(suggestions))

            # ترتيب حسب الأداء
            performance = self.performance_stats.get("hashtag_performance", {})
            sorted_suggestions = sorted(
                unique_suggestions,
                key=lambda x: performance.get(x, {}).get("engagement_rate", 0),
                reverse=True
            )

            return sorted_suggestions[:max_suggestions]

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على اقتراحات الهاشتاغات: {str(e)}")
            return []

    def get_generator_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المولد"""
        try:
            stats = {
                "total_hashtags_in_database": 0,
                "total_categories": 0,
                "total_posts_analyzed": self.performance_stats.get("total_posts_analyzed", 0),
                "best_performing_hashtags": self.get_best_performing_hashtags(limit=5),
                "trending_hashtags_count": {
                    "arabic": len(self.trending_hashtags.get("arabic", [])),
                    "english": len(self.trending_hashtags.get("english", []))
                },
                "categories": {
                    "arabic": list(self.hashtag_database.get("arabic_hashtags", {}).keys()),
                    "english": list(self.hashtag_database.get("english_hashtags", {}).keys())
                }
            }

            # حساب إجمالي الهاشتاغات
            for lang in ["arabic_hashtags", "english_hashtags"]:
                for category, hashtags in self.hashtag_database.get(lang, {}).items():
                    stats["total_hashtags_in_database"] += len(hashtags)
                    stats["total_categories"] += 1

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات المولد: {str(e)}")
            return {}

    def export_hashtags(self, file_path: str, category: Optional[str] = None):
        """تصدير الهاشتاغات إلى ملف"""
        try:
            export_data = {}

            if category:
                # تصدير فئة محددة
                for lang in ["arabic_hashtags", "english_hashtags"]:
                    if category in self.hashtag_database.get(lang, {}):
                        if lang not in export_data:
                            export_data[lang] = {}
                        export_data[lang][category] = self.hashtag_database[lang][category]
            else:
                # تصدير كل الهاشتاغات
                export_data = self.hashtag_database

            # إضافة إحصائيات الأداء
            export_data["performance_stats"] = self.performance_stats
            export_data["export_date"] = datetime.now().isoformat()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير الهاشتاغات إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير الهاشتاغات: {str(e)}")

    def import_hashtags(self, file_path: str):
        """استيراد هاشتاغات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            # دمج الهاشتاغات
            for lang in ["arabic_hashtags", "english_hashtags"]:
                if lang in import_data:
                    if lang not in self.hashtag_database:
                        self.hashtag_database[lang] = {}

                    for category, hashtags in import_data[lang].items():
                        if category not in self.hashtag_database[lang]:
                            self.hashtag_database[lang][category] = []

                        # إضافة الهاشتاغات الجديدة فقط
                        for hashtag in hashtags:
                            if hashtag not in self.hashtag_database[lang][category]:
                                self.hashtag_database[lang][category].append(hashtag)

            # دمج إحصائيات الأداء
            if "performance_stats" in import_data:
                imported_performance = import_data["performance_stats"]
                current_performance = self.performance_stats.get("hashtag_performance", {})

                for hashtag, stats in imported_performance.get("hashtag_performance", {}).items():
                    if hashtag not in current_performance:
                        current_performance[hashtag] = stats
                    else:
                        # دمج الإحصائيات
                        current_performance[hashtag]["usage_count"] += stats.get("usage_count", 0)
                        current_performance[hashtag]["total_engagement"] += stats.get("total_engagement", 0)
                        current_performance[hashtag]["total_likes"] += stats.get("total_likes", 0)
                        current_performance[hashtag]["total_comments"] += stats.get("total_comments", 0)
                        current_performance[hashtag]["total_shares"] += stats.get("total_shares", 0)

                        # إعادة حساب معدل التفاعل
                        usage_count = current_performance[hashtag]["usage_count"]
                        if usage_count > 0:
                            current_performance[hashtag]["engagement_rate"] = (
                                current_performance[hashtag]["total_engagement"] / usage_count
                            )

            # حفظ البيانات المحدثة
            self._save_hashtag_database()
            self._save_performance_stats()

            self.logger.info(f"تم استيراد الهاشتاغات من {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد الهاشتاغات: {str(e)}")
