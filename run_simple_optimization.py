#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشغيل التحسين المبسطة - Simple Optimization Runner
تشغل تحسينات الأداء الأساسية بدون مكتبات خارجية
"""

import os
import sys
import gc
import time
import json
import threading
import tracemalloc
from pathlib import Path
from datetime import datetime
import logging

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('optimization.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SimpleOptimizationRunner:
    """أداة تشغيل التحسين المبسطة"""
    
    def __init__(self):
        self.start_time = time.time()
        self.results = {}
        
        # بدء تتبع الذاكرة
        tracemalloc.start()
        
    def run_optimization(self) -> Dict[str, Any]:
        """تشغيل التحسين الشامل"""
        logger.info("🚀 بدء عملية تحسين الأداء المبسطة...")
        
        try:
            # المرحلة 1: تحليل الوضع الحالي
            logger.info("📊 المرحلة 1: تحليل الوضع الحالي...")
            initial_analysis = self._analyze_current_state()
            
            # المرحلة 2: تحسين الذاكرة
            logger.info("🧠 المرحلة 2: تحسين الذاكرة...")
            memory_optimization = self._optimize_memory()
            
            # المرحلة 3: تنظيف الملفات المؤقتة
            logger.info("🧹 المرحلة 3: تنظيف الملفات المؤقتة...")
            cleanup_results = self._cleanup_temp_files()
            
            # المرحلة 4: تحسين إعدادات Python
            logger.info("⚙️ المرحلة 4: تحسين إعدادات Python...")
            python_optimization = self._optimize_python_settings()
            
            # المرحلة 5: فحص الموارد النهائي
            logger.info("🔍 المرحلة 5: فحص الموارد النهائي...")
            final_analysis = self._analyze_current_state()
            
            # المرحلة 6: توليد التقرير
            logger.info("📋 المرحلة 6: توليد التقرير...")
            report = self._generate_report(
                initial_analysis, 
                memory_optimization,
                cleanup_results,
                python_optimization,
                final_analysis
            )
            
            logger.info("✅ تم إكمال عملية التحسين بنجاح!")
            return report
            
        except Exception as e:
            logger.error(f"❌ خطأ في عملية التحسين: {e}")
            return {"error": str(e), "status": "failed"}
    
    def _analyze_current_state(self) -> Dict[str, Any]:
        """تحليل الوضع الحالي للنظام"""
        try:
            # معلومات الذاكرة من tracemalloc
            current, peak = tracemalloc.get_traced_memory()
            
            # معلومات الخيوط
            thread_count = threading.active_count()
            
            # معلومات الملفات
            temp_files = self._count_temp_files()
            
            # معلومات Python
            python_info = {
                "version": sys.version,
                "platform": sys.platform,
                "executable": sys.executable
            }
            
            return {
                "timestamp": datetime.now().isoformat(),
                "memory": {
                    "current_mb": current / 1024 / 1024,
                    "peak_mb": peak / 1024 / 1024
                },
                "threads": thread_count,
                "temp_files": temp_files,
                "python": python_info,
                "gc_stats": {
                    "collections": gc.get_stats(),
                    "counts": gc.get_count()
                }
            }
            
        except Exception as e:
            return {"error": f"خطأ في التحليل: {e}"}
    
    def _optimize_memory(self) -> Dict[str, Any]:
        """تحسين استخدام الذاكرة"""
        try:
            # قياس الذاكرة قبل التحسين
            before_current, before_peak = tracemalloc.get_traced_memory()
            
            # تشغيل garbage collection
            collected_objects = []
            for generation in range(3):
                collected = gc.collect(generation)
                collected_objects.append(collected)
            
            # تحسين إعدادات gc
            gc.set_threshold(700, 10, 10)  # تحسين عتبات التنظيف
            
            # قياس الذاكرة بعد التحسين
            after_current, after_peak = tracemalloc.get_traced_memory()
            
            return {
                "status": "completed",
                "before_mb": before_current / 1024 / 1024,
                "after_mb": after_current / 1024 / 1024,
                "saved_mb": (before_current - after_current) / 1024 / 1024,
                "collected_objects": collected_objects,
                "total_collected": sum(collected_objects)
            }
            
        except Exception as e:
            return {"error": f"خطأ في تحسين الذاكرة: {e}"}
    
    def _cleanup_temp_files(self) -> Dict[str, Any]:
        """تنظيف الملفات المؤقتة"""
        try:
            cleaned_files = 0
            cleaned_size = 0
            
            # مجلدات الملفات المؤقتة
            temp_dirs = [
                "temp",
                "tmp", 
                "__pycache__",
                ".pytest_cache",
                "logs"
            ]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                # احذف الملفات القديمة (أكثر من يوم)
                                if time.time() - os.path.getmtime(file_path) > 86400:
                                    os.remove(file_path)
                                    cleaned_files += 1
                                    cleaned_size += file_size
                            except:
                                continue
            
            # تنظيف ملفات .pyc
            for root, dirs, files in os.walk("."):
                for file in files:
                    if file.endswith('.pyc'):
                        try:
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_files += 1
                            cleaned_size += file_size
                        except:
                            continue
            
            return {
                "status": "completed",
                "cleaned_files": cleaned_files,
                "cleaned_size_mb": cleaned_size / 1024 / 1024
            }
            
        except Exception as e:
            return {"error": f"خطأ في تنظيف الملفات: {e}"}
    
    def _optimize_python_settings(self) -> Dict[str, Any]:
        """تحسين إعدادات Python"""
        try:
            optimizations = []
            
            # تحسين استيراد الوحدات
            if hasattr(sys, 'dont_write_bytecode'):
                original_bytecode = sys.dont_write_bytecode
                sys.dont_write_bytecode = False  # السماح بكتابة bytecode للسرعة
                optimizations.append("enabled_bytecode_writing")
            
            # تحسين إعدادات الخيوط
            original_switch_interval = sys.getswitchinterval()
            sys.setswitchinterval(0.001)  # تحسين تبديل الخيوط
            optimizations.append("optimized_thread_switching")
            
            return {
                "status": "completed",
                "optimizations": optimizations,
                "original_switch_interval": original_switch_interval
            }
            
        except Exception as e:
            return {"error": f"خطأ في تحسين إعدادات Python: {e}"}
    
    def _count_temp_files(self) -> int:
        """عد الملفات المؤقتة"""
        count = 0
        temp_dirs = ["temp", "tmp", "__pycache__", ".pytest_cache"]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    count += len(files)
        
        return count
    
    def _generate_report(self, initial, memory_opt, cleanup, python_opt, final) -> Dict[str, Any]:
        """توليد التقرير النهائي"""
        execution_time = time.time() - self.start_time
        
        # حساب التحسينات
        memory_improvement = 0
        if "memory" in initial and "memory" in final:
            memory_improvement = initial["memory"]["current_mb"] - final["memory"]["current_mb"]
        
        report = {
            "optimization_summary": {
                "status": "completed",
                "execution_time_seconds": execution_time,
                "timestamp": datetime.now().isoformat()
            },
            "memory_optimization": {
                "improvement_mb": memory_improvement,
                "garbage_collected": memory_opt.get("total_collected", 0),
                "status": memory_opt.get("status", "unknown")
            },
            "file_cleanup": {
                "files_cleaned": cleanup.get("cleaned_files", 0),
                "space_freed_mb": cleanup.get("cleaned_size_mb", 0),
                "status": cleanup.get("status", "unknown")
            },
            "python_optimization": {
                "optimizations_applied": python_opt.get("optimizations", []),
                "status": python_opt.get("status", "unknown")
            },
            "before_after_comparison": {
                "initial_state": initial,
                "final_state": final
            },
            "recommendations": self._generate_recommendations(initial, final)
        }
        
        # حفظ التقرير
        report_file = f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"تم حفظ التقرير في: {report_file}")
        except Exception as e:
            logger.error(f"خطأ في حفظ التقرير: {e}")
        
        return report
    
    def _generate_recommendations(self, initial, final) -> List[str]:
        """توليد التوصيات"""
        recommendations = []
        
        # توصيات الذاكرة
        if "memory" in final:
            if final["memory"]["current_mb"] > 100:
                recommendations.append("فكر في تحسين استخدام الذاكرة أكثر")
            if final["memory"]["peak_mb"] > 500:
                recommendations.append("ذروة استخدام الذاكرة عالية، راجع الخوارزميات")
        
        # توصيات الخيوط
        if "threads" in final and final["threads"] > 20:
            recommendations.append("عدد الخيوط مرتفع، فكر في تحسين إدارة الخيوط")
        
        # توصيات عامة
        recommendations.extend([
            "قم بتشغيل التحسين بانتظام للحفاظ على الأداء",
            "راقب استخدام الذاكرة أثناء العمليات الثقيلة",
            "استخدم أدوات التحليل المتقدمة لتحسين أكثر تفصيلاً"
        ])
        
        return recommendations

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل أداة التحسين المبسطة...")
    
    optimizer = SimpleOptimizationRunner()
    results = optimizer.run_optimization()
    
    if "error" not in results:
        print("\n✅ تم إكمال التحسين بنجاح!")
        print(f"📊 تحسين الذاكرة: {results['memory_optimization']['improvement_mb']:.2f} MB")
        print(f"🧹 ملفات منظفة: {results['file_cleanup']['files_cleaned']}")
        print(f"⏱️ وقت التنفيذ: {results['optimization_summary']['execution_time_seconds']:.2f} ثانية")
    else:
        print(f"❌ فشل التحسين: {results['error']}")

if __name__ == "__main__":
    main()
