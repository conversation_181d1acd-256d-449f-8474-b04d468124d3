#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الفئة الأساسية لجلب المحتوى - Base Content Fetcher
تحتوي على الوظائف المشتركة لجميع منصات جلب المحتوى
"""

import logging
import requests
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from pathlib import Path
import json
from datetime import datetime, timedelta
import hashlib

from ..core.logger import activity_logger
from ..core.security import SecurityManager

class ContentItem:
    """عنصر محتوى واحد"""
    
    def __init__(self, platform: str, content_type: str, url: str, 
                 title: str = "", description: str = "", 
                 author: str = "", timestamp: datetime = None,
                 metadata: Dict[str, Any] = None):
        self.platform = platform
        self.content_type = content_type  # "story", "live", "post"
        self.url = url
        self.title = title
        self.description = description
        self.author = author
        self.timestamp = timestamp or datetime.now()
        self.metadata = metadata or {}
        
        # إنشاء معرف فريد
        self.id = self._generate_id()
        
        # حالة التحميل
        self.downloaded = False
        self.local_path = None
        self.download_error = None
    
    def _generate_id(self) -> str:
        """إنشاء معرف فريد للمحتوى"""
        content_string = f"{self.platform}_{self.url}_{self.timestamp.isoformat()}"
        return hashlib.md5(content_string.encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            "id": self.id,
            "platform": self.platform,
            "content_type": self.content_type,
            "url": self.url,
            "title": self.title,
            "description": self.description,
            "author": self.author,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata,
            "downloaded": self.downloaded,
            "local_path": self.local_path,
            "download_error": self.download_error
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContentItem':
        """إنشاء من قاموس"""
        item = cls(
            platform=data["platform"],
            content_type=data["content_type"],
            url=data["url"],
            title=data.get("title", ""),
            description=data.get("description", ""),
            author=data.get("author", ""),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            metadata=data.get("metadata", {})
        )
        item.id = data["id"]
        item.downloaded = data.get("downloaded", False)
        item.local_path = data.get("local_path")
        item.download_error = data.get("download_error")
        return item

class BaseFetcher(ABC):
    """الفئة الأساسية لجلب المحتوى"""
    
    def __init__(self, platform_name: str, config_manager, security_manager: SecurityManager):
        self.platform_name = platform_name
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.logger = logging.getLogger(f"{__name__}.{platform_name}")
        
        # إعدادات الجلسة
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # مجلد التحميل
        self.download_dir = Path.home() / ".smart_content_app" / "downloads" / platform_name
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # قاعدة بيانات المحتوى المحلية
        self.content_db_file = self.download_dir / "content_db.json"
        self.content_db = self._load_content_db()
        
        # إعدادات المراقبة
        self.monitoring_active = False
        self.last_check_time = None
        self.check_interval = 300  # 5 دقائق افتراضياً
        
        # إحصائيات
        self.stats = {
            "total_fetched": 0,
            "successful_downloads": 0,
            "failed_downloads": 0,
            "last_activity": None
        }
    
    def _load_content_db(self) -> Dict[str, ContentItem]:
        """تحميل قاعدة بيانات المحتوى المحلية"""
        try:
            if self.content_db_file.exists():
                with open(self.content_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return {
                        item_id: ContentItem.from_dict(item_data)
                        for item_id, item_data in data.items()
                    }
            return {}
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قاعدة بيانات المحتوى: {str(e)}")
            return {}
    
    def _save_content_db(self):
        """حفظ قاعدة بيانات المحتوى"""
        try:
            data = {
                item_id: item.to_dict()
                for item_id, item in self.content_db.items()
            }
            with open(self.content_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات المحتوى: {str(e)}")
    
    def is_authenticated(self) -> bool:
        """التحقق من حالة المصادقة"""
        account = self.config_manager.get_account(self.platform_name)
        return account.get("active", False) and bool(account.get("token"))
    
    def get_auth_headers(self) -> Dict[str, str]:
        """الحصول على رؤوس المصادقة"""
        account = self.config_manager.get_account(self.platform_name)
        token = account.get("token", "")
        
        if not token:
            raise ValueError(f"لا يوجد رمز مصادقة لمنصة {self.platform_name}")
        
        return self._build_auth_headers(token)
    
    @abstractmethod
    def _build_auth_headers(self, token: str) -> Dict[str, str]:
        """بناء رؤوس المصادقة (يجب تنفيذها في الفئات المشتقة)"""
        pass
    
    @abstractmethod
    def fetch_content(self, content_types: List[str] = None, 
                     limit: int = 10) -> List[ContentItem]:
        """جلب المحتوى (يجب تنفيذها في الفئات المشتقة)"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """اختبار الاتصال (يجب تنفيذها في الفئات المشتقة)"""
        pass
    
    def download_content(self, content_item: ContentItem) -> bool:
        """تحميل محتوى معين"""
        try:
            if content_item.downloaded and content_item.local_path:
                if Path(content_item.local_path).exists():
                    return True
            
            # إنشاء اسم ملف آمن
            safe_filename = self.security_manager.generate_secure_filename(
                f"{content_item.id}_{content_item.content_type}"
            )
            
            # تحديد مسار التحميل
            local_path = self.download_dir / safe_filename
            
            # تحميل الملف
            response = self.session.get(content_item.url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # تحديث معلومات المحتوى
            content_item.downloaded = True
            content_item.local_path = str(local_path)
            content_item.download_error = None
            
            # حفظ في قاعدة البيانات
            self.content_db[content_item.id] = content_item
            self._save_content_db()
            
            # تسجيل النشاط
            activity_logger.log_content_fetched(self.platform_name)
            self.stats["successful_downloads"] += 1
            
            self.logger.info(f"تم تحميل المحتوى: {content_item.id}")
            return True
            
        except Exception as e:
            content_item.download_error = str(e)
            self.stats["failed_downloads"] += 1
            activity_logger.log_error(self.platform_name, f"خطأ في التحميل: {str(e)}")
            self.logger.error(f"خطأ في تحميل المحتوى {content_item.id}: {str(e)}")
            return False
    
    def start_monitoring(self, content_types: List[str] = None, 
                        check_interval: int = 300):
        """بدء مراقبة المحتوى"""
        self.monitoring_active = True
        self.check_interval = check_interval
        self.last_check_time = datetime.now()
        
        self.logger.info(f"بدء مراقبة {self.platform_name}")
    
    def stop_monitoring(self):
        """إيقاف مراقبة المحتوى"""
        self.monitoring_active = False
        self.logger.info(f"إيقاف مراقبة {self.platform_name}")
    
    def check_for_new_content(self, content_types: List[str] = None) -> List[ContentItem]:
        """فحص المحتوى الجديد"""
        if not self.monitoring_active:
            return []
        
        try:
            # التحقق من الأمان
            if not self.security_manager.is_safe_to_continue():
                self.logger.warning("تم إيقاف المراقبة لأسباب أمنية")
                self.stop_monitoring()
                return []
            
            # جلب المحتوى الجديد
            new_content = self.fetch_content(content_types)
            
            # فلترة المحتوى الجديد فقط
            truly_new = []
            for item in new_content:
                if item.id not in self.content_db:
                    truly_new.append(item)
                    self.content_db[item.id] = item
            
            if truly_new:
                self._save_content_db()
                self.logger.info(f"تم العثور على {len(truly_new)} محتوى جديد")
            
            self.last_check_time = datetime.now()
            self.stats["last_activity"] = self.last_check_time.isoformat()
            
            return truly_new
            
        except Exception as e:
            activity_logger.log_error(self.platform_name, f"خطأ في فحص المحتوى: {str(e)}")
            self.logger.error(f"خطأ في فحص المحتوى الجديد: {str(e)}")
            return []
    
    def get_content_history(self, days: int = 7) -> List[ContentItem]:
        """الحصول على تاريخ المحتوى"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        return [
            item for item in self.content_db.values()
            if item.timestamp >= cutoff_date
        ]
    
    def cleanup_old_content(self, days: int = 30):
        """تنظيف المحتوى القديم"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            old_items = []
            
            for item_id, item in list(self.content_db.items()):
                if item.timestamp < cutoff_date:
                    old_items.append(item)
                    
                    # حذف الملف المحلي
                    if item.local_path and Path(item.local_path).exists():
                        self.security_manager.secure_delete_file(Path(item.local_path))
                    
                    # إزالة من قاعدة البيانات
                    del self.content_db[item_id]
            
            if old_items:
                self._save_content_db()
                self.logger.info(f"تم تنظيف {len(old_items)} عنصر قديم")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المحتوى القديم: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات"""
        return {
            **self.stats,
            "total_stored": len(self.content_db),
            "monitoring_active": self.monitoring_active,
            "last_check": self.last_check_time.isoformat() if self.last_check_time else None,
            "platform": self.platform_name
        }
