import gc
import os
import time
import tracemalloc

def test_optimization():
    print("Starting optimization test...")
    
    # Start memory tracking
    tracemalloc.start()
    
    # Get initial memory
    current, peak = tracemalloc.get_traced_memory()
    print(f"Initial memory: {current / 1024 / 1024:.2f} MB")
    
    # Run garbage collection
    collected = gc.collect()
    print(f"Garbage collected: {collected} objects")
    
    # Get final memory
    current, peak = tracemalloc.get_traced_memory()
    print(f"Final memory: {current / 1024 / 1024:.2f} MB")
    
    # Clean some temp files
    cleaned = 0
    if os.path.exists("__pycache__"):
        for root, dirs, files in os.walk("__pycache__"):
            for file in files:
                if file.endswith('.pyc'):
                    try:
                        os.remove(os.path.join(root, file))
                        cleaned += 1
                    except:
                        pass
    
    print(f"Cleaned {cleaned} cache files")
    print("Optimization test completed!")

if __name__ == "__main__":
    test_optimization()
