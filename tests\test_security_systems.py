#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات أنظمة الحماية والأمان
Tests for Security and Protection Systems
"""

import pytest
import os
import tempfile
import json
import hashlib
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import time

# استيراد أنظمة الحماية
from src.security.advanced_encryption import AdvancedEncryptionSystem, EncryptionLevel
from src.security.advanced_authentication import AdvancedAuthenticationSystem, AuthenticationMethod
from src.security.malware_protection import MalwareProtectionSystem, ThreatLevel
from src.security.secure_backup import SecureBackupSystem, BackupType
from src.security.tamper_protection import TamperProtectionSystem, ProtectionLevel, IntegrityStatus

@pytest.mark.security
class TestAdvancedEncryption:
    """اختبارات نظام التشفير المتقدم"""
    
    @pytest.fixture
    def encryption_system(self, temp_dir):
        """إنشاء نظام التشفير للاختبار"""
        return AdvancedEncryptionSystem(str(temp_dir))
    
    @pytest.fixture
    def sample_data(self):
        """بيانات تجريبية للتشفير"""
        return "هذا نص تجريبي للتشفير والفك".encode('utf-8')
    
    def test_initialization(self, encryption_system):
        """اختبار تهيئة نظام التشفير"""
        assert encryption_system is not None
        assert hasattr(encryption_system, 'master_key')
        assert hasattr(encryption_system, 'encryption_keys')
    
    def test_generate_encryption_key(self, encryption_system):
        """اختبار إنتاج مفتاح التشفير"""
        key = encryption_system.generate_encryption_key(EncryptionLevel.STANDARD)
        assert key is not None
        assert len(key) > 0
    
    def test_encrypt_decrypt_basic(self, encryption_system, sample_data):
        """اختبار التشفير والفك الأساسي"""
        # تشفير البيانات
        encrypted_data = encryption_system.encrypt_data(sample_data, EncryptionLevel.BASIC)
        assert encrypted_data is not None
        assert encrypted_data != sample_data
        
        # فك التشفير
        decrypted_data = encryption_system.decrypt_data(encrypted_data, EncryptionLevel.BASIC)
        assert decrypted_data == sample_data
    
    def test_encrypt_decrypt_advanced(self, encryption_system, sample_data):
        """اختبار التشفير والفك المتقدم"""
        encrypted_data = encryption_system.encrypt_data(sample_data, EncryptionLevel.ADVANCED)
        assert encrypted_data is not None
        assert encrypted_data != sample_data
        
        decrypted_data = encryption_system.decrypt_data(encrypted_data, EncryptionLevel.ADVANCED)
        assert decrypted_data == sample_data
    
    def test_encrypt_file(self, encryption_system, temp_dir):
        """اختبار تشفير الملفات"""
        # إنشاء ملف تجريبي
        test_file = temp_dir / "test_file.txt"
        test_content = "محتوى الملف التجريبي"
        test_file.write_text(test_content, encoding='utf-8')
        
        # تشفير الملف
        encrypted_file = encryption_system.encrypt_file(str(test_file), EncryptionLevel.STANDARD)
        assert encrypted_file is not None
        assert Path(encrypted_file).exists()
        
        # فك تشفير الملف
        decrypted_file = encryption_system.decrypt_file(encrypted_file, EncryptionLevel.STANDARD)
        assert decrypted_file is not None
        
        # التحقق من المحتوى
        decrypted_content = Path(decrypted_file).read_text(encoding='utf-8')
        assert decrypted_content == test_content
    
    def test_key_rotation(self, encryption_system):
        """اختبار تدوير المفاتيح"""
        old_key = encryption_system.get_current_key(EncryptionLevel.STANDARD)
        
        # تدوير المفتاح
        new_key = encryption_system.rotate_encryption_key(EncryptionLevel.STANDARD)
        assert new_key is not None
        assert new_key != old_key
        
        current_key = encryption_system.get_current_key(EncryptionLevel.STANDARD)
        assert current_key == new_key
    
    def test_encryption_performance(self, encryption_system):
        """اختبار أداء التشفير"""
        large_data = b"A" * 1024 * 1024  # 1 MB
        
        start_time = time.time()
        encrypted_data = encryption_system.encrypt_data(large_data, EncryptionLevel.BASIC)
        encryption_time = time.time() - start_time
        
        assert encrypted_data is not None
        assert encryption_time < 5.0  # يجب أن يكون أقل من 5 ثوان
        
        start_time = time.time()
        decrypted_data = encryption_system.decrypt_data(encrypted_data, EncryptionLevel.BASIC)
        decryption_time = time.time() - start_time
        
        assert decrypted_data == large_data
        assert decryption_time < 5.0

@pytest.mark.security
class TestAdvancedAuthentication:
    """اختبارات نظام المصادقة المتقدم"""
    
    @pytest.fixture
    def auth_system(self, temp_dir):
        """إنشاء نظام المصادقة للاختبار"""
        return AdvancedAuthenticationSystem(str(temp_dir))
    
    @pytest.fixture
    def test_user(self):
        """مستخدم تجريبي"""
        return {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "SecurePassword123!",
            "phone": "+1234567890"
        }
    
    def test_initialization(self, auth_system):
        """اختبار تهيئة نظام المصادقة"""
        assert auth_system is not None
        assert hasattr(auth_system, 'users_database')
        assert hasattr(auth_system, 'sessions')
    
    def test_register_user(self, auth_system, test_user):
        """اختبار تسجيل مستخدم جديد"""
        result = auth_system.register_user(
            test_user["username"],
            test_user["email"],
            test_user["password"],
            test_user["phone"]
        )
        assert result == True
        
        # التحقق من وجود المستخدم
        user_exists = auth_system.user_exists(test_user["username"])
        assert user_exists == True
    
    def test_authenticate_password(self, auth_system, test_user):
        """اختبار المصادقة بكلمة المرور"""
        # تسجيل المستخدم أولاً
        auth_system.register_user(
            test_user["username"],
            test_user["email"],
            test_user["password"],
            test_user["phone"]
        )
        
        # اختبار المصادقة الصحيحة
        result = auth_system.authenticate(
            test_user["username"],
            test_user["password"],
            AuthenticationMethod.PASSWORD
        )
        assert result["success"] == True
        assert "session_token" in result
        
        # اختبار المصادقة الخاطئة
        result = auth_system.authenticate(
            test_user["username"],
            "wrong_password",
            AuthenticationMethod.PASSWORD
        )
        assert result["success"] == False
    
    @patch('pyotp.TOTP.verify')
    def test_authenticate_2fa(self, mock_verify, auth_system, test_user):
        """اختبار المصادقة الثنائية"""
        mock_verify.return_value = True
        
        # تسجيل المستخدم وتفعيل 2FA
        auth_system.register_user(
            test_user["username"],
            test_user["email"],
            test_user["password"],
            test_user["phone"]
        )
        auth_system.enable_2fa(test_user["username"])
        
        # اختبار المصادقة الثنائية
        result = auth_system.authenticate_2fa(test_user["username"], "123456")
        assert result["success"] == True
    
    def test_session_management(self, auth_system, test_user):
        """اختبار إدارة الجلسات"""
        # تسجيل المستخدم والمصادقة
        auth_system.register_user(
            test_user["username"],
            test_user["email"],
            test_user["password"],
            test_user["phone"]
        )
        
        auth_result = auth_system.authenticate(
            test_user["username"],
            test_user["password"],
            AuthenticationMethod.PASSWORD
        )
        session_token = auth_result["session_token"]
        
        # التحقق من صحة الجلسة
        is_valid = auth_system.validate_session(session_token)
        assert is_valid == True
        
        # إنهاء الجلسة
        logout_result = auth_system.logout(session_token)
        assert logout_result == True
        
        # التحقق من انتهاء الجلسة
        is_valid = auth_system.validate_session(session_token)
        assert is_valid == False
    
    def test_password_strength_validation(self, auth_system):
        """اختبار التحقق من قوة كلمة المرور"""
        weak_passwords = ["123", "password", "abc123"]
        strong_passwords = ["SecurePass123!", "MyStr0ng#P@ssw0rd", "C0mpl3x!P@ssw0rd"]
        
        for password in weak_passwords:
            is_strong = auth_system.validate_password_strength(password)
            assert is_strong == False
        
        for password in strong_passwords:
            is_strong = auth_system.validate_password_strength(password)
            assert is_strong == True
    
    def test_account_lockout(self, auth_system, test_user):
        """اختبار قفل الحساب بعد محاولات فاشلة"""
        # تسجيل المستخدم
        auth_system.register_user(
            test_user["username"],
            test_user["email"],
            test_user["password"],
            test_user["phone"]
        )
        
        # محاولات مصادقة فاشلة متعددة
        for _ in range(5):
            auth_system.authenticate(
                test_user["username"],
                "wrong_password",
                AuthenticationMethod.PASSWORD
            )
        
        # التحقق من قفل الحساب
        is_locked = auth_system.is_account_locked(test_user["username"])
        assert is_locked == True
        
        # محاولة المصادقة بكلمة المرور الصحيحة (يجب أن تفشل)
        result = auth_system.authenticate(
            test_user["username"],
            test_user["password"],
            AuthenticationMethod.PASSWORD
        )
        assert result["success"] == False

@pytest.mark.security
class TestMalwareProtection:
    """اختبارات نظام الحماية من البرمجيات الخبيثة"""
    
    @pytest.fixture
    def malware_system(self, temp_dir):
        """إنشاء نظام الحماية من البرمجيات الخبيثة"""
        return MalwareProtectionSystem(str(temp_dir))
    
    @pytest.fixture
    def sample_files(self, temp_dir):
        """ملفات تجريبية للفحص"""
        files = {}
        
        # ملف نظيف
        clean_file = temp_dir / "clean_file.txt"
        clean_file.write_text("هذا ملف نظيف وآمن")
        files["clean"] = str(clean_file)
        
        # ملف مشبوه (محاكاة)
        suspicious_file = temp_dir / "suspicious_file.exe"
        suspicious_file.write_bytes(b"MZ\x90\x00" + b"suspicious_content" * 100)
        files["suspicious"] = str(suspicious_file)
        
        return files
    
    def test_initialization(self, malware_system):
        """اختبار تهيئة نظام الحماية"""
        assert malware_system is not None
        assert hasattr(malware_system, 'virus_database')
        assert hasattr(malware_system, 'quarantine_dir')
    
    def test_scan_file_clean(self, malware_system, sample_files):
        """اختبار فحص ملف نظيف"""
        result = malware_system.scan_file(sample_files["clean"])
        
        assert result is not None
        assert result["is_clean"] == True
        assert result["threat_level"] == ThreatLevel.NONE
    
    def test_scan_file_suspicious(self, malware_system, sample_files):
        """اختبار فحص ملف مشبوه"""
        with patch.object(malware_system, '_check_file_signature') as mock_check:
            mock_check.return_value = {"is_malware": True, "threat_type": "trojan"}
            
            result = malware_system.scan_file(sample_files["suspicious"])
            
            assert result is not None
            assert result["is_clean"] == False
            assert result["threat_level"] in [ThreatLevel.MEDIUM, ThreatLevel.HIGH, ThreatLevel.CRITICAL]
    
    def test_quarantine_file(self, malware_system, sample_files):
        """اختبار عزل الملفات المشبوهة"""
        file_path = sample_files["suspicious"]
        
        # عزل الملف
        quarantine_result = malware_system.quarantine_file(file_path, "test_threat")
        assert quarantine_result == True
        
        # التحقق من عدم وجود الملف الأصلي
        assert not Path(file_path).exists()
        
        # التحقق من وجود الملف في الحجر الصحي
        quarantined_files = malware_system.get_quarantined_files()
        assert len(quarantined_files) > 0
    
    def test_real_time_monitoring(self, malware_system, temp_dir):
        """اختبار المراقبة في الوقت الفعلي"""
        # بدء المراقبة
        monitoring_started = malware_system.start_real_time_monitoring(str(temp_dir))
        assert monitoring_started == True
        
        # إنشاء ملف جديد
        new_file = temp_dir / "new_file.txt"
        new_file.write_text("ملف جديد للمراقبة")
        
        # انتظار قصير للمراقبة
        time.sleep(1)
        
        # إيقاف المراقبة
        monitoring_stopped = malware_system.stop_real_time_monitoring()
        assert monitoring_stopped == True
    
    def test_update_virus_database(self, malware_system):
        """اختبار تحديث قاعدة بيانات الفيروسات"""
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "signatures": [
                    {"hash": "abc123", "name": "test_virus", "threat_level": "high"}
                ],
                "version": "2024.01.01"
            }
            mock_get.return_value = mock_response
            
            update_result = malware_system.update_virus_database()
            assert update_result == True
    
    def test_scan_directory(self, malware_system, temp_dir, sample_files):
        """اختبار فحص مجلد كامل"""
        scan_results = malware_system.scan_directory(str(temp_dir))
        
        assert scan_results is not None
        assert "total_files" in scan_results
        assert "clean_files" in scan_results
        assert "infected_files" in scan_results
        assert scan_results["total_files"] >= 2  # على الأقل الملفين التجريبيين

@pytest.mark.security
class TestSecureBackup:
    """اختبارات نظام النسخ الاحتياطي الآمن"""

    @pytest.fixture
    def backup_system(self, temp_dir):
        """إنشاء نظام النسخ الاحتياطي"""
        return SecureBackupSystem(str(temp_dir))

    @pytest.fixture
    def test_data_files(self, temp_dir):
        """ملفات بيانات تجريبية للنسخ الاحتياطي"""
        files = []
        for i in range(3):
            file_path = temp_dir / f"data_file_{i}.txt"
            file_path.write_text(f"محتوى الملف رقم {i}")
            files.append(str(file_path))
        return files

    def test_initialization(self, backup_system):
        """اختبار تهيئة نظام النسخ الاحتياطي"""
        assert backup_system is not None
        assert hasattr(backup_system, 'backup_storage')
        assert hasattr(backup_system, 'encryption_system')

    def test_create_full_backup(self, backup_system, test_data_files):
        """اختبار إنشاء نسخة احتياطية كاملة"""
        source_dir = Path(test_data_files[0]).parent

        backup_result = backup_system.create_backup(
            str(source_dir),
            BackupType.FULL,
            encrypt=True
        )

        assert backup_result is not None
        assert "backup_id" in backup_result
        assert "backup_path" in backup_result
        assert backup_result["success"] == True

    def test_create_incremental_backup(self, backup_system, test_data_files):
        """اختبار إنشاء نسخة احتياطية تزايدية"""
        source_dir = Path(test_data_files[0]).parent

        # إنشاء نسخة احتياطية كاملة أولاً
        full_backup = backup_system.create_backup(
            str(source_dir),
            BackupType.FULL,
            encrypt=True
        )

        # تعديل ملف
        new_file = source_dir / "new_file.txt"
        new_file.write_text("ملف جديد")

        # إنشاء نسخة احتياطية تزايدية
        incremental_backup = backup_system.create_backup(
            str(source_dir),
            BackupType.INCREMENTAL,
            encrypt=True
        )

        assert incremental_backup is not None
        assert incremental_backup["success"] == True
        assert incremental_backup["backup_id"] != full_backup["backup_id"]

    def test_restore_backup(self, backup_system, test_data_files, temp_dir):
        """اختبار استعادة النسخة الاحتياطية"""
        source_dir = Path(test_data_files[0]).parent
        restore_dir = temp_dir / "restore_test"
        restore_dir.mkdir()

        # إنشاء نسخة احتياطية
        backup_result = backup_system.create_backup(
            str(source_dir),
            BackupType.FULL,
            encrypt=True
        )

        # استعادة النسخة الاحتياطية
        restore_result = backup_system.restore_backup(
            backup_result["backup_id"],
            str(restore_dir)
        )

        assert restore_result == True

        # التحقق من استعادة الملفات
        restored_files = list(restore_dir.glob("*.txt"))
        assert len(restored_files) >= len(test_data_files)

    def test_backup_verification(self, backup_system, test_data_files):
        """اختبار التحقق من سلامة النسخة الاحتياطية"""
        source_dir = Path(test_data_files[0]).parent

        backup_result = backup_system.create_backup(
            str(source_dir),
            BackupType.FULL,
            encrypt=True
        )

        # التحقق من سلامة النسخة الاحتياطية
        verification_result = backup_system.verify_backup(backup_result["backup_id"])

        assert verification_result is not None
        assert verification_result["is_valid"] == True
        assert "checksum_match" in verification_result

    def test_scheduled_backup(self, backup_system, test_data_files):
        """اختبار النسخ الاحتياطي المجدول"""
        source_dir = Path(test_data_files[0]).parent

        # جدولة نسخة احتياطية يومية
        schedule_result = backup_system.schedule_backup(
            str(source_dir),
            BackupType.INCREMENTAL,
            schedule="daily",
            time="02:00"
        )

        assert schedule_result is not None
        assert "schedule_id" in schedule_result

        # الحصول على النسخ المجدولة
        scheduled_backups = backup_system.get_scheduled_backups()
        assert len(scheduled_backups) > 0

    def test_backup_cleanup(self, backup_system, test_data_files):
        """اختبار تنظيف النسخ الاحتياطية القديمة"""
        source_dir = Path(test_data_files[0]).parent

        # إنشاء عدة نسخ احتياطية
        backup_ids = []
        for i in range(5):
            backup_result = backup_system.create_backup(
                str(source_dir),
                BackupType.FULL,
                encrypt=True
            )
            backup_ids.append(backup_result["backup_id"])

        # تنظيف النسخ القديمة (الاحتفاظ بـ 3 فقط)
        cleanup_result = backup_system.cleanup_old_backups(keep_count=3)

        assert cleanup_result == True

        # التحقق من عدد النسخ المتبقية
        remaining_backups = backup_system.list_backups()
        assert len(remaining_backups) <= 3

@pytest.mark.security
class TestTamperProtection:
    """اختبارات نظام الحماية من التلاعب"""

    @pytest.fixture
    def tamper_system(self, temp_dir):
        """إنشاء نظام الحماية من التلاعب"""
        return TamperProtectionSystem(str(temp_dir))

    @pytest.fixture
    def protected_files(self, temp_dir):
        """ملفات محمية للاختبار"""
        files = []
        for i in range(3):
            file_path = temp_dir / f"protected_file_{i}.txt"
            file_path.write_text(f"محتوى محمي رقم {i}")
            files.append(str(file_path))
        return files

    def test_initialization(self, tamper_system):
        """اختبار تهيئة نظام الحماية من التلاعب"""
        assert tamper_system is not None
        assert hasattr(tamper_system, 'protected_files')
        assert hasattr(tamper_system, 'integrity_database')

    def test_add_file_protection(self, tamper_system, protected_files):
        """اختبار إضافة حماية للملفات"""
        for file_path in protected_files:
            result = tamper_system.add_file_protection(
                file_path,
                ProtectionLevel.HIGH
            )
            assert result == True

        # التحقق من إضافة الملفات للحماية
        protected_list = tamper_system.get_protected_files()
        assert len(protected_list) >= len(protected_files)

    def test_calculate_file_integrity(self, tamper_system, protected_files):
        """اختبار حساب سلامة الملفات"""
        file_path = protected_files[0]

        # إضافة الملف للحماية
        tamper_system.add_file_protection(file_path, ProtectionLevel.HIGH)

        # حساب سلامة الملف
        integrity_hash = tamper_system.calculate_file_integrity(file_path)
        assert integrity_hash is not None
        assert len(integrity_hash) > 0

    def test_detect_file_tampering(self, tamper_system, protected_files):
        """اختبار اكتشاف التلاعب بالملفات"""
        file_path = protected_files[0]

        # إضافة الملف للحماية
        tamper_system.add_file_protection(file_path, ProtectionLevel.HIGH)

        # التحقق من سلامة الملف (يجب أن يكون سليماً)
        status = tamper_system.check_file_integrity(file_path)
        assert status == IntegrityStatus.INTACT

        # تعديل الملف (محاكاة التلاعب)
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write("\nمحتوى معدل")

        # التحقق من اكتشاف التلاعب
        status = tamper_system.check_file_integrity(file_path)
        assert status == IntegrityStatus.MODIFIED

    def test_monitor_directory(self, tamper_system, temp_dir):
        """اختبار مراقبة المجلد"""
        # بدء مراقبة المجلد
        monitoring_result = tamper_system.monitor_directory(
            str(temp_dir),
            ProtectionLevel.MEDIUM
        )
        assert monitoring_result == True

        # إنشاء ملف جديد
        new_file = temp_dir / "new_monitored_file.txt"
        new_file.write_text("ملف جديد تحت المراقبة")

        # انتظار قصير للمراقبة
        time.sleep(1)

        # التحقق من اكتشاف الملف الجديد
        events = tamper_system.get_monitoring_events()
        assert len(events) > 0

    def test_restore_file_integrity(self, tamper_system, protected_files, temp_dir):
        """اختبار استعادة سلامة الملف"""
        file_path = protected_files[0]
        backup_dir = temp_dir / "backups"
        backup_dir.mkdir()

        # إضافة الملف للحماية مع نسخة احتياطية
        tamper_system.add_file_protection(file_path, ProtectionLevel.HIGH)
        tamper_system.create_integrity_backup(file_path, str(backup_dir))

        # تعديل الملف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("محتوى معدل بالكامل")

        # استعادة سلامة الملف
        restore_result = tamper_system.restore_file_integrity(file_path)
        assert restore_result == True

        # التحقق من استعادة المحتوى الأصلي
        status = tamper_system.check_file_integrity(file_path)
        assert status == IntegrityStatus.INTACT

    def test_generate_integrity_report(self, tamper_system, protected_files):
        """اختبار إنشاء تقرير السلامة"""
        # إضافة ملفات للحماية
        for file_path in protected_files:
            tamper_system.add_file_protection(file_path, ProtectionLevel.HIGH)

        # تعديل أحد الملفات
        with open(protected_files[0], 'a', encoding='utf-8') as f:
            f.write("\nتعديل")

        # إنشاء تقرير السلامة
        report = tamper_system.generate_integrity_report()

        assert report is not None
        assert "total_files" in report
        assert "intact_files" in report
        assert "modified_files" in report
        assert report["modified_files"] > 0

@pytest.mark.integration
class TestSecuritySystemsIntegration:
    """اختبارات التكامل بين أنظمة الأمان"""

    @pytest.fixture
    def security_systems(self, temp_dir):
        """إنشاء جميع أنظمة الأمان"""
        return {
            "encryption": AdvancedEncryptionSystem(str(temp_dir)),
            "authentication": AdvancedAuthenticationSystem(str(temp_dir)),
            "malware": MalwareProtectionSystem(str(temp_dir)),
            "backup": SecureBackupSystem(str(temp_dir)),
            "tamper": TamperProtectionSystem(str(temp_dir))
        }

    def test_complete_security_workflow(self, security_systems, temp_dir):
        """اختبار سير عمل الأمان الكامل"""
        encryption = security_systems["encryption"]
        auth = security_systems["authentication"]
        malware = security_systems["malware"]
        backup = security_systems["backup"]
        tamper = security_systems["tamper"]

        # 1. تسجيل مستخدم ومصادقة
        user_registered = auth.register_user(
            "test_user", "<EMAIL>", "SecurePass123!", "+1234567890"
        )
        assert user_registered == True

        auth_result = auth.authenticate(
            "test_user", "SecurePass123!", AuthenticationMethod.PASSWORD
        )
        assert auth_result["success"] == True

        # 2. إنشاء ملف وتشفيره
        test_file = temp_dir / "secure_file.txt"
        test_file.write_text("محتوى سري")

        encrypted_file = encryption.encrypt_file(str(test_file), EncryptionLevel.ADVANCED)
        assert encrypted_file is not None

        # 3. فحص الملف من البرمجيات الخبيثة
        scan_result = malware.scan_file(encrypted_file)
        assert scan_result["is_clean"] == True

        # 4. إضافة حماية من التلاعب
        tamper_result = tamper.add_file_protection(encrypted_file, ProtectionLevel.HIGH)
        assert tamper_result == True

        # 5. إنشاء نسخة احتياطية آمنة
        backup_result = backup.create_backup(
            str(temp_dir), BackupType.FULL, encrypt=True
        )
        assert backup_result["success"] == True

        # 6. التحقق من سلامة النظام
        integrity_status = tamper.check_file_integrity(encrypted_file)
        assert integrity_status == IntegrityStatus.INTACT

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
