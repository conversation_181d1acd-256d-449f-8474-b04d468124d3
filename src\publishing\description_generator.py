#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الوصف - Description Generator
يقوم بإنشاء أوصاف جذابة ومحسنة للمحتوى
"""

import logging
import json
import os
import re
import random
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

@dataclass
class PostDescription:
    """وصف المنشور"""
    title: str
    description: str
    call_to_action: str
    hashtags: List[str]
    emoji_enhanced: str
    character_count: int
    platform: str
    category: str
    engagement_score: float
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "title": self.title,
            "description": self.description,
            "call_to_action": self.call_to_action,
            "hashtags": self.hashtags,
            "emoji_enhanced": self.emoji_enhanced,
            "character_count": self.character_count,
            "platform": self.platform,
            "category": self.category,
            "engagement_score": self.engagement_score,
            "created_at": self.created_at.isoformat()
        }

class DescriptionGenerator:
    """مولد الوصف الذكي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المولد
        self.settings = self._load_generator_settings()
        
        # قوالب الوصف
        self.description_templates = self._load_description_templates()
        
        # قوالب العناوين
        self.title_templates = self._load_title_templates()
        
        # دعوات للعمل
        self.call_to_actions = self._load_call_to_actions()
        
        # الرموز التعبيرية
        self.emojis = self._load_emojis()
        
        # إحصائيات الأداء
        self.performance_stats = self._load_performance_stats()
        
        # مسار ملفات البيانات
        self.data_dir = os.path.join(
            self.config_manager.get_app_data_dir(),
            "descriptions"
        )
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _load_generator_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المولد"""
        try:
            return self.config_manager.get_setting("publishing_settings", "description_generator", {
                "max_description_length": {
                    "tiktok": 2200,
                    "instagram": 2200,
                    "youtube": 5000,
                    "twitter": 280
                },
                "min_description_length": 50,
                "include_emojis": True,
                "emoji_density": 0.1,  # نسبة الرموز التعبيرية
                "include_call_to_action": True,
                "arabic_content_ratio": 0.7,  # نسبة المحتوى العربي
                "use_trending_phrases": True,
                "personalization_level": "medium",  # low, medium, high
                "include_questions": True,
                "max_hashtags_in_description": 5
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المولد: {str(e)}")
            return {"max_description_length": {"tiktok": 2200}, "min_description_length": 50}
    
    def _load_description_templates(self) -> Dict[str, List[str]]:
        """تحميل قوالب الوصف"""
        try:
            templates_file = os.path.join(self.data_dir, "description_templates.json")
            
            if os.path.exists(templates_file):
                with open(templates_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return self._create_default_templates()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قوالب الوصف: {str(e)}")
            return self._create_default_templates()
    
    def _create_default_templates(self) -> Dict[str, List[str]]:
        """إنشاء قوالب افتراضية"""
        return {
            "comedy": {
                "arabic": [
                    "😂 {title} - مقطع مضحك جداً!",
                    "🤣 شاهد هذا المقطع المضحك: {title}",
                    "😆 لن تصدق ما حدث! {title}",
                    "🎭 كوميديا خالصة: {title}",
                    "😄 مقطع سيجعلك تضحك بلا توقف: {title}"
                ],
                "english": [
                    "😂 {title} - This is hilarious!",
                    "🤣 You won't believe this: {title}",
                    "😆 Comedy gold: {title}",
                    "🎭 Pure entertainment: {title}",
                    "😄 This will make your day: {title}"
                ]
            },
            "viral": {
                "arabic": [
                    "🔥 {title} - هذا المقطع أصبح فيروسي!",
                    "⚡ شاهد قبل أن ينتشر أكثر: {title}",
                    "🌟 مقطع يستحق المشاهدة: {title}",
                    "💥 انتشر بسرعة البرق: {title}",
                    "🚀 ترند جديد: {title}"
                ],
                "english": [
                    "🔥 {title} - Going viral!",
                    "⚡ Watch before it explodes: {title}",
                    "🌟 Must-watch content: {title}",
                    "💥 Trending now: {title}",
                    "🚀 New viral sensation: {title}"
                ]
            },
            "entertainment": {
                "arabic": [
                    "🎬 {title} - محتوى ترفيهي رائع",
                    "🎪 استمتع بمشاهدة: {title}",
                    "🎨 إبداع وتسلية: {title}",
                    "🎵 محتوى ممتع: {title}",
                    "🎯 تسلية خالصة: {title}"
                ],
                "english": [
                    "🎬 {title} - Amazing entertainment",
                    "🎪 Enjoy watching: {title}",
                    "🎨 Creative and fun: {title}",
                    "🎵 Pure entertainment: {title}",
                    "🎯 Fun content: {title}"
                ]
            },
            "lifestyle": {
                "arabic": [
                    "✨ {title} - من يومياتي",
                    "🌸 شاركوني تجربتي: {title}",
                    "💫 لحظات من الحياة: {title}",
                    "🌺 نصائح وتجارب: {title}",
                    "🦋 رحلة الحياة: {title}"
                ],
                "english": [
                    "✨ {title} - From my daily life",
                    "🌸 Sharing my experience: {title}",
                    "💫 Life moments: {title}",
                    "🌺 Tips and experiences: {title}",
                    "🦋 Life journey: {title}"
                ]
            },
            "technology": {
                "arabic": [
                    "💻 {title} - تقنية جديدة",
                    "🔧 شرح تقني: {title}",
                    "⚙️ معلومات تقنية: {title}",
                    "📱 تكنولوجيا حديثة: {title}",
                    "🚀 ابتكار تقني: {title}"
                ],
                "english": [
                    "💻 {title} - New technology",
                    "🔧 Tech explanation: {title}",
                    "⚙️ Technical info: {title}",
                    "📱 Modern technology: {title}",
                    "🚀 Tech innovation: {title}"
                ]
            }
        }
    
    def _load_title_templates(self) -> Dict[str, List[str]]:
        """تحميل قوالب العناوين"""
        return {
            "question": {
                "arabic": [
                    "هل تعرف {topic}؟",
                    "ماذا لو {scenario}؟",
                    "كيف يمكن {action}؟",
                    "لماذا {reason}؟",
                    "متى {timing}؟"
                ],
                "english": [
                    "Do you know {topic}?",
                    "What if {scenario}?",
                    "How to {action}?",
                    "Why {reason}?",
                    "When {timing}?"
                ]
            },
            "exclamation": {
                "arabic": [
                    "لا يصدق! {content}",
                    "مذهل! {content}",
                    "رائع! {content}",
                    "لن تصدق {content}",
                    "شاهد {content}"
                ],
                "english": [
                    "Unbelievable! {content}",
                    "Amazing! {content}",
                    "Awesome! {content}",
                    "You won't believe {content}",
                    "Watch {content}"
                ]
            },
            "how_to": {
                "arabic": [
                    "كيفية {action}",
                    "طريقة {method}",
                    "خطوات {steps}",
                    "دليل {guide}",
                    "تعلم {skill}"
                ],
                "english": [
                    "How to {action}",
                    "Way to {method}",
                    "Steps to {steps}",
                    "Guide to {guide}",
                    "Learn {skill}"
                ]
            }
        }
    
    def _load_call_to_actions(self) -> Dict[str, List[str]]:
        """تحميل دعوات العمل"""
        return {
            "engagement": {
                "arabic": [
                    "شاركوني رأيكم في التعليقات! 💬",
                    "لا تنسوا اللايك والمتابعة! ❤️",
                    "شاركوا المقطع مع أصدقائكم! 🔄",
                    "ما رأيكم؟ اكتبوا في التعليقات! 📝",
                    "تابعوني للمزيد من المحتوى! 🔔"
                ],
                "english": [
                    "Share your thoughts in the comments! 💬",
                    "Don't forget to like and follow! ❤️",
                    "Share this with your friends! 🔄",
                    "What do you think? Comment below! 📝",
                    "Follow me for more content! 🔔"
                ]
            },
            "subscribe": {
                "arabic": [
                    "اشتركوا في القناة! 🔔",
                    "فعلوا الجرس للتنبيهات! 🔔",
                    "تابعوني لمحتوى يومي! 📱",
                    "انضموا لعائلتنا! 👨‍👩‍👧‍👦",
                    "كونوا جزءاً من المجتمع! 🤝"
                ],
                "english": [
                    "Subscribe to the channel! 🔔",
                    "Turn on notifications! 🔔",
                    "Follow me for daily content! 📱",
                    "Join our family! 👨‍👩‍👧‍👦",
                    "Be part of the community! 🤝"
                ]
            },
            "share": {
                "arabic": [
                    "شاركوا المقطع! 🔄",
                    "انشروا الفيديو! 📤",
                    "أرسلوه لأصدقائكم! 👥",
                    "شاركوا الحب! ❤️",
                    "انشروا الإيجابية! ✨"
                ],
                "english": [
                    "Share this video! 🔄",
                    "Spread the word! 📤",
                    "Send it to your friends! 👥",
                    "Share the love! ❤️",
                    "Spread positivity! ✨"
                ]
            }
        }
    
    def _load_emojis(self) -> Dict[str, List[str]]:
        """تحميل الرموز التعبيرية"""
        return {
            "emotions": {
                "happy": ["😊", "😄", "😃", "😁", "🙂", "😌", "😍", "🥰", "😘"],
                "funny": ["😂", "🤣", "😆", "😄", "😁", "🤪", "😜", "😝", "🤭"],
                "excited": ["🤩", "😍", "🥳", "🎉", "🔥", "⚡", "💥", "🚀", "⭐"],
                "love": ["❤️", "💕", "💖", "💗", "💝", "💘", "💞", "💓", "🥰"],
                "surprised": ["😮", "😯", "😲", "🤯", "😱", "🙀", "😵", "🤩", "😳"]
            },
            "activities": {
                "entertainment": ["🎬", "🎭", "🎪", "🎨", "🎵", "🎶", "🎤", "🎸", "🎹"],
                "sports": ["⚽", "🏀", "🏈", "🎾", "🏐", "🏓", "🏸", "🥊", "🏋️"],
                "food": ["🍕", "🍔", "🍟", "🌮", "🍜", "🍝", "🍰", "🧁", "🍪"],
                "travel": ["✈️", "🚗", "🚢", "🏖️", "🏔️", "🗺️", "📍", "🧳", "📸"],
                "technology": ["💻", "📱", "⌚", "🎮", "📷", "🔧", "⚙️", "🔌", "💡"]
            },
            "symbols": {
                "fire": ["🔥", "💥", "⚡", "✨", "💫", "⭐", "🌟", "💎", "👑"],
                "arrows": ["➡️", "⬆️", "⬇️", "↗️", "↘️", "🔄", "🔃", "🔁", "🔀"],
                "hands": ["👍", "👎", "👏", "🙌", "👌", "✌️", "🤞", "🤟", "👋"],
                "hearts": ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎"]
            }
        }
    
    def _load_performance_stats(self) -> Dict[str, Any]:
        """تحميل إحصائيات الأداء"""
        try:
            stats_file = os.path.join(self.data_dir, "performance_stats.json")
            
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {
                    "template_performance": {},
                    "cta_performance": {},
                    "emoji_performance": {},
                    "total_descriptions_generated": 0,
                    "average_engagement": 0.0
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إحصائيات الأداء: {str(e)}")
            return {"template_performance": {}, "total_descriptions_generated": 0}

    def generate_description(self, content_analysis: Dict[str, Any],
                           platform: str = "tiktok",
                           hashtags: Optional[List[str]] = None) -> PostDescription:
        """إنشاء وصف للمحتوى"""
        try:
            self.logger.info(f"بدء إنشاء وصف للمنصة: {platform}")

            # تحديد فئة المحتوى
            content_category = self._determine_content_category(content_analysis)

            # إنشاء العنوان
            title = self._generate_title(content_analysis, content_category)

            # إنشاء الوصف الأساسي
            base_description = self._generate_base_description(
                content_analysis, content_category, title
            )

            # إضافة دعوة للعمل
            call_to_action = self._generate_call_to_action(content_category, platform)

            # دمج الوصف
            full_description = self._combine_description_parts(
                base_description, call_to_action, platform
            )

            # إضافة الرموز التعبيرية
            emoji_enhanced = self._add_emojis(full_description, content_category)

            # إضافة الهاشتاغات في الوصف
            if hashtags:
                description_hashtags = self._select_hashtags_for_description(hashtags)
                emoji_enhanced = self._add_hashtags_to_description(
                    emoji_enhanced, description_hashtags
                )
            else:
                description_hashtags = []

            # التحقق من طول الوصف
            final_description = self._ensure_length_limits(emoji_enhanced, platform)

            # حساب نقاط التفاعل
            engagement_score = self._calculate_engagement_score(
                final_description, content_category, platform
            )

            # إنشاء كائن الوصف النهائي
            post_description = PostDescription(
                title=title,
                description=base_description,
                call_to_action=call_to_action,
                hashtags=description_hashtags,
                emoji_enhanced=final_description,
                character_count=len(final_description),
                platform=platform,
                category=content_category,
                engagement_score=engagement_score,
                created_at=datetime.now()
            )

            # تحديث الإحصائيات
            self._update_generation_stats(post_description)

            self.logger.info(f"تم إنشاء وصف بطول {len(final_description)} حرف")

            return post_description

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الوصف: {str(e)}")
            return self._get_fallback_description(platform, content_analysis)

    def _determine_content_category(self, content_analysis: Dict[str, Any]) -> str:
        """تحديد فئة المحتوى"""
        try:
            # من تحليل المشاعر
            if "emotions" in content_analysis:
                emotions = content_analysis["emotions"]

                if emotions.get("joy", 0) > 60 or emotions.get("amusement", 0) > 60:
                    return "comedy"
                elif emotions.get("excitement", 0) > 60:
                    return "viral"
                elif emotions.get("calm", 0) > 60:
                    return "lifestyle"

            # من الكلمات المفتاحية
            if "keywords" in content_analysis:
                keywords = [k.lower() for k in content_analysis["keywords"]]

                comedy_words = ["مضحك", "ضحك", "كوميديا", "funny", "comedy", "laugh"]
                tech_words = ["تقنية", "تكنولوجيا", "tech", "technology", "digital"]
                lifestyle_words = ["حياة", "يومي", "lifestyle", "daily", "routine"]

                if any(word in keywords for word in comedy_words):
                    return "comedy"
                elif any(word in keywords for word in tech_words):
                    return "technology"
                elif any(word in keywords for word in lifestyle_words):
                    return "lifestyle"

            return "entertainment"

        except Exception as e:
            self.logger.error(f"خطأ في تحديد فئة المحتوى: {str(e)}")
            return "entertainment"

    def _generate_title(self, content_analysis: Dict[str, Any], category: str) -> str:
        """إنشاء العنوان"""
        try:
            # استخراج الكلمات المفتاحية
            keywords = content_analysis.get("keywords", [])

            # اختيار نوع العنوان
            title_types = ["question", "exclamation", "how_to"]
            title_type = random.choice(title_types)

            # اختيار اللغة
            arabic_ratio = self.settings.get("arabic_content_ratio", 0.7)
            use_arabic = random.random() < arabic_ratio
            lang = "arabic" if use_arabic else "english"

            # الحصول على قالب العنوان
            templates = self.title_templates.get(title_type, {}).get(lang, [])

            if templates and keywords:
                template = random.choice(templates)

                # ملء القالب
                if "{topic}" in template:
                    topic = random.choice(keywords[:3]) if keywords else "موضوع شيق"
                    title = template.format(topic=topic)
                elif "{content}" in template:
                    content = random.choice(keywords[:3]) if keywords else "محتوى رائع"
                    title = template.format(content=content)
                elif "{action}" in template:
                    action = random.choice(keywords[:3]) if keywords else "عمل مميز"
                    title = template.format(action=action)
                else:
                    title = template
            else:
                # عنوان افتراضي
                if use_arabic:
                    title = f"محتوى {category} رائع"
                else:
                    title = f"Amazing {category} content"

            return title[:100]  # تحديد طول العنوان

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء العنوان: {str(e)}")
            return "محتوى رائع"

    def _generate_base_description(self, content_analysis: Dict[str, Any],
                                 category: str, title: str) -> str:
        """إنشاء الوصف الأساسي"""
        try:
            # اختيار اللغة
            arabic_ratio = self.settings.get("arabic_content_ratio", 0.7)
            use_arabic = random.random() < arabic_ratio
            lang = "arabic" if use_arabic else "english"

            # الحصول على قوالب الفئة
            category_templates = self.description_templates.get(category, {})
            templates = category_templates.get(lang, [])

            if not templates:
                # استخدام قوالب عامة
                templates = self.description_templates.get("entertainment", {}).get(lang, [])

            if templates:
                template = random.choice(templates)
                description = template.format(title=title)
            else:
                # وصف افتراضي
                if use_arabic:
                    description = f"شاهد هذا المحتوى الرائع: {title}"
                else:
                    description = f"Watch this amazing content: {title}"

            # إضافة تفاصيل من تحليل المحتوى
            if "transcription" in content_analysis:
                text = content_analysis["transcription"].get("text", "")
                if text and len(text) > 20:
                    # إضافة جزء من النص
                    text_snippet = text[:100] + "..." if len(text) > 100 else text
                    description += f"\n\n{text_snippet}"

            # إضافة معلومات إضافية
            if "video_analysis" in content_analysis:
                video = content_analysis["video_analysis"]
                if video.get("duration", 0) > 60:
                    if use_arabic:
                        description += "\n\nفيديو طويل مليء بالتفاصيل!"
                    else:
                        description += "\n\nLong video full of details!"

            return description

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الوصف الأساسي: {str(e)}")
            return title

    def _generate_call_to_action(self, category: str, platform: str) -> str:
        """إنشاء دعوة للعمل"""
        try:
            if not self.settings.get("include_call_to_action", True):
                return ""

            # اختيار نوع دعوة العمل
            cta_types = ["engagement", "subscribe", "share"]

            # تخصيص حسب المنصة
            if platform == "tiktok":
                cta_types = ["engagement", "share"]
            elif platform == "youtube":
                cta_types = ["subscribe", "engagement"]

            cta_type = random.choice(cta_types)

            # اختيار اللغة
            arabic_ratio = self.settings.get("arabic_content_ratio", 0.7)
            use_arabic = random.random() < arabic_ratio
            lang = "arabic" if use_arabic else "english"

            # الحصول على دعوة العمل
            cta_options = self.call_to_actions.get(cta_type, {}).get(lang, [])

            if cta_options:
                return random.choice(cta_options)
            else:
                # دعوة افتراضية
                if use_arabic:
                    return "شاركوني رأيكم في التعليقات! 💬"
                else:
                    return "Share your thoughts in the comments! 💬"

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء دعوة العمل: {str(e)}")
            return ""

    def _combine_description_parts(self, base_description: str, call_to_action: str,
                                 platform: str) -> str:
        """دمج أجزاء الوصف"""
        try:
            parts = [base_description]

            if call_to_action:
                parts.append(call_to_action)

            # دمج الأجزاء
            combined = "\n\n".join(parts)

            return combined

        except Exception as e:
            self.logger.error(f"خطأ في دمج أجزاء الوصف: {str(e)}")
            return base_description

    def _add_emojis(self, description: str, category: str) -> str:
        """إضافة الرموز التعبيرية"""
        try:
            if not self.settings.get("include_emojis", True):
                return description

            emoji_density = self.settings.get("emoji_density", 0.1)
            words = description.split()
            emoji_count = max(1, int(len(words) * emoji_density))

            # اختيار رموز مناسبة للفئة
            category_emojis = []

            if category == "comedy":
                category_emojis.extend(self.emojis["emotions"]["funny"])
            elif category == "viral":
                category_emojis.extend(self.emojis["symbols"]["fire"])
            elif category == "entertainment":
                category_emojis.extend(self.emojis["activities"]["entertainment"])
            elif category == "lifestyle":
                category_emojis.extend(self.emojis["emotions"]["happy"])
            elif category == "technology":
                category_emojis.extend(self.emojis["activities"]["technology"])

            # إضافة رموز عامة
            category_emojis.extend(self.emojis["emotions"]["happy"])
            category_emojis.extend(self.emojis["symbols"]["hearts"])

            # إضافة الرموز في مواضع عشوائية
            enhanced_description = description

            for _ in range(emoji_count):
                if category_emojis:
                    emoji = random.choice(category_emojis)

                    # إضافة في نهاية الجملة أو في مكان عشوائي
                    if random.random() < 0.7:  # 70% في نهاية الجملة
                        sentences = enhanced_description.split('.')
                        if len(sentences) > 1:
                            sentence_idx = random.randint(0, len(sentences) - 2)
                            sentences[sentence_idx] += f" {emoji}"
                            enhanced_description = '.'.join(sentences)
                    else:  # 30% في مكان عشوائي
                        words = enhanced_description.split()
                        if len(words) > 2:
                            word_idx = random.randint(1, len(words) - 1)
                            words.insert(word_idx, emoji)
                            enhanced_description = ' '.join(words)

            return enhanced_description

        except Exception as e:
            self.logger.error(f"خطأ في إضافة الرموز التعبيرية: {str(e)}")
            return description

    def _select_hashtags_for_description(self, hashtags: List[str]) -> List[str]:
        """اختيار هاشتاغات للوصف"""
        try:
            max_hashtags = self.settings.get("max_hashtags_in_description", 5)

            # اختيار أفضل الهاشتاغات
            selected = hashtags[:max_hashtags]

            return selected

        except Exception as e:
            self.logger.error(f"خطأ في اختيار هاشتاغات الوصف: {str(e)}")
            return []

    def _add_hashtags_to_description(self, description: str, hashtags: List[str]) -> str:
        """إضافة هاشتاغات للوصف"""
        try:
            if not hashtags:
                return description

            # إضافة الهاشتاغات في نهاية الوصف
            hashtag_line = " ".join(hashtags)

            return f"{description}\n\n{hashtag_line}"

        except Exception as e:
            self.logger.error(f"خطأ في إضافة هاشتاغات للوصف: {str(e)}")
            return description

    def _ensure_length_limits(self, description: str, platform: str) -> str:
        """التأكد من حدود الطول"""
        try:
            max_length = self.settings.get("max_description_length", {}).get(platform, 2200)
            min_length = self.settings.get("min_description_length", 50)

            # قطع الوصف إذا كان طويلاً
            if len(description) > max_length:
                # قطع عند آخر جملة كاملة
                truncated = description[:max_length]
                last_period = truncated.rfind('.')
                last_exclamation = truncated.rfind('!')
                last_question = truncated.rfind('?')

                last_sentence_end = max(last_period, last_exclamation, last_question)

                if last_sentence_end > min_length:
                    description = truncated[:last_sentence_end + 1]
                else:
                    description = truncated + "..."

            # إضافة محتوى إذا كان قصيراً
            elif len(description) < min_length:
                # إضافة دعوة للعمل إضافية
                if "تابع" not in description and "follow" not in description.lower():
                    description += "\n\nتابعوني للمزيد! 🔔"

            return description

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من حدود الطول: {str(e)}")
            return description

    def _calculate_engagement_score(self, description: str, category: str, platform: str) -> float:
        """حساب نقاط التفاعل المتوقع"""
        try:
            score = 50.0  # نقاط أساسية

            # نقاط طول الوصف
            length = len(description)
            if 100 <= length <= 300:
                score += 10
            elif 300 < length <= 500:
                score += 5
            elif length > 1000:
                score -= 5

            # نقاط الرموز التعبيرية
            emoji_count = len(re.findall(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]', description))
            if emoji_count > 0:
                score += min(emoji_count * 2, 10)  # حد أقصى 10 نقاط

            # نقاط دعوة العمل
            cta_keywords = ["تابع", "شارك", "لايك", "كومنت", "follow", "share", "like", "comment"]
            if any(keyword in description.lower() for keyword in cta_keywords):
                score += 15

            # نقاط الأسئلة
            if "؟" in description or "?" in description:
                score += 10

            # نقاط الهاشتاغات
            hashtag_count = description.count("#")
            if hashtag_count > 0:
                score += min(hashtag_count * 1.5, 8)

            # نقاط الفئة
            category_multipliers = {
                "comedy": 1.2,
                "viral": 1.3,
                "entertainment": 1.1,
                "lifestyle": 1.0,
                "technology": 0.9
            }
            score *= category_multipliers.get(category, 1.0)

            # نقاط المنصة
            platform_multipliers = {
                "tiktok": 1.2,
                "instagram": 1.1,
                "youtube": 1.0,
                "twitter": 0.9
            }
            score *= platform_multipliers.get(platform, 1.0)

            return min(100.0, max(0.0, score))

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط التفاعل: {str(e)}")
            return 50.0

    def _update_generation_stats(self, description: PostDescription):
        """تحديث إحصائيات الإنشاء"""
        try:
            self.performance_stats["total_descriptions_generated"] += 1

            # تحديث إحصائيات القوالب
            template_stats = self.performance_stats.get("template_performance", {})
            category = description.category

            if category not in template_stats:
                template_stats[category] = {
                    "usage_count": 0,
                    "total_engagement_score": 0,
                    "average_engagement": 0
                }

            template_stats[category]["usage_count"] += 1
            template_stats[category]["total_engagement_score"] += description.engagement_score
            template_stats[category]["average_engagement"] = (
                template_stats[category]["total_engagement_score"] /
                template_stats[category]["usage_count"]
            )

            self.performance_stats["template_performance"] = template_stats

            # حفظ الإحصائيات
            self._save_performance_stats()

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات الإنشاء: {str(e)}")

    def _get_fallback_description(self, platform: str, content_analysis: Dict[str, Any]) -> PostDescription:
        """الحصول على وصف احتياطي"""
        try:
            title = "محتوى رائع"
            description = "شاهد هذا المحتوى المميز!"
            call_to_action = "شاركوني رأيكم في التعليقات! 💬"

            full_description = f"{description}\n\n{call_to_action}"

            return PostDescription(
                title=title,
                description=description,
                call_to_action=call_to_action,
                hashtags=[],
                emoji_enhanced=full_description,
                character_count=len(full_description),
                platform=platform,
                category="general",
                engagement_score=30.0,
                created_at=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على وصف احتياطي: {str(e)}")
            return PostDescription(
                title="محتوى",
                description="محتوى رائع",
                call_to_action="",
                hashtags=[],
                emoji_enhanced="محتوى رائع",
                character_count=10,
                platform=platform,
                category="general",
                engagement_score=0.0,
                created_at=datetime.now()
            )

    def _save_performance_stats(self):
        """حفظ إحصائيات الأداء"""
        try:
            stats_file = os.path.join(self.data_dir, "performance_stats.json")
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.performance_stats, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ إحصائيات الأداء: {str(e)}")

    def update_description_performance(self, description_id: str, engagement_data: Dict[str, Any]):
        """تحديث أداء الوصف"""
        try:
            # تحديث إحصائيات الأداء بناءً على البيانات الفعلية
            if "description_performance" not in self.performance_stats:
                self.performance_stats["description_performance"] = {}

            self.performance_stats["description_performance"][description_id] = {
                "likes": engagement_data.get("likes", 0),
                "comments": engagement_data.get("comments", 0),
                "shares": engagement_data.get("shares", 0),
                "views": engagement_data.get("views", 0),
                "engagement_rate": engagement_data.get("engagement_rate", 0),
                "updated_at": datetime.now().isoformat()
            }

            # حفظ الإحصائيات
            self._save_performance_stats()

            self.logger.info(f"تم تحديث أداء الوصف {description_id}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث أداء الوصف: {str(e)}")

    def get_best_performing_templates(self, limit: int = 5) -> List[Dict[str, Any]]:
        """الحصول على أفضل القوالب أداءً"""
        try:
            template_stats = self.performance_stats.get("template_performance", {})

            # ترتيب القوالب حسب متوسط التفاعل
            sorted_templates = sorted(
                template_stats.items(),
                key=lambda x: x[1].get("average_engagement", 0),
                reverse=True
            )

            return [
                {
                    "category": category,
                    "usage_count": stats["usage_count"],
                    "average_engagement": stats["average_engagement"],
                    "total_engagement_score": stats["total_engagement_score"]
                }
                for category, stats in sorted_templates[:limit]
            ]

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على أفضل القوالب: {str(e)}")
            return []

    def generate_multiple_descriptions(self, content_analysis: Dict[str, Any],
                                     platform: str = "tiktok",
                                     count: int = 3,
                                     hashtags: Optional[List[str]] = None) -> List[PostDescription]:
        """إنشاء عدة أوصاف للمحتوى"""
        try:
            descriptions = []

            for i in range(count):
                # تنويع الإعدادات لكل وصف
                temp_settings = self.settings.copy()

                # تنويع نسبة اللغة العربية
                if i == 1:
                    temp_settings["arabic_content_ratio"] = 0.3
                elif i == 2:
                    temp_settings["arabic_content_ratio"] = 1.0

                # تنويع كثافة الرموز التعبيرية
                temp_settings["emoji_density"] = 0.05 + (i * 0.05)

                # حفظ الإعدادات الأصلية
                original_settings = self.settings
                self.settings = temp_settings

                try:
                    description = self.generate_description(content_analysis, platform, hashtags)
                    descriptions.append(description)
                finally:
                    # استعادة الإعدادات الأصلية
                    self.settings = original_settings

            return descriptions

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء أوصاف متعددة: {str(e)}")
            return [self._get_fallback_description(platform, content_analysis)]

    def customize_description(self, base_description: PostDescription,
                            custom_settings: Dict[str, Any]) -> PostDescription:
        """تخصيص وصف موجود"""
        try:
            # تطبيق الإعدادات المخصصة
            original_settings = self.settings.copy()
            self.settings.update(custom_settings)

            try:
                # إعادة معالجة الوصف
                enhanced_description = base_description.description

                # إعادة إضافة الرموز التعبيرية
                if custom_settings.get("include_emojis", True):
                    enhanced_description = self._add_emojis(enhanced_description, base_description.category)

                # إعادة إضافة دعوة العمل
                if custom_settings.get("include_call_to_action", True):
                    new_cta = self._generate_call_to_action(base_description.category, base_description.platform)
                    enhanced_description = self._combine_description_parts(
                        enhanced_description, new_cta, base_description.platform
                    )

                # إعادة إضافة الهاشتاغات
                if base_description.hashtags:
                    enhanced_description = self._add_hashtags_to_description(
                        enhanced_description, base_description.hashtags
                    )

                # التحقق من حدود الطول
                final_description = self._ensure_length_limits(enhanced_description, base_description.platform)

                # إعادة حساب نقاط التفاعل
                new_engagement_score = self._calculate_engagement_score(
                    final_description, base_description.category, base_description.platform
                )

                # إنشاء وصف جديد
                customized_description = PostDescription(
                    title=base_description.title,
                    description=base_description.description,
                    call_to_action=base_description.call_to_action,
                    hashtags=base_description.hashtags,
                    emoji_enhanced=final_description,
                    character_count=len(final_description),
                    platform=base_description.platform,
                    category=base_description.category,
                    engagement_score=new_engagement_score,
                    created_at=datetime.now()
                )

                return customized_description

            finally:
                # استعادة الإعدادات الأصلية
                self.settings = original_settings

        except Exception as e:
            self.logger.error(f"خطأ في تخصيص الوصف: {str(e)}")
            return base_description

    def get_generator_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المولد"""
        try:
            stats = {
                "total_descriptions_generated": self.performance_stats.get("total_descriptions_generated", 0),
                "template_performance": self.get_best_performing_templates(),
                "average_engagement": self.performance_stats.get("average_engagement", 0),
                "settings": self.settings,
                "available_categories": list(self.description_templates.keys()),
                "available_languages": ["arabic", "english"],
                "emoji_categories": list(self.emojis.keys()),
                "cta_types": list(self.call_to_actions.keys())
            }

            # إحصائيات الأداء
            description_performance = self.performance_stats.get("description_performance", {})
            if description_performance:
                total_engagement = sum(
                    desc.get("engagement_rate", 0) for desc in description_performance.values()
                )
                stats["average_real_engagement"] = total_engagement / len(description_performance)
                stats["total_tracked_descriptions"] = len(description_performance)

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات المولد: {str(e)}")
            return {}

    def export_templates(self, file_path: str):
        """تصدير القوالب"""
        try:
            export_data = {
                "description_templates": self.description_templates,
                "title_templates": self.title_templates,
                "call_to_actions": self.call_to_actions,
                "emojis": self.emojis,
                "performance_stats": self.performance_stats,
                "export_date": datetime.now().isoformat()
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير القوالب إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير القوالب: {str(e)}")

    def import_templates(self, file_path: str):
        """استيراد قوالب"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)

            # دمج القوالب
            if "description_templates" in import_data:
                for category, templates in import_data["description_templates"].items():
                    if category not in self.description_templates:
                        self.description_templates[category] = templates
                    else:
                        # دمج القوالب الجديدة
                        for lang, lang_templates in templates.items():
                            if lang not in self.description_templates[category]:
                                self.description_templates[category][lang] = lang_templates
                            else:
                                # إضافة القوالب الجديدة فقط
                                for template in lang_templates:
                                    if template not in self.description_templates[category][lang]:
                                        self.description_templates[category][lang].append(template)

            # حفظ القوالب المحدثة
            self._save_templates()

            self.logger.info(f"تم استيراد القوالب من {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في استيراد القوالب: {str(e)}")

    def _save_templates(self):
        """حفظ القوالب"""
        try:
            templates_file = os.path.join(self.data_dir, "description_templates.json")
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.description_templates, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ القوالب: {str(e)}")

    def add_custom_template(self, category: str, language: str, template: str):
        """إضافة قالب مخصص"""
        try:
            if category not in self.description_templates:
                self.description_templates[category] = {}

            if language not in self.description_templates[category]:
                self.description_templates[category][language] = []

            if template not in self.description_templates[category][language]:
                self.description_templates[category][language].append(template)
                self._save_templates()
                self.logger.info(f"تم إضافة قالب جديد للفئة {category}")

        except Exception as e:
            self.logger.error(f"خطأ في إضافة قالب مخصص: {str(e)}")
