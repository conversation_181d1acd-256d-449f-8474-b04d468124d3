#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محسن الذاكرة والموارد
Memory and Resource Optimizer Test
"""

import os
import gc
import sys
import time
import json
import psutil
import tracemalloc
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

def test_memory_optimization():
    """اختبار تحسين الذاكرة"""
    print("🧠 اختبار تحسين الذاكرة...")
    
    try:
        # بدء تتبع الذاكرة
        if not tracemalloc.is_tracing():
            tracemalloc.start()
        
        # قياس الذاكرة قبل التحسين
        before_current, before_peak = tracemalloc.get_traced_memory()
        before_rss = psutil.Process().memory_info().rss
        
        print(f"   📊 الذاكرة قبل التحسين:")
        print(f"      - Traced: {before_current / 1024 / 1024:.2f} MB")
        print(f"      - RSS: {before_rss / 1024 / 1024:.2f} MB")
        
        # تطبيق تحسينات الذاكرة
        optimizations = []
        
        # 1. تنظيف الذاكرة متعدد المراحل
        total_collected = 0
        for generation in range(3):
            collected = gc.collect(generation)
            total_collected += collected
            optimizations.append(f"gc_generation_{generation}_collected_{collected}")
        
        # 2. تحسين إعدادات garbage collector
        old_threshold = gc.get_threshold()
        gc.set_threshold(700, 10, 10)
        optimizations.append(f"gc_threshold_optimized_from_{old_threshold}")
        
        # 3. تنظيف الكائنات الضعيفة المراجع
        import weakref
        weak_refs_cleaned = 0
        for obj in gc.get_objects():
            if isinstance(obj, weakref.ref) and obj() is None:
                weak_refs_cleaned += 1
        optimizations.append(f"weak_refs_cleaned_{weak_refs_cleaned}")
        
        # 4. تنظيف الذاكرة المؤقتة
        cache_cleaned = 0
        try:
            import re
            if hasattr(re, '_cache'):
                re._cache.clear()
                cache_cleaned += 1
        except:
            pass
        optimizations.append(f"caches_cleaned_{cache_cleaned}")
        
        # قياس الذاكرة بعد التحسين
        after_current, after_peak = tracemalloc.get_traced_memory()
        after_rss = psutil.Process().memory_info().rss
        
        # حساب التحسينات
        memory_saved_traced = (before_current - after_current) / 1024 / 1024
        memory_saved_rss = (before_rss - after_rss) / 1024 / 1024
        
        print(f"   📊 الذاكرة بعد التحسين:")
        print(f"      - Traced: {after_current / 1024 / 1024:.2f} MB")
        print(f"      - RSS: {after_rss / 1024 / 1024:.2f} MB")
        print(f"   💾 ذاكرة محررة:")
        print(f"      - Traced: {memory_saved_traced:.2f} MB")
        print(f"      - RSS: {memory_saved_rss:.2f} MB")
        print(f"   🗑️ كائنات محررة: {total_collected}")
        
        improvement_percent = (memory_saved_rss / (before_rss / 1024 / 1024)) * 100 if before_rss > 0 else 0
        print(f"   📈 تحسين الأداء: {improvement_percent:.1f}%")
        
        return {
            "status": "completed",
            "optimizations": optimizations,
            "memory_saved_mb": memory_saved_rss,
            "objects_collected": total_collected,
            "improvement_percent": improvement_percent
        }
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تحسين الذاكرة: {e}")
        return {"error": str(e)}

def test_resource_analysis():
    """اختبار تحليل الموارد"""
    print("\n🔧 اختبار تحليل الموارد...")
    
    try:
        process = psutil.Process()
        
        # تحليل الملفات المفتوحة
        open_files_count = 0
        try:
            open_files = process.open_files()
            open_files_count = len(open_files)
        except (psutil.AccessDenied, AttributeError):
            pass
        
        # تحليل الاتصالات
        connections_count = 0
        try:
            connections = process.connections()
            connections_count = len(connections)
        except (psutil.AccessDenied, AttributeError):
            pass
        
        # تحليل الخيوط
        import threading
        active_threads = threading.active_count()
        
        # تحليل استخدام المعالج والذاكرة
        cpu_percent = process.cpu_percent()
        memory_percent = process.memory_percent()
        
        print(f"   📁 ملفات مفتوحة: {open_files_count}")
        print(f"   🌐 اتصالات نشطة: {connections_count}")
        print(f"   🧵 خيوط نشطة: {active_threads}")
        print(f"   💻 استخدام المعالج: {cpu_percent:.1f}%")
        print(f"   💾 استخدام الذاكرة: {memory_percent:.1f}%")
        
        # كشف تسريبات محتملة
        potential_leaks = []
        
        if open_files_count > 100:
            potential_leaks.append(f"file_handles_leak_{open_files_count}")
        
        if connections_count > 50:
            potential_leaks.append(f"network_connections_leak_{connections_count}")
        
        if active_threads > 20:
            potential_leaks.append(f"threads_leak_{active_threads}")
        
        if potential_leaks:
            print(f"   ⚠️ تسريبات محتملة: {len(potential_leaks)}")
            for leak in potential_leaks:
                print(f"      - {leak}")
        else:
            print(f"   ✅ لم يتم العثور على تسريبات واضحة")
        
        return {
            "status": "completed",
            "open_files": open_files_count,
            "connections": connections_count,
            "threads": active_threads,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "potential_leaks": potential_leaks
        }
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تحليل الموارد: {e}")
        return {"error": str(e)}

def create_optimization_configs():
    """إنشاء ملفات إعدادات التحسين"""
    print("\n⚙️ إنشاء ملفات إعدادات التحسين...")
    
    try:
        # إنشاء المجلدات
        config_dir = Path("config/memory_optimization")
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # إعدادات تحسين الذاكرة
        memory_config = {
            "memory_optimization": {
                "gc_enabled": True,
                "gc_threshold": [700, 10, 10],
                "auto_cleanup_enabled": True,
                "cleanup_interval_seconds": 300,
                "memory_threshold_mb": 1024,
                "weak_ref_cleanup_enabled": True,
                "cache_cleanup_enabled": True,
                "object_pooling_enabled": True
            },
            "monitoring": {
                "memory_monitoring_enabled": True,
                "check_interval_seconds": 60,
                "alert_threshold_mb": 2048,
                "log_memory_usage": True,
                "detailed_analysis_enabled": True
            },
            "performance_targets": {
                "max_memory_usage_mb": 1024,
                "max_objects_count": 100000,
                "gc_frequency_seconds": 30,
                "memory_efficiency_target_percent": 80
            }
        }
        
        # إعدادات تحسين الموارد
        resource_config = {
            "resource_optimization": {
                "file_management": {
                    "max_open_files": 100,
                    "auto_close_unused_files": True,
                    "temp_file_cleanup_enabled": True,
                    "temp_file_max_age_hours": 1
                },
                "connection_management": {
                    "max_connections": 50,
                    "connection_timeout_seconds": 30,
                    "keep_alive_enabled": True,
                    "connection_pooling_enabled": True
                },
                "thread_management": {
                    "max_threads": 20,
                    "thread_timeout_seconds": 300,
                    "daemon_thread_cleanup": True,
                    "thread_pool_size": 8
                }
            },
            "monitoring": {
                "resource_monitoring_enabled": True,
                "check_interval_seconds": 60,
                "alert_on_resource_leaks": True,
                "log_resource_usage": True
            }
        }
        
        # إعدادات المراقبة التلقائية
        monitoring_config = {
            "auto_monitoring": {
                "enabled": True,
                "check_interval_seconds": 60,
                "memory_threshold_mb": 1024,
                "cpu_threshold_percent": 80,
                "disk_threshold_percent": 90
            },
            "alerts": {
                "memory_alert_enabled": True,
                "cpu_alert_enabled": True,
                "disk_alert_enabled": True,
                "resource_leak_alert_enabled": True,
                "alert_cooldown_minutes": 5
            },
            "cleanup": {
                "auto_cleanup_enabled": True,
                "cleanup_interval_minutes": 30,
                "memory_cleanup_threshold_mb": 512,
                "temp_file_cleanup_enabled": True,
                "cache_cleanup_enabled": True
            },
            "logging": {
                "log_level": "INFO",
                "log_file": "memory_resource_monitoring.log",
                "max_log_size_mb": 100,
                "backup_count": 5,
                "log_rotation_enabled": True
            }
        }
        
        # حفظ ملفات الإعدادات
        configs = [
            ("memory_optimization_config.json", memory_config),
            ("resource_optimization_config.json", resource_config),
            ("monitoring_config.json", monitoring_config)
        ]
        
        created_files = []
        for filename, config in configs:
            config_file = config_dir / filename
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            created_files.append(str(config_file))
            print(f"   ✅ تم إنشاء: {config_file}")
        
        return {
            "status": "completed",
            "created_files": created_files,
            "config_directory": str(config_dir)
        }
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملفات الإعدادات: {e}")
        return {"error": str(e)}

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🚀 اختبار محسن الذاكرة والموارد")
    print("=" * 50)
    
    try:
        # اختبار تحسين الذاكرة
        memory_results = test_memory_optimization()
        
        # اختبار تحليل الموارد
        resource_results = test_resource_analysis()
        
        # إنشاء ملفات الإعدادات
        config_results = create_optimization_configs()
        
        # عرض النتائج النهائية
        print("\n" + "=" * 50)
        print("🎉 تم إكمال اختبار التحسين بنجاح!")
        print("=" * 50)
        
        if memory_results.get("memory_saved_mb", 0) > 0:
            print(f"💾 ذاكرة محررة: {memory_results['memory_saved_mb']:.2f} MB")
        
        if memory_results.get("objects_collected", 0) > 0:
            print(f"🗑️ كائنات محررة: {memory_results['objects_collected']}")
        
        if memory_results.get("improvement_percent", 0) > 0:
            print(f"📈 تحسين الأداء: {memory_results['improvement_percent']:.1f}%")
        
        if resource_results.get("potential_leaks"):
            print(f"⚠️ تسريبات محتملة: {len(resource_results['potential_leaks'])}")
        
        if config_results.get("created_files"):
            print(f"📁 ملفات إعدادات منشأة: {len(config_results['created_files'])}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
