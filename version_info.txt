
# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Augment Code'),
        StringStruct(u'FileDescription', u'Smart Content Creator - منشئ المحتوى الذكي'),
        StringStruct(u'FileVersion', u'1.0.0'),
        StringStruct(u'InternalName', u'Smart_Content_Creator'),
        StringStruct(u'LegalCopyright', u'© 2025 Augment Code. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'Smart_Content_Creator.exe'),
        StringStruct(u'ProductName', u'Smart Content Creator'),
        StringStruct(u'ProductVersion', u'1.0.0')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
