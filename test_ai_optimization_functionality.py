#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف تحسين محرك الذكاء الاصطناعي
Test AI Engine Optimization Functionality
"""

import os
import sys
import json
import time
from pathlib import Path

def test_optimization_configs():
    """اختبار ملفات إعدادات التحسين"""
    print("🔧 اختبار ملفات إعدادات التحسين...")
    
    config_files = [
        "src/ai/model_optimization_config.json",
        "src/ai/video_optimization_config.json", 
        "src/ai/audio_optimization_config.json",
        "src/ai/cache_optimization_config.json",
        "src/ai/parallel_optimization_config.json"
    ]
    
    results = {}
    
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    results[config_file] = {
                        "status": "✅ موجود وصالح",
                        "size": len(json.dumps(config)),
                        "keys": list(config.keys()) if isinstance(config, dict) else []
                    }
                    print(f"   ✅ {config_file}: صالح ({len(config.keys()) if isinstance(config, dict) else 0} مفاتيح)")
            else:
                results[config_file] = {"status": "❌ غير موجود"}
                print(f"   ❌ {config_file}: غير موجود")
        except Exception as e:
            results[config_file] = {"status": f"❌ خطأ: {e}"}
            print(f"   ❌ {config_file}: خطأ - {e}")
    
    return results

def test_content_analyzer_optimization():
    """اختبار تحسينات ContentAnalyzer"""
    print("\n🤖 اختبار تحسينات ContentAnalyzer...")
    
    try:
        # إضافة مسار src إلى Python path
        sys.path.insert(0, 'src')
        
        # استيراد ContentAnalyzer
        from ai.content_analyzer import ContentAnalyzer
        
        # إنشاء مثيل
        analyzer = ContentAnalyzer()
        
        # اختبار الدوال المحسنة
        optimization_methods = [
            'get_cached_analysis',
            'cache_analysis', 
            'batch_analyze_content',
            'analyze_content_async',
            'optimize_models',
            'get_performance_stats',
            'cleanup_resources'
        ]
        
        results = {}
        for method in optimization_methods:
            if hasattr(analyzer, method):
                results[method] = "✅ موجود"
                print(f"   ✅ {method}: موجود")
            else:
                results[method] = "❌ غير موجود"
                print(f"   ❌ {method}: غير موجود")
        
        # اختبار إحصائيات الأداء
        if hasattr(analyzer, 'performance_stats'):
            stats = analyzer.performance_stats
            print(f"   📊 إحصائيات الأداء: {len(stats)} مقياس")
            results['performance_stats'] = f"✅ {len(stats)} مقياس"
        else:
            results['performance_stats'] = "❌ غير موجود"
            print(f"   ❌ إحصائيات الأداء: غير موجودة")
        
        # اختبار إعدادات التحسين
        if hasattr(analyzer, 'optimization_settings'):
            settings = analyzer.optimization_settings
            print(f"   ⚙️ إعدادات التحسين: {len(settings)} إعداد")
            results['optimization_settings'] = f"✅ {len(settings)} إعداد"
        else:
            results['optimization_settings'] = "❌ غير موجود"
            print(f"   ❌ إعدادات التحسين: غير موجودة")
        
        return results
        
    except ImportError as e:
        print(f"   ❌ خطأ في الاستيراد: {e}")
        return {"import_error": str(e)}
    except Exception as e:
        print(f"   ❌ خطأ عام: {e}")
        return {"general_error": str(e)}

def test_cache_directory():
    """اختبار مجلد التخزين المؤقت"""
    print("\n💾 اختبار مجلد التخزين المؤقت...")
    
    cache_dirs = [
        "cache",
        "cache/ai_results"
    ]
    
    results = {}
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            results[cache_dir] = "✅ موجود"
            print(f"   ✅ {cache_dir}: موجود")
        else:
            # محاولة إنشاء المجلد
            try:
                os.makedirs(cache_dir, exist_ok=True)
                results[cache_dir] = "✅ تم إنشاؤه"
                print(f"   ✅ {cache_dir}: تم إنشاؤه")
            except Exception as e:
                results[cache_dir] = f"❌ خطأ: {e}"
                print(f"   ❌ {cache_dir}: خطأ - {e}")
    
    return results

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار وظائف تحسين محرك الذكاء الاصطناعي")
    print("=" * 60)
    
    # اختبار ملفات الإعدادات
    config_results = test_optimization_configs()
    
    # اختبار ContentAnalyzer
    analyzer_results = test_content_analyzer_optimization()
    
    # اختبار مجلد التخزين المؤقت
    cache_results = test_cache_directory()
    
    # تجميع النتائج
    all_results = {
        "config_files": config_results,
        "content_analyzer": analyzer_results,
        "cache_directories": cache_results,
        "test_timestamp": time.time(),
        "test_date": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # حفظ نتائج الاختبار
    with open("ai_optimization_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبار:")
    
    # عد النتائج الناجحة
    config_success = sum(1 for r in config_results.values() if isinstance(r, dict) and "✅" in r.get("status", ""))
    analyzer_success = sum(1 for r in analyzer_results.values() if "✅" in str(r))
    cache_success = sum(1 for r in cache_results.values() if "✅" in str(r))
    
    print(f"   📁 ملفات الإعدادات: {config_success}/{len(config_results)} ناجح")
    print(f"   🤖 تحسينات ContentAnalyzer: {analyzer_success}/{len(analyzer_results)} ناجح")
    print(f"   💾 مجلدات التخزين المؤقت: {cache_success}/{len(cache_results)} ناجح")
    
    total_success = config_success + analyzer_success + cache_success
    total_tests = len(config_results) + len(analyzer_results) + len(cache_results)
    
    print(f"\n🎯 النتيجة الإجمالية: {total_success}/{total_tests} ({(total_success/total_tests)*100:.1f}%)")
    
    if total_success == total_tests:
        print("🎉 جميع الاختبارات نجحت! تحسين محرك الذكاء الاصطناعي مكتمل.")
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع النتائج أعلاه.")
    
    print(f"\n📄 تم حفظ النتائج في: ai_optimization_test_results.json")

if __name__ == "__main__":
    main()
