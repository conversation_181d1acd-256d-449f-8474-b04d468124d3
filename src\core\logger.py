#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التسجيل والمراقبة - Logging System
يدير تسجيل جميع العمليات والأخطاء في التطبيق
"""

import logging
import logging.handlers
import os
from pathlib import Path
from datetime import datetime
import json

def setup_logger(name: str = "smart_content_app", level: int = logging.INFO) -> logging.Logger:
    """إعداد نظام التسجيل"""
    
    # إنشاء مجلد السجلات
    logs_dir = Path.home() / ".smart_content_app" / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # إنشاء logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # تجنب إضافة handlers متعددة
    if logger.handlers:
        return logger
    
    # تنسيق الرسائل
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # معالج الملف (يدور يومياً)
    file_handler = logging.handlers.TimedRotatingFileHandler(
        logs_dir / "app.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # معالج الأخطاء (ملف منفصل)
    error_handler = logging.FileHandler(
        logs_dir / "errors.log",
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # إضافة المعالجات
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)
    
    return logger

class ActivityLogger:
    """مسجل الأنشطة والإحصائيات"""
    
    def __init__(self):
        self.logger = logging.getLogger("activity")
        self.stats_file = Path.home() / ".smart_content_app" / "stats.json"
        self.daily_stats = self._load_daily_stats()
    
    def _load_daily_stats(self) -> dict:
        """تحميل إحصائيات اليوم"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        try:
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    all_stats = json.load(f)
                    return all_stats.get(today, self._get_empty_stats())
            else:
                return self._get_empty_stats()
        except Exception:
            return self._get_empty_stats()
    
    def _get_empty_stats(self) -> dict:
        """إحصائيات فارغة"""
        return {
            "content_fetched": 0,
            "videos_processed": 0,
            "videos_published": 0,
            "errors": 0,
            "start_time": datetime.now().isoformat(),
            "platforms": {
                "snapchat": {"fetched": 0, "errors": 0},
                "tiktok": {"fetched": 0, "published": 0, "errors": 0},
                "kick": {"fetched": 0, "errors": 0}
            }
        }
    
    def _save_stats(self):
        """حفظ الإحصائيات"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        try:
            all_stats = {}
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    all_stats = json.load(f)
            
            all_stats[today] = self.daily_stats
            
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(all_stats, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإحصائيات: {str(e)}")
    
    def log_content_fetched(self, platform: str, count: int = 1):
        """تسجيل جلب محتوى"""
        self.daily_stats["content_fetched"] += count
        self.daily_stats["platforms"][platform]["fetched"] += count
        self._save_stats()
        self.logger.info(f"تم جلب {count} محتوى من {platform}")
    
    def log_video_processed(self, duration: float = 0):
        """تسجيل معالجة فيديو"""
        self.daily_stats["videos_processed"] += 1
        self._save_stats()
        self.logger.info(f"تم معالجة فيديو (المدة: {duration:.2f}s)")
    
    def log_video_published(self, platform: str):
        """تسجيل نشر فيديو"""
        self.daily_stats["videos_published"] += 1
        self.daily_stats["platforms"][platform]["published"] += 1
        self._save_stats()
        self.logger.info(f"تم نشر فيديو على {platform}")
    
    def log_error(self, platform: str, error_msg: str):
        """تسجيل خطأ"""
        self.daily_stats["errors"] += 1
        self.daily_stats["platforms"][platform]["errors"] += 1
        self._save_stats()
        self.logger.error(f"خطأ في {platform}: {error_msg}")
    
    def get_daily_report(self) -> dict:
        """الحصول على تقرير يومي"""
        return self.daily_stats.copy()
    
    def get_weekly_report(self) -> dict:
        """الحصول على تقرير أسبوعي"""
        try:
            if not self.stats_file.exists():
                return {}
            
            with open(self.stats_file, 'r', encoding='utf-8') as f:
                all_stats = json.load(f)
            
            # حساب آخر 7 أيام
            from datetime import timedelta
            today = datetime.now()
            week_stats = {
                "total_content_fetched": 0,
                "total_videos_processed": 0,
                "total_videos_published": 0,
                "total_errors": 0,
                "daily_breakdown": {}
            }
            
            for i in range(7):
                date = (today - timedelta(days=i)).strftime("%Y-%m-%d")
                day_stats = all_stats.get(date, self._get_empty_stats())
                
                week_stats["total_content_fetched"] += day_stats["content_fetched"]
                week_stats["total_videos_processed"] += day_stats["videos_processed"]
                week_stats["total_videos_published"] += day_stats["videos_published"]
                week_stats["total_errors"] += day_stats["errors"]
                week_stats["daily_breakdown"][date] = day_stats
            
            return week_stats
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التقرير الأسبوعي: {str(e)}")
            return {}

# إنشاء مسجل الأنشطة العام
activity_logger = ActivityLogger()
