#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق - Main Window
واجهة المستخدم الرئيسية مع جميع التبويبات والوظائف
"""

import sys
import logging
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QLabel, QPushButton, QTextEdit,
    QStatusBar, QMenuBar, QSystemTrayIcon, QMenu,
    QMessageBox, QProgressBar, QFrame, QGridLayout,
    QGroupBox, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction

from ..core.config_manager import ConfigManager
from ..core.logger import activity_logger
from ..core.security import SecurityManager

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارات مخصصة
    status_updated = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # تهيئة المدراء
        self.config_manager = ConfigManager()
        self.security_manager = SecurityManager()
        
        # متغيرات الحالة
        self.is_monitoring = False
        self.is_processing = False
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_system_tray()
        self.setup_timers()
        self.setup_connections()
        
        # تحميل الإعدادات
        self.load_settings()
        
        self.logger.info("تم إنشاء النافذة الرئيسية بنجاح")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("Smart Content Creator - منشئ المحتوى الذكي")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        # إظهار/إخفاء شريط الحالة
        status_action = QAction("شريط الحالة", self)
        status_action.setCheckable(True)
        status_action.setChecked(True)
        status_action.triggered.connect(self.toggle_status_bar)
        view_menu.addAction(status_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        # حول
        about_action = QAction("حول التطبيق", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # رسالة الحالة
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # حالة الاتصال
        self.connection_label = QLabel("غير متصل")
        self.connection_label.setStyleSheet("color: red;")
        self.status_bar.addPermanentWidget(self.connection_label)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب الحسابات
        self.create_accounts_tab()
        
        # تبويب المراقبة
        self.create_monitoring_tab()
        
        # تبويب المونتاج
        self.create_editing_tab()
        
        # تبويب النشر
        self.create_publishing_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        # تبويب السجلات
        self.create_logs_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)
        
        # عنوان اللوحة
        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # إحصائيات سريعة
        stats_frame = self.create_stats_frame()
        layout.addWidget(stats_frame)
        
        # أزرار التحكم السريع
        controls_frame = self.create_controls_frame()
        layout.addWidget(controls_frame)
        
        # سجل الأنشطة الحديثة
        activity_frame = self.create_activity_frame()
        layout.addWidget(activity_frame)
        
        layout.addStretch()
        
        self.tab_widget.addTab(dashboard_widget, "لوحة التحكم")
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QGroupBox("إحصائيات اليوم")
        layout = QGridLayout(frame)
        
        # إحصائيات مختلفة
        stats = [
            ("المحتوى المجمع", "0", "green"),
            ("الفيديوهات المعالجة", "0", "blue"),
            ("المنشورات", "0", "orange"),
            ("الأخطاء", "0", "red")
        ]
        
        self.stats_labels = {}
        
        for i, (name, value, color) in enumerate(stats):
            # اسم الإحصائية
            name_label = QLabel(name)
            name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(name_label, 0, i)
            
            # قيمة الإحصائية
            value_label = QLabel(value)
            value_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
            value_label.setStyleSheet(f"color: {color};")
            value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(value_label, 1, i)
            
            self.stats_labels[name] = value_label
        
        return frame
    
    def create_controls_frame(self):
        """إنشاء إطار أزرار التحكم"""
        frame = QGroupBox("التحكم السريع")
        layout = QHBoxLayout(frame)
        
        # زر بدء المراقبة
        self.start_monitoring_btn = QPushButton("بدء المراقبة")
        self.start_monitoring_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.start_monitoring_btn.clicked.connect(self.toggle_monitoring)
        layout.addWidget(self.start_monitoring_btn)
        
        # زر إيقاف الطوارئ
        emergency_stop_btn = QPushButton("إيقاف طوارئ")
        emergency_stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        emergency_stop_btn.clicked.connect(self.emergency_stop)
        layout.addWidget(emergency_stop_btn)
        
        # زر تحديث الحالة
        refresh_btn = QPushButton("تحديث الحالة")
        refresh_btn.clicked.connect(self.refresh_status)
        layout.addWidget(refresh_btn)
        
        return frame
    
    def create_activity_frame(self):
        """إنشاء إطار سجل الأنشطة"""
        frame = QGroupBox("الأنشطة الحديثة")
        layout = QVBoxLayout(frame)
        
        # منطقة النص للأنشطة
        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(150)
        self.activity_text.setReadOnly(True)
        layout.addWidget(self.activity_text)
        
        return frame

    def create_accounts_tab(self):
        """إنشاء تبويب الحسابات"""
        accounts_widget = QWidget()
        layout = QVBoxLayout(accounts_widget)

        # عنوان
        title_label = QLabel("إدارة الحسابات")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # منطقة التمرير للحسابات
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # حسابات المنصات
        platforms = ["snapchat", "tiktok", "kick"]
        self.account_frames = {}

        for platform in platforms:
            frame = self.create_account_frame(platform)
            self.account_frames[platform] = frame
            scroll_layout.addWidget(frame)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(accounts_widget, "الحسابات")

    def create_account_frame(self, platform: str):
        """إنشاء إطار حساب منصة"""
        platform_names = {
            "snapchat": "سناب شات",
            "tiktok": "تيك توك",
            "kick": "كيك"
        }

        frame = QGroupBox(platform_names.get(platform, platform))
        layout = QGridLayout(frame)

        # حالة الاتصال
        status_label = QLabel("الحالة:")
        layout.addWidget(status_label, 0, 0)

        status_value = QLabel("غير متصل")
        status_value.setStyleSheet("color: red;")
        layout.addWidget(status_value, 0, 1)

        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        layout.addWidget(username_label, 1, 0)

        from PyQt6.QtWidgets import QLineEdit
        username_input = QLineEdit()
        layout.addWidget(username_input, 1, 1)

        # الرمز المميز
        token_label = QLabel("الرمز المميز:")
        layout.addWidget(token_label, 2, 0)

        token_input = QLineEdit()
        token_input.setEchoMode(QLineEdit.EchoMode.Password)
        layout.addWidget(token_input, 2, 1)

        # أزرار التحكم
        connect_btn = QPushButton("اتصال")
        connect_btn.clicked.connect(lambda: self.connect_account(platform))
        layout.addWidget(connect_btn, 3, 0)

        test_btn = QPushButton("اختبار")
        test_btn.clicked.connect(lambda: self.test_account(platform))
        layout.addWidget(test_btn, 3, 1)

        return frame

    def create_monitoring_tab(self):
        """إنشاء تبويب المراقبة"""
        monitoring_widget = QWidget()
        layout = QVBoxLayout(monitoring_widget)

        # عنوان
        title_label = QLabel("مراقبة المحتوى")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # إعدادات المراقبة
        settings_frame = QGroupBox("إعدادات المراقبة")
        settings_layout = QGridLayout(settings_frame)

        from PyQt6.QtWidgets import QCheckBox, QSpinBox, QComboBox

        # نوع المحتوى
        content_type_label = QLabel("نوع المحتوى:")
        settings_layout.addWidget(content_type_label, 0, 0)

        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["الكل", "ستوريات فقط", "لايفات فقط"])
        settings_layout.addWidget(self.content_type_combo, 0, 1)

        # فترة المراقبة
        interval_label = QLabel("فترة المراقبة (دقائق):")
        settings_layout.addWidget(interval_label, 1, 0)

        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(5)
        settings_layout.addWidget(self.interval_spin, 1, 1)

        # المراقبة التلقائية
        self.auto_monitoring_check = QCheckBox("مراقبة تلقائية")
        settings_layout.addWidget(self.auto_monitoring_check, 2, 0, 1, 2)

        layout.addWidget(settings_frame)

        # سجل المراقبة
        log_frame = QGroupBox("سجل المراقبة")
        log_layout = QVBoxLayout(log_frame)

        self.monitoring_log = QTextEdit()
        self.monitoring_log.setReadOnly(True)
        log_layout.addWidget(self.monitoring_log)

        layout.addWidget(log_frame)

        self.tab_widget.addTab(monitoring_widget, "المراقبة")

    def create_editing_tab(self):
        """إنشاء تبويب المونتاج"""
        editing_widget = QWidget()
        layout = QVBoxLayout(editing_widget)

        # عنوان
        title_label = QLabel("إعدادات المونتاج")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # إعدادات المونتاج
        editing_frame = QGroupBox("إعدادات المونتاج التلقائي")
        editing_layout = QGridLayout(editing_frame)

        from PyQt6.QtWidgets import QComboBox, QCheckBox

        # قالب المونتاج
        template_label = QLabel("قالب المونتاج:")
        editing_layout.addWidget(template_label, 0, 0)

        self.template_combo = QComboBox()
        self.template_combo.addItems(["افتراضي", "احترافي", "ترند", "مضحك"])
        editing_layout.addWidget(self.template_combo, 0, 1)

        # إضافة موسيقى
        self.add_music_check = QCheckBox("إضافة موسيقى تلقائية")
        self.add_music_check.setChecked(True)
        editing_layout.addWidget(self.add_music_check, 1, 0, 1, 2)

        # إضافة نصوص
        self.add_captions_check = QCheckBox("إضافة نصوص تلقائية")
        self.add_captions_check.setChecked(True)
        editing_layout.addWidget(self.add_captions_check, 2, 0, 1, 2)

        # إضافة مؤثرات
        self.add_effects_check = QCheckBox("إضافة مؤثرات بصرية")
        self.add_effects_check.setChecked(True)
        editing_layout.addWidget(self.add_effects_check, 3, 0, 1, 2)

        # جودة الإخراج
        quality_label = QLabel("جودة الإخراج:")
        editing_layout.addWidget(quality_label, 4, 0)

        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["720p", "1080p", "4K"])
        self.quality_combo.setCurrentText("1080p")
        editing_layout.addWidget(self.quality_combo, 4, 1)

        layout.addWidget(editing_frame)

        # معاينة المونتاج
        preview_frame = QGroupBox("معاينة المونتاج")
        preview_layout = QVBoxLayout(preview_frame)

        preview_label = QLabel("لا توجد معاينة متاحة")
        preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_label.setStyleSheet("border: 2px dashed #ccc; padding: 50px;")
        preview_layout.addWidget(preview_label)

        layout.addWidget(preview_frame)

        self.tab_widget.addTab(editing_widget, "المونتاج")

    def create_publishing_tab(self):
        """إنشاء تبويب النشر"""
        publishing_widget = QWidget()
        layout = QVBoxLayout(publishing_widget)

        # عنوان
        title_label = QLabel("إعدادات النشر")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # إعدادات النشر
        publishing_frame = QGroupBox("إعدادات النشر التلقائي")
        publishing_layout = QGridLayout(publishing_frame)

        from PyQt6.QtWidgets import QTimeEdit, QLineEdit, QCheckBox
        from PyQt6.QtCore import QTime

        # النشر التلقائي
        self.auto_publish_check = QCheckBox("نشر تلقائي")
        self.auto_publish_check.setChecked(True)
        publishing_layout.addWidget(self.auto_publish_check, 0, 0, 1, 2)

        # أفضل وقت للنشر
        best_time_label = QLabel("أفضل وقت للنشر:")
        publishing_layout.addWidget(best_time_label, 1, 0)

        self.best_time_edit = QTimeEdit()
        self.best_time_edit.setTime(QTime(20, 0))  # 8:00 PM
        publishing_layout.addWidget(self.best_time_edit, 1, 1)

        # قالب الوصف
        description_label = QLabel("قالب الوصف:")
        publishing_layout.addWidget(description_label, 2, 0)

        self.description_template = QTextEdit()
        self.description_template.setMaximumHeight(100)
        self.description_template.setPlainText("محتوى رائع! 🔥")
        publishing_layout.addWidget(self.description_template, 2, 1)

        # الهاشتاغات
        hashtags_label = QLabel("الهاشتاغات:")
        publishing_layout.addWidget(hashtags_label, 3, 0)

        self.hashtags_input = QLineEdit()
        self.hashtags_input.setText("#ترند #مضحك #فيرال")
        publishing_layout.addWidget(self.hashtags_input, 3, 1)

        layout.addWidget(publishing_frame)

        # سجل النشر
        publish_log_frame = QGroupBox("سجل النشر")
        publish_log_layout = QVBoxLayout(publish_log_frame)

        self.publish_log = QTextEdit()
        self.publish_log.setReadOnly(True)
        publish_log_layout.addWidget(self.publish_log)

        layout.addWidget(publish_log_frame)

        self.tab_widget.addTab(publishing_widget, "النشر")

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # عنوان
        title_label = QLabel("الإعدادات العامة")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # إعدادات التطبيق
        app_frame = QGroupBox("إعدادات التطبيق")
        app_layout = QGridLayout(app_frame)

        from PyQt6.QtWidgets import QComboBox

        # اللغة
        language_label = QLabel("اللغة:")
        app_layout.addWidget(language_label, 0, 0)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        app_layout.addWidget(self.language_combo, 0, 1)

        # المظهر
        theme_label = QLabel("المظهر:")
        app_layout.addWidget(theme_label, 1, 0)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["داكن", "فاتح", "تلقائي"])
        app_layout.addWidget(self.theme_combo, 1, 1)

        # بدء تلقائي
        self.auto_start_check = QCheckBox("بدء تلقائي مع النظام")
        app_layout.addWidget(self.auto_start_check, 2, 0, 1, 2)

        # تصغير للشريط
        self.minimize_tray_check = QCheckBox("تصغير إلى شريط المهام")
        self.minimize_tray_check.setChecked(True)
        app_layout.addWidget(self.minimize_tray_check, 3, 0, 1, 2)

        layout.addWidget(app_frame)

        # أزرار الإعدادات
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ الإعدادات")
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)

        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        layout.addStretch()

        self.tab_widget.addTab(settings_widget, "الإعدادات")

    def create_logs_tab(self):
        """إنشاء تبويب السجلات"""
        logs_widget = QWidget()
        layout = QVBoxLayout(logs_widget)

        # عنوان
        title_label = QLabel("سجلات النظام")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # أزرار التحكم في السجلات
        controls_layout = QHBoxLayout()

        refresh_logs_btn = QPushButton("تحديث السجلات")
        refresh_logs_btn.clicked.connect(self.refresh_logs)
        controls_layout.addWidget(refresh_logs_btn)

        clear_logs_btn = QPushButton("مسح السجلات")
        clear_logs_btn.clicked.connect(self.clear_logs)
        controls_layout.addWidget(clear_logs_btn)

        export_logs_btn = QPushButton("تصدير السجلات")
        export_logs_btn.clicked.connect(self.export_logs)
        controls_layout.addWidget(export_logs_btn)

        controls_layout.addStretch()
        layout.addLayout(controls_layout)

        # منطقة السجلات
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.logs_text)

        self.tab_widget.addTab(logs_widget, "السجلات")

    def setup_system_tray(self):
        """إعداد أيقونة شريط المهام"""
        try:
            from PyQt6.QtWidgets import QSystemTrayIcon, QMenu
            from PyQt6.QtGui import QIcon

            if QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon = QSystemTrayIcon(self)

                # إنشاء قائمة شريط المهام
                tray_menu = QMenu()

                show_action = tray_menu.addAction("إظهار")
                show_action.triggered.connect(self.show)

                hide_action = tray_menu.addAction("إخفاء")
                hide_action.triggered.connect(self.hide)

                tray_menu.addSeparator()

                quit_action = tray_menu.addAction("خروج")
                quit_action.triggered.connect(self.close)

                self.tray_icon.setContextMenu(tray_menu)
                self.tray_icon.show()

                # ربط النقر المزدوج
                self.tray_icon.activated.connect(self.tray_icon_activated)

        except Exception as e:
            self.logger.error(f"خطأ في إعداد شريط المهام: {str(e)}")

    def setup_timers(self):
        """إعداد المؤقتات"""
        # مؤقت تحديث الحالة
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # كل 5 ثوان

        # مؤقت فحص الأمان
        self.security_timer = QTimer()
        self.security_timer.timeout.connect(self.check_security)
        self.security_timer.start(30000)  # كل 30 ثانية

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط الإشارات المخصصة
        self.status_updated.connect(self.update_status_bar)
        self.progress_updated.connect(self.update_progress_bar)

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحميل إعدادات التطبيق
            language = self.config_manager.get_setting("app_settings", "language", "ar")
            theme = self.config_manager.get_setting("app_settings", "theme", "dark")

            # تطبيق الإعدادات على الواجهة
            if hasattr(self, 'language_combo'):
                if language == "ar":
                    self.language_combo.setCurrentText("العربية")
                else:
                    self.language_combo.setCurrentText("English")

            if hasattr(self, 'theme_combo'):
                if theme == "dark":
                    self.theme_combo.setCurrentText("داكن")
                elif theme == "light":
                    self.theme_combo.setCurrentText("فاتح")
                else:
                    self.theme_combo.setCurrentText("تلقائي")

            # تحميل حالة الحسابات
            self.update_accounts_status()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات التطبيق
            if hasattr(self, 'language_combo'):
                language = "ar" if self.language_combo.currentText() == "العربية" else "en"
                self.config_manager.set_setting("app_settings", "language", language)

            if hasattr(self, 'theme_combo'):
                theme_map = {"داكن": "dark", "فاتح": "light", "تلقائي": "auto"}
                theme = theme_map.get(self.theme_combo.currentText(), "dark")
                self.config_manager.set_setting("app_settings", "theme", theme)

            # حفظ إعدادات المحتوى
            if hasattr(self, 'content_type_combo'):
                content_types = {
                    "الكل": ["stories", "lives"],
                    "ستوريات فقط": ["stories"],
                    "لايفات فقط": ["lives"]
                }
                selected = content_types.get(self.content_type_combo.currentText(), ["stories", "lives"])
                self.config_manager.set_setting("content_settings", "content_types", selected)

            self.status_updated.emit("تم حفظ الإعدادات بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
            self.status_updated.emit("خطأ في حفظ الإعدادات")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self,
                "إعادة تعيين الإعدادات",
                "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # إعادة تعيين الإعدادات للقيم الافتراضية
                self.language_combo.setCurrentText("العربية")
                self.theme_combo.setCurrentText("داكن")
                self.content_type_combo.setCurrentText("الكل")

                self.status_updated.emit("تم إعادة تعيين الإعدادات")

        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين الإعدادات: {str(e)}")

    # وظائف التحكم الأساسية
    def toggle_monitoring(self):
        """تبديل حالة المراقبة"""
        try:
            if not self.is_monitoring:
                # بدء المراقبة
                if self.start_monitoring():
                    self.is_monitoring = True
                    self.start_monitoring_btn.setText("إيقاف المراقبة")
                    self.start_monitoring_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #f44336;
                            color: white;
                            border: none;
                            padding: 10px;
                            font-size: 14px;
                            border-radius: 5px;
                        }
                        QPushButton:hover {
                            background-color: #da190b;
                        }
                    """)
                    self.status_updated.emit("تم بدء المراقبة")
            else:
                # إيقاف المراقبة
                self.stop_monitoring()
                self.is_monitoring = False
                self.start_monitoring_btn.setText("بدء المراقبة")
                self.start_monitoring_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 10px;
                        font-size: 14px;
                        border-radius: 5px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                self.status_updated.emit("تم إيقاف المراقبة")

        except Exception as e:
            self.logger.error(f"خطأ في تبديل المراقبة: {str(e)}")

    def start_monitoring(self) -> bool:
        """بدء المراقبة"""
        try:
            # التحقق من الحسابات المتصلة
            active_accounts = []
            for platform in ["snapchat", "tiktok", "kick"]:
                if self.config_manager.is_account_active(platform):
                    active_accounts.append(platform)

            if not active_accounts:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "لا توجد حسابات متصلة! يرجى ربط حساب واحد على الأقل."
                )
                return False

            # بدء خدمات المراقبة
            self.monitoring_log.append(f"بدء مراقبة المنصات: {', '.join(active_accounts)}")

            # هنا سيتم إضافة منطق المراقبة الفعلي لاحقاً
            return True

        except Exception as e:
            self.logger.error(f"خطأ في بدء المراقبة: {str(e)}")
            return False

    def stop_monitoring(self):
        """إيقاف المراقبة"""
        try:
            self.monitoring_log.append("تم إيقاف المراقبة")
            # هنا سيتم إضافة منطق إيقاف المراقبة لاحقاً

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المراقبة: {str(e)}")

    def emergency_stop(self):
        """إيقاف طوارئ"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            reply = QMessageBox.critical(
                self,
                "إيقاف طوارئ",
                "هل أنت متأكد من إيقاف جميع العمليات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # إيقاف جميع العمليات
                self.stop_monitoring()
                self.is_monitoring = False
                self.is_processing = False

                # تحديث الواجهة
                self.start_monitoring_btn.setText("بدء المراقبة")
                self.status_updated.emit("تم إيقاف جميع العمليات")

                self.logger.warning("تم تنفيذ إيقاف طوارئ")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف الطوارئ: {str(e)}")

    def refresh_status(self):
        """تحديث الحالة"""
        try:
            # تحديث حالة الحسابات
            self.update_accounts_status()

            # تحديث الإحصائيات
            self.update_statistics()

            # تحديث السجلات
            self.refresh_logs()

            self.status_updated.emit("تم تحديث الحالة")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الحالة: {str(e)}")

    def connect_account(self, platform: str):
        """ربط حساب منصة"""
        try:
            # هنا سيتم إضافة منطق ربط الحسابات لاحقاً
            self.status_updated.emit(f"جاري ربط حساب {platform}...")

        except Exception as e:
            self.logger.error(f"خطأ في ربط حساب {platform}: {str(e)}")

    def test_account(self, platform: str):
        """اختبار حساب منصة"""
        try:
            # هنا سيتم إضافة منطق اختبار الحسابات لاحقاً
            self.status_updated.emit(f"جاري اختبار حساب {platform}...")

        except Exception as e:
            self.logger.error(f"خطأ في اختبار حساب {platform}: {str(e)}")

    def update_accounts_status(self):
        """تحديث حالة الحسابات"""
        try:
            for platform in ["snapchat", "tiktok", "kick"]:
                is_active = self.config_manager.is_account_active(platform)
                # تحديث واجهة المستخدم حسب الحالة
                # هنا سيتم إضافة منطق تحديث الواجهة لاحقاً

        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة الحسابات: {str(e)}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # الحصول على التقرير اليومي
            daily_report = activity_logger.get_daily_report()

            # تحديث الإحصائيات في الواجهة
            if "المحتوى المجمع" in self.stats_labels:
                self.stats_labels["المحتوى المجمع"].setText(str(daily_report.get("content_fetched", 0)))

            if "الفيديوهات المعالجة" in self.stats_labels:
                self.stats_labels["الفيديوهات المعالجة"].setText(str(daily_report.get("videos_processed", 0)))

            if "المنشورات" in self.stats_labels:
                self.stats_labels["المنشورات"].setText(str(daily_report.get("videos_published", 0)))

            if "الأخطاء" in self.stats_labels:
                self.stats_labels["الأخطاء"].setText(str(daily_report.get("errors", 0)))

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def refresh_logs(self):
        """تحديث السجلات"""
        try:
            # قراءة ملف السجل
            logs_dir = Path.home() / ".smart_content_app" / "logs"
            log_file = logs_dir / "app.log"

            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    # قراءة آخر 100 سطر
                    lines = f.readlines()
                    recent_lines = lines[-100:] if len(lines) > 100 else lines
                    self.logs_text.setPlainText(''.join(recent_lines))

                    # التمرير للأسفل
                    cursor = self.logs_text.textCursor()
                    cursor.movePosition(cursor.MoveOperation.End)
                    self.logs_text.setTextCursor(cursor)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث السجلات: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self,
                "مسح السجلات",
                "هل أنت متأكد من مسح جميع السجلات؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.logs_text.clear()
                self.status_updated.emit("تم مسح السجلات")

        except Exception as e:
            self.logger.error(f"خطأ في مسح السجلات: {str(e)}")

    def export_logs(self):
        """تصدير السجلات"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            from datetime import datetime

            # اختيار مكان الحفظ
            default_name = f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير السجلات",
                default_name,
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.logs_text.toPlainText())

                self.status_updated.emit(f"تم تصدير السجلات إلى: {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير السجلات: {str(e)}")

    # وظائف الأحداث والإشارات
    def update_status_bar(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.setText(message)

    def update_progress_bar(self, value: int):
        """تحديث شريط التقدم"""
        if value > 0:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(value)
        else:
            self.progress_bar.setVisible(False)

    def update_status(self):
        """تحديث الحالة الدوري"""
        try:
            # فحص حالة الاتصال
            connected_accounts = 0
            for platform in ["snapchat", "tiktok", "kick"]:
                if self.config_manager.is_account_active(platform):
                    connected_accounts += 1

            if connected_accounts > 0:
                self.connection_label.setText(f"متصل ({connected_accounts})")
                self.connection_label.setStyleSheet("color: green;")
            else:
                self.connection_label.setText("غير متصل")
                self.connection_label.setStyleSheet("color: red;")

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في التحديث الدوري: {str(e)}")

    def check_security(self):
        """فحص الأمان الدوري"""
        try:
            if not self.security_manager.is_safe_to_continue():
                self.emergency_stop()
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "تحذير أمني",
                    "تم اكتشاف نشاط مشبوه! تم إيقاف التطبيق للحماية."
                )

        except Exception as e:
            self.logger.error(f"خطأ في فحص الأمان: {str(e)}")

    def tray_icon_activated(self, reason):
        """معالج نقر أيقونة شريط المهام"""
        try:
            from PyQt6.QtWidgets import QSystemTrayIcon

            if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
                if self.isVisible():
                    self.hide()
                else:
                    self.show()
                    self.raise_()
                    self.activateWindow()

        except Exception as e:
            self.logger.error(f"خطأ في معالج شريط المهام: {str(e)}")

    def toggle_status_bar(self, checked: bool):
        """تبديل إظهار شريط الحالة"""
        self.status_bar.setVisible(checked)

    def show_settings(self):
        """إظهار نافذة الإعدادات"""
        self.tab_widget.setCurrentIndex(5)  # تبويب الإعدادات

    def show_about(self):
        """إظهار نافذة حول التطبيق"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            about_text = """
            <h2>Smart Content Creator</h2>
            <p><b>منشئ المحتوى الذكي</b></p>
            <p>الإصدار: 1.0.0</p>
            <p>المطور: Augment Agent</p>
            <p>التاريخ: 2025-07-01</p>
            <br>
            <p>تطبيق سطح مكتب ذكي لجمع ومونتاج ونشر المحتوى تلقائياً</p>
            <p>من منصات التواصل الاجتماعي المختلفة</p>
            """

            QMessageBox.about(self, "حول التطبيق", about_text)

        except Exception as e:
            self.logger.error(f"خطأ في إظهار نافذة حول: {str(e)}")

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        try:
            if hasattr(self, 'minimize_tray_check') and self.minimize_tray_check.isChecked():
                # تصغير إلى شريط المهام بدلاً من الإغلاق
                event.ignore()
                self.hide()
                if hasattr(self, 'tray_icon'):
                    self.tray_icon.showMessage(
                        "Smart Content Creator",
                        "التطبيق يعمل في الخلفية",
                        2000
                    )
            else:
                # إغلاق فعلي
                if self.is_monitoring:
                    self.stop_monitoring()

                self.logger.info("تم إغلاق التطبيق")
                event.accept()

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق التطبيق: {str(e)}")
            event.accept()

    def create_accounts_tab(self):
        """إنشاء تبويب الحسابات"""
        accounts_widget = QWidget()
        layout = QVBoxLayout(accounts_widget)

        # عنوان
        title_label = QLabel("إدارة الحسابات")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # منطقة التمرير للحسابات
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # حسابات المنصات
        platforms = ["snapchat", "tiktok", "kick"]
        self.account_frames = {}

        for platform in platforms:
            frame = self.create_account_frame(platform)
            self.account_frames[platform] = frame
            scroll_layout.addWidget(frame)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(accounts_widget, "الحسابات")

    def create_account_frame(self, platform: str):
        """إنشاء إطار حساب منصة"""
        platform_names = {
            "snapchat": "سناب شات",
            "tiktok": "تيك توك",
            "kick": "كيك"
        }

        frame = QGroupBox(platform_names.get(platform, platform))
        layout = QGridLayout(frame)

        # حالة الاتصال
        status_label = QLabel("الحالة:")
        layout.addWidget(status_label, 0, 0)

        status_value = QLabel("غير متصل")
        status_value.setStyleSheet("color: red;")
        layout.addWidget(status_value, 0, 1)

        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        layout.addWidget(username_label, 1, 0)

        from PyQt6.QtWidgets import QLineEdit
        username_input = QLineEdit()
        layout.addWidget(username_input, 1, 1)

        # الرمز المميز
        token_label = QLabel("الرمز المميز:")
        layout.addWidget(token_label, 2, 0)

        token_input = QLineEdit()
        token_input.setEchoMode(QLineEdit.EchoMode.Password)
        layout.addWidget(token_input, 2, 1)

        # أزرار التحكم
        connect_btn = QPushButton("اتصال")
        connect_btn.clicked.connect(lambda: self.connect_account(platform))
        layout.addWidget(connect_btn, 3, 0)

        test_btn = QPushButton("اختبار")
        test_btn.clicked.connect(lambda: self.test_account(platform))
        layout.addWidget(test_btn, 3, 1)

        return frame
