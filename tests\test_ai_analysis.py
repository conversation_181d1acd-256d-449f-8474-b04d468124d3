#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات نظام الذكاء الاصطناعي
Tests for AI Analysis System
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import cv2
import tempfile

# استيراد الوحدات المطلوب اختبارها
from src.ai_analysis.video_analyzer import VideoAnalyzer
from src.ai_analysis.content_classifier import ContentClassifier
from src.ai_analysis.quality_assessor import QualityAssessor
from src.ai_analysis.trend_analyzer import TrendAnalyzer

class TestVideoAnalyzer:
    """اختبارات محلل الفيديو"""
    
    @pytest.fixture
    def video_analyzer(self, temp_dir):
        """إنشاء كائن VideoAnalyzer للاختبار"""
        return VideoAnalyzer(str(temp_dir))
    
    @pytest.fixture
    def sample_video_path(self, temp_dir):
        """إنشاء فيديو تجريبي للاختبار"""
        video_path = temp_dir / "test_video.mp4"
        
        # إنشاء فيديو وهمي باستخدام OpenCV
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(video_path), fourcc, 30.0, (640, 480))
        
        # إنشاء 30 إطار (ثانية واحدة)
        for i in range(30):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            out.write(frame)
        
        out.release()
        return str(video_path)
    
    def test_initialization(self, video_analyzer):
        """اختبار تهيئة محلل الفيديو"""
        assert video_analyzer is not None
        assert hasattr(video_analyzer, 'models')
        assert hasattr(video_analyzer, 'config')
    
    def test_extract_frames(self, video_analyzer, sample_video_path):
        """اختبار استخراج الإطارات من الفيديو"""
        frames = video_analyzer.extract_frames(sample_video_path, max_frames=10)
        
        assert frames is not None
        assert len(frames) <= 10
        assert all(isinstance(frame, np.ndarray) for frame in frames)
    
    @patch('cv2.dnn.readNetFromDarknet')
    def test_detect_objects(self, mock_net, video_analyzer, sample_video_path):
        """اختبار كشف الكائنات في الفيديو"""
        # إعداد النموذج الوهمي
        mock_model = Mock()
        mock_model.setInput = Mock()
        mock_model.forward = Mock(return_value=[
            np.array([[0.1, 0.2, 0.3, 0.4, 0.9, 0.8, 0.1]])  # مربع محيط وهمي
        ])
        mock_net.return_value = mock_model
        
        # تشغيل الاختبار
        result = video_analyzer.detect_objects(sample_video_path)
        
        # التحقق من النتائج
        assert result is not None
        assert isinstance(result, list)
    
    def test_analyze_motion(self, video_analyzer, sample_video_path):
        """اختبار تحليل الحركة في الفيديو"""
        motion_data = video_analyzer.analyze_motion(sample_video_path)
        
        assert motion_data is not None
        assert "motion_intensity" in motion_data
        assert "motion_vectors" in motion_data
        assert isinstance(motion_data["motion_intensity"], (int, float))
    
    def test_extract_audio_features(self, video_analyzer, sample_video_path):
        """اختبار استخراج خصائص الصوت"""
        with patch('librosa.load') as mock_load:
            # إعداد بيانات صوتية وهمية
            mock_load.return_value = (np.random.random(22050), 22050)  # ثانية واحدة من الصوت
            
            audio_features = video_analyzer.extract_audio_features(sample_video_path)
            
            assert audio_features is not None
            assert "tempo" in audio_features
            assert "spectral_features" in audio_features
            assert "energy" in audio_features

class TestContentClassifier:
    """اختبارات مصنف المحتوى"""
    
    @pytest.fixture
    def content_classifier(self, temp_dir):
        """إنشاء كائن ContentClassifier للاختبار"""
        return ContentClassifier(str(temp_dir))
    
    def test_initialization(self, content_classifier):
        """اختبار تهيئة مصنف المحتوى"""
        assert content_classifier is not None
        assert hasattr(content_classifier, 'models')
        assert hasattr(content_classifier, 'categories')
    
    @patch('transformers.pipeline')
    def test_classify_content_type(self, mock_pipeline, content_classifier):
        """اختبار تصنيف نوع المحتوى"""
        # إعداد النموذج الوهمي
        mock_classifier = Mock()
        mock_classifier.return_value = [
            {"label": "رقص", "score": 0.85},
            {"label": "كوميديا", "score": 0.12}
        ]
        mock_pipeline.return_value = mock_classifier
        
        # تشغيل الاختبار
        video_path = "test_video.mp4"
        result = content_classifier.classify_content_type(video_path)
        
        # التحقق من النتائج
        assert result is not None
        assert "primary_category" in result
        assert "confidence" in result
        assert "all_categories" in result
    
    def test_detect_inappropriate_content(self, content_classifier):
        """اختبار كشف المحتوى غير المناسب"""
        with patch.object(content_classifier, '_analyze_visual_content') as mock_visual:
            with patch.object(content_classifier, '_analyze_audio_content') as mock_audio:
                # إعداد النتائج الوهمية
                mock_visual.return_value = {"inappropriate": False, "confidence": 0.95}
                mock_audio.return_value = {"inappropriate": False, "confidence": 0.90}
                
                result = content_classifier.detect_inappropriate_content("test_video.mp4")
                
                assert result is not None
                assert "is_appropriate" in result
                assert "visual_analysis" in result
                assert "audio_analysis" in result
    
    def test_extract_keywords(self, content_classifier):
        """اختبار استخراج الكلمات المفتاحية"""
        sample_text = "هذا فيديو رائع عن الطبخ والوصفات اللذيذة"
        
        keywords = content_classifier.extract_keywords(sample_text)
        
        assert keywords is not None
        assert isinstance(keywords, list)
        assert len(keywords) > 0

class TestQualityAssessor:
    """اختبارات مقيم الجودة"""
    
    @pytest.fixture
    def quality_assessor(self, temp_dir):
        """إنشاء كائن QualityAssessor للاختبار"""
        return QualityAssessor(str(temp_dir))
    
    def test_initialization(self, quality_assessor):
        """اختبار تهيئة مقيم الجودة"""
        assert quality_assessor is not None
        assert hasattr(quality_assessor, 'quality_metrics')
        assert hasattr(quality_assessor, 'thresholds')
    
    def test_assess_video_quality(self, quality_assessor):
        """اختبار تقييم جودة الفيديو"""
        # إنشاء فيديو وهمي
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
            video_path = temp_video.name
        
        with patch('cv2.VideoCapture') as mock_cap:
            # إعداد كائن الفيديو الوهمي
            mock_video = Mock()
            mock_video.get.side_effect = lambda prop: {
                cv2.CAP_PROP_FRAME_WIDTH: 1920,
                cv2.CAP_PROP_FRAME_HEIGHT: 1080,
                cv2.CAP_PROP_FPS: 30,
                cv2.CAP_PROP_FRAME_COUNT: 900
            }.get(prop, 0)
            mock_video.read.return_value = (True, np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8))
            mock_cap.return_value = mock_video
            
            result = quality_assessor.assess_video_quality(video_path)
            
            assert result is not None
            assert "overall_score" in result
            assert "resolution_score" in result
            assert "fps_score" in result
            assert "duration_score" in result
    
    def test_calculate_sharpness(self, quality_assessor):
        """اختبار حساب وضوح الصورة"""
        # إنشاء صورة وهمية
        image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        sharpness = quality_assessor.calculate_sharpness(image)
        
        assert sharpness is not None
        assert isinstance(sharpness, (int, float))
        assert sharpness >= 0
    
    def test_detect_blur(self, quality_assessor):
        """اختبار كشف الضبابية"""
        # إنشاء صورة واضحة
        clear_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # إنشاء صورة ضبابية
        blurry_image = cv2.GaussianBlur(clear_image, (15, 15), 0)
        
        clear_result = quality_assessor.detect_blur(clear_image)
        blurry_result = quality_assessor.detect_blur(blurry_image)
        
        assert clear_result["is_blurry"] == False
        assert blurry_result["is_blurry"] == True

class TestTrendAnalyzer:
    """اختبارات محلل الاتجاهات"""
    
    @pytest.fixture
    def trend_analyzer(self, temp_dir):
        """إنشاء كائن TrendAnalyzer للاختبار"""
        return TrendAnalyzer(str(temp_dir))
    
    def test_initialization(self, trend_analyzer):
        """اختبار تهيئة محلل الاتجاهات"""
        assert trend_analyzer is not None
        assert hasattr(trend_analyzer, 'trend_data')
        assert hasattr(trend_analyzer, 'models')
    
    @patch('requests.get')
    def test_fetch_trending_topics(self, mock_get, trend_analyzer):
        """اختبار جلب المواضيع الرائجة"""
        # إعداد الاستجابة الوهمية
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "trends": [
                {"name": "موضوع_رائج_1", "volume": 10000},
                {"name": "موضوع_رائج_2", "volume": 8000}
            ]
        }
        mock_get.return_value = mock_response
        
        result = trend_analyzer.fetch_trending_topics()
        
        assert result is not None
        assert len(result) > 0
        assert all("name" in trend for trend in result)
    
    def test_analyze_content_trends(self, trend_analyzer):
        """اختبار تحليل اتجاهات المحتوى"""
        sample_content = {
            "title": "فيديو رقص جديد",
            "description": "رقصة رائجة على تيك توك",
            "tags": ["رقص", "تيك_توك", "ترند"]
        }
        
        result = trend_analyzer.analyze_content_trends(sample_content)
        
        assert result is not None
        assert "trend_score" in result
        assert "matching_trends" in result
        assert "recommendations" in result
    
    def test_predict_viral_potential(self, trend_analyzer):
        """اختبار توقع إمكانية الانتشار"""
        content_features = {
            "duration": 15,
            "quality_score": 0.85,
            "trend_alignment": 0.75,
            "engagement_history": 0.60
        }
        
        result = trend_analyzer.predict_viral_potential(content_features)
        
        assert result is not None
        assert "viral_score" in result
        assert "confidence" in result
        assert 0 <= result["viral_score"] <= 1

@pytest.mark.integration
class TestAISystemIntegration:
    """اختبارات التكامل بين أنظمة الذكاء الاصطناعي"""

    @pytest.fixture
    def ai_systems(self, temp_dir):
        """إنشاء جميع أنظمة الذكاء الاصطناعي"""
        return {
            "video_analyzer": VideoAnalyzer(str(temp_dir)),
            "content_classifier": ContentClassifier(str(temp_dir)),
            "quality_assessor": QualityAssessor(str(temp_dir)),
            "trend_analyzer": TrendAnalyzer(str(temp_dir))
        }

    def test_full_analysis_pipeline(self, ai_systems, sample_video_path):
        """اختبار خط إنتاج التحليل الكامل"""
        video_analyzer = ai_systems["video_analyzer"]
        content_classifier = ai_systems["content_classifier"]
        quality_assessor = ai_systems["quality_assessor"]
        trend_analyzer = ai_systems["trend_analyzer"]

        # تحليل الفيديو
        with patch.object(video_analyzer, 'extract_frames') as mock_frames:
            mock_frames.return_value = [np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)]

            # تصنيف المحتوى
            with patch.object(content_classifier, 'classify_content_type') as mock_classify:
                mock_classify.return_value = {
                    "primary_category": "رقص",
                    "confidence": 0.85,
                    "all_categories": [{"label": "رقص", "score": 0.85}]
                }

                # تقييم الجودة
                with patch.object(quality_assessor, 'assess_video_quality') as mock_quality:
                    mock_quality.return_value = {
                        "overall_score": 0.80,
                        "resolution_score": 0.85,
                        "fps_score": 0.90,
                        "duration_score": 0.75
                    }

                    # تحليل الاتجاهات
                    with patch.object(trend_analyzer, 'analyze_content_trends') as mock_trends:
                        mock_trends.return_value = {
                            "trend_score": 0.70,
                            "matching_trends": ["رقص", "تيك_توك"],
                            "recommendations": ["إضافة موسيقى رائجة"]
                        }

                        # تشغيل خط الإنتاج الكامل
                        analysis_result = {
                            "video_analysis": video_analyzer.extract_frames(sample_video_path),
                            "content_classification": content_classifier.classify_content_type(sample_video_path),
                            "quality_assessment": quality_assessor.assess_video_quality(sample_video_path),
                            "trend_analysis": trend_analyzer.analyze_content_trends({
                                "title": "فيديو رقص",
                                "description": "رقصة جديدة"
                            })
                        }

                        # التحقق من النتائج
                        assert analysis_result["video_analysis"] is not None
                        assert analysis_result["content_classification"]["primary_category"] == "رقص"
                        assert analysis_result["quality_assessment"]["overall_score"] > 0
                        assert analysis_result["trend_analysis"]["trend_score"] > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
