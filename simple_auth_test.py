#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام المصادقة المتقدم
"""

import sys
import tempfile
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_import():
    """اختبار استيراد النظام"""
    try:
        from security.advanced_authentication import (
            AdvancedAuthenticationSystem,
            AuthenticationMethod,
            SecurityLevel
        )
        print("✅ تم استيراد النظام بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل استيراد النظام: {str(e)}")
        return False

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    try:
        from security.advanced_authentication import (
            AdvancedAuthenticationSystem,
            SecurityLevel
        )
        
        # إنشاء مجلد مؤقت
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 استخدام مجلد مؤقت: {temp_dir}")
            
            # إنشاء النظام
            print("🔧 إنشاء نظام المصادقة...")
            auth_system = AdvancedAuthenticationSystem(base_dir=temp_dir)
            print("✅ تم إنشاء النظام بنجاح")
            
            # إنشاء مستخدم
            print("👤 إنشاء مستخدم تجريبي...")
            success, result = auth_system.create_user(
                username="testuser",
                email="<EMAIL>",
                password="TestPassword123!",
                security_level=SecurityLevel.MEDIUM
            )
            
            if success:
                print(f"✅ تم إنشاء المستخدم: {result}")
                user_id = result
            else:
                print(f"❌ فشل إنشاء المستخدم: {result}")
                return False
            
            # تسجيل الدخول
            print("🔑 اختبار تسجيل الدخول...")
            success, message, session_id = auth_system.authenticate_user(
                username="testuser",
                password="TestPassword123!",
                ip_address="127.0.0.1",
                user_agent="Test Client"
            )
            
            if success:
                print(f"✅ تم تسجيل الدخول بنجاح: {session_id}")
            else:
                print(f"❌ فشل تسجيل الدخول: {message}")
                return False
            
            # التحقق من الجلسة
            print("🔍 اختبار التحقق من الجلسة...")
            is_valid, user, error = auth_system.validate_session(
                session_id=session_id,
                ip_address="127.0.0.1"
            )
            
            if is_valid and user:
                print(f"✅ الجلسة صالحة للمستخدم: {user.username}")
            else:
                print(f"❌ الجلسة غير صالحة: {error}")
                return False
            
            # الحصول على إحصائيات
            print("📊 اختبار الإحصائيات...")
            stats = auth_system.get_authentication_statistics(days=1)
            
            if stats and "total_attempts" in stats:
                print(f"✅ تم الحصول على الإحصائيات: {stats['total_attempts']} محاولة")
            else:
                print("❌ فشل الحصول على الإحصائيات")
                return False
            
            # حالة النظام
            print("⚙️ اختبار حالة النظام...")
            status = auth_system.get_system_status()
            
            if status and "system_health" in status:
                print(f"✅ حالة النظام: {status['system_health']}")
            else:
                print("❌ فشل الحصول على حالة النظام")
                return False
            
            # إيقاف النظام
            print("🛑 إيقاف النظام...")
            auth_system.shutdown()
            print("✅ تم إيقاف النظام بأمان")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """تشغيل الاختبارات"""
    print("🚀 بدء اختبار نظام المصادقة المتقدم")
    print("=" * 50)
    
    # اختبار الاستيراد
    if not test_import():
        print("❌ فشل اختبار الاستيراد")
        return False
    
    # اختبار الوظائف الأساسية
    if not test_basic_functionality():
        print("❌ فشل اختبار الوظائف الأساسية")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ نظام المصادقة المتقدم جاهز للاستخدام!")
    else:
        print("\n❌ يوجد مشاكل في النظام تحتاج إلى إصلاح")
    
    sys.exit(0 if success else 1)
