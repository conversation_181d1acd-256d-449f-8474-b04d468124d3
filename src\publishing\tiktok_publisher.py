#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ناشر TikTok - TikTok Publisher
يقوم بنشر المحتوى على TikTok باستخدام API الرسمي
"""

import logging
import requests
import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
import hmac
import base64
from urllib.parse import urlencode, quote

class TikTokPost:
    """منشور TikTok"""
    
    def __init__(self, video_path: str, title: str = "", description: str = ""):
        self.video_path = video_path
        self.title = title
        self.description = description
        self.hashtags = []
        self.privacy_level = "PUBLIC_TO_EVERYONE"  # PUBLIC_TO_EVERYONE, MUTUAL_FOLLOW_FRIENDS, SELF_ONLY
        self.allows_comments = True
        self.allows_duet = True
        self.allows_stitch = True
        self.disable_duet = False
        self.disable_stitch = False
        self.brand_content_toggle = False
        self.brand_organic_toggle = False
        
        # معلومات إضافية
        self.created_at = datetime.now()
        self.scheduled_time = None
        self.post_id = None
        self.status = "draft"  # draft, scheduled, uploading, published, failed
        self.upload_progress = 0.0
        self.error_message = None
        
        # إحصائيات (بعد النشر)
        self.views = 0
        self.likes = 0
        self.comments = 0
        self.shares = 0
        self.engagement_rate = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "video_path": self.video_path,
            "title": self.title,
            "description": self.description,
            "hashtags": self.hashtags,
            "privacy_level": self.privacy_level,
            "allows_comments": self.allows_comments,
            "allows_duet": self.allows_duet,
            "allows_stitch": self.allows_stitch,
            "created_at": self.created_at.isoformat(),
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None,
            "post_id": self.post_id,
            "status": self.status,
            "upload_progress": self.upload_progress,
            "error_message": self.error_message,
            "views": self.views,
            "likes": self.likes,
            "comments": self.comments,
            "shares": self.shares,
            "engagement_rate": self.engagement_rate
        }

class TikTokPublisher:
    """ناشر TikTok"""
    
    def __init__(self, config_manager, security_manager):
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات TikTok
        self.settings = self._load_tiktok_settings()
        
        # معلومات API
        self.api_base_url = "https://open-api.tiktok.com"
        self.client_key = None
        self.client_secret = None
        self.access_token = None
        self.refresh_token = None
        self.user_id = None
        
        # تحميل بيانات المصادقة
        self._load_auth_credentials()
        
        # إحصائيات
        self.stats = {
            "posts_published": 0,
            "posts_failed": 0,
            "total_views": 0,
            "total_likes": 0,
            "total_comments": 0,
            "total_shares": 0,
            "success_rate": 0.0
        }
        
        # قائمة المنشورات
        self.posts = []
    
    def _load_tiktok_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات TikTok"""
        try:
            return self.config_manager.get_setting("publishing_settings", "tiktok", {
                "auto_publish": True,
                "default_privacy": "PUBLIC_TO_EVERYONE",
                "allow_comments": True,
                "allow_duet": True,
                "allow_stitch": True,
                "max_retries": 3,
                "retry_delay": 60,
                "upload_timeout": 300,
                "quality_check": True,
                "auto_hashtags": True,
                "max_hashtags": 30,
                "auto_description": True,
                "max_description_length": 2200,
                "optimal_posting_times": [
                    {"day": "monday", "hours": [18, 19, 20]},
                    {"day": "tuesday", "hours": [18, 19, 20]},
                    {"day": "wednesday", "hours": [18, 19, 20]},
                    {"day": "thursday", "hours": [18, 19, 20]},
                    {"day": "friday", "hours": [17, 18, 19, 20, 21]},
                    {"day": "saturday", "hours": [10, 11, 12, 17, 18, 19]},
                    {"day": "sunday", "hours": [10, 11, 12, 17, 18, 19]}
                ]
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات TikTok: {str(e)}")
            return {
                "auto_publish": True,
                "default_privacy": "PUBLIC_TO_EVERYONE",
                "allow_comments": True,
                "allow_duet": True,
                "allow_stitch": True,
                "max_retries": 3,
                "retry_delay": 60,
                "upload_timeout": 300
            }
    
    def _load_auth_credentials(self):
        """تحميل بيانات المصادقة"""
        try:
            # تحميل بيانات التطبيق
            app_credentials = self.security_manager.get_encrypted_data("tiktok_app_credentials")
            if app_credentials:
                self.client_key = app_credentials.get("client_key")
                self.client_secret = app_credentials.get("client_secret")
            
            # تحميل رموز الوصول
            user_tokens = self.security_manager.get_encrypted_data("tiktok_user_tokens")
            if user_tokens:
                self.access_token = user_tokens.get("access_token")
                self.refresh_token = user_tokens.get("refresh_token")
                self.user_id = user_tokens.get("user_id")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات المصادقة: {str(e)}")
    
    def setup_app_credentials(self, client_key: str, client_secret: str) -> bool:
        """إعداد بيانات التطبيق"""
        try:
            self.client_key = client_key
            self.client_secret = client_secret
            
            # حفظ البيانات مشفرة
            app_credentials = {
                "client_key": client_key,
                "client_secret": client_secret
            }
            
            self.security_manager.store_encrypted_data("tiktok_app_credentials", app_credentials)
            
            self.logger.info("تم إعداد بيانات التطبيق بنجاح")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد بيانات التطبيق: {str(e)}")
            return False
    
    def get_authorization_url(self, redirect_uri: str, state: str = None) -> str:
        """الحصول على رابط التفويض"""
        try:
            if not self.client_key:
                raise ValueError("لم يتم إعداد بيانات التطبيق")
            
            # إنشاء state عشوائي إذا لم يتم توفيره
            if not state:
                state = hashlib.md5(str(time.time()).encode()).hexdigest()
            
            # معاملات التفويض
            params = {
                "client_key": self.client_key,
                "scope": "user.info.basic,video.upload,video.publish",
                "response_type": "code",
                "redirect_uri": redirect_uri,
                "state": state
            }
            
            auth_url = f"https://www.tiktok.com/auth/authorize/?{urlencode(params)}"
            
            self.logger.info("تم إنشاء رابط التفويض")
            return auth_url
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رابط التفويض: {str(e)}")
            raise
    
    def exchange_code_for_token(self, code: str, redirect_uri: str) -> bool:
        """تبديل الكود برمز الوصول"""
        try:
            if not self.client_key or not self.client_secret:
                raise ValueError("لم يتم إعداد بيانات التطبيق")
            
            # إعداد البيانات
            data = {
                "client_key": self.client_key,
                "client_secret": self.client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": redirect_uri
            }
            
            # إرسال الطلب
            response = requests.post(
                f"{self.api_base_url}/oauth/token/",
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                
                if token_data.get("data"):
                    data = token_data["data"]
                    self.access_token = data.get("access_token")
                    self.refresh_token = data.get("refresh_token")
                    
                    # الحصول على معلومات المستخدم
                    user_info = self.get_user_info()
                    if user_info:
                        self.user_id = user_info.get("open_id")
                    
                    # حفظ الرموز مشفرة
                    user_tokens = {
                        "access_token": self.access_token,
                        "refresh_token": self.refresh_token,
                        "user_id": self.user_id,
                        "expires_at": (datetime.now() + timedelta(seconds=data.get("expires_in", 86400))).isoformat()
                    }
                    
                    self.security_manager.store_encrypted_data("tiktok_user_tokens", user_tokens)
                    
                    self.logger.info("تم الحصول على رمز الوصول بنجاح")
                    return True
                else:
                    self.logger.error(f"خطأ في الاستجابة: {token_data}")
                    return False
            else:
                self.logger.error(f"فشل في الحصول على رمز الوصول: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في تبديل الكود: {str(e)}")
            return False
    
    def refresh_access_token(self) -> bool:
        """تجديد رمز الوصول"""
        try:
            if not self.refresh_token:
                self.logger.error("لا يوجد رمز تجديد")
                return False
            
            data = {
                "client_key": self.client_key,
                "client_secret": self.client_secret,
                "grant_type": "refresh_token",
                "refresh_token": self.refresh_token
            }
            
            response = requests.post(
                f"{self.api_base_url}/oauth/refresh_token/",
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                
                if token_data.get("data"):
                    data = token_data["data"]
                    self.access_token = data.get("access_token")
                    self.refresh_token = data.get("refresh_token")
                    
                    # تحديث الرموز المحفوظة
                    user_tokens = self.security_manager.get_encrypted_data("tiktok_user_tokens") or {}
                    user_tokens.update({
                        "access_token": self.access_token,
                        "refresh_token": self.refresh_token,
                        "expires_at": (datetime.now() + timedelta(seconds=data.get("expires_in", 86400))).isoformat()
                    })
                    
                    self.security_manager.store_encrypted_data("tiktok_user_tokens", user_tokens)
                    
                    self.logger.info("تم تجديد رمز الوصول بنجاح")
                    return True
                else:
                    self.logger.error(f"خطأ في تجديد الرمز: {token_data}")
                    return False
            else:
                self.logger.error(f"فشل في تجديد رمز الوصول: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في تجديد رمز الوصول: {str(e)}")
            return False
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات المستخدم"""
        try:
            if not self.access_token:
                self.logger.error("لا يوجد رمز وصول")
                return None
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.api_base_url}/user/info/",
                headers=headers
            )
            
            if response.status_code == 200:
                user_data = response.json()
                if user_data.get("data"):
                    return user_data["data"]["user"]
                else:
                    self.logger.error(f"خطأ في بيانات المستخدم: {user_data}")
                    return None
            else:
                self.logger.error(f"فشل في الحصول على معلومات المستخدم: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات المستخدم: {str(e)}")
            return None
    
    def is_authenticated(self) -> bool:
        """التحقق من حالة المصادقة"""
        return bool(self.access_token and self.client_key and self.client_secret)
    
    def upload_video(self, post: TikTokPost) -> bool:
        """رفع فيديو إلى TikTok"""
        try:
            if not self.is_authenticated():
                self.logger.error("لم يتم تسجيل الدخول")
                return False
            
            if not os.path.exists(post.video_path):
                self.logger.error(f"الملف غير موجود: {post.video_path}")
                return False
            
            self.logger.info(f"بدء رفع الفيديو: {post.video_path}")
            post.status = "uploading"
            post.upload_progress = 0.0
            
            # الخطوة 1: إنشاء جلسة رفع
            upload_session = self._create_upload_session()
            if not upload_session:
                post.status = "failed"
                post.error_message = "فشل في إنشاء جلسة الرفع"
                return False
            
            # الخطوة 2: رفع الفيديو
            upload_result = self._upload_video_file(post, upload_session)
            if not upload_result:
                post.status = "failed"
                post.error_message = "فشل في رفع الفيديو"
                return False
            
            # الخطوة 3: نشر الفيديو
            publish_result = self._publish_video(post, upload_result)
            if publish_result:
                post.status = "published"
                post.upload_progress = 100.0
                self.stats["posts_published"] += 1
                self.posts.append(post)
                self.logger.info(f"تم نشر الفيديو بنجاح: {post.post_id}")
                return True
            else:
                post.status = "failed"
                post.error_message = "فشل في نشر الفيديو"
                self.stats["posts_failed"] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في رفع الفيديو: {str(e)}")
            post.status = "failed"
            post.error_message = str(e)
            self.stats["posts_failed"] += 1
            return False
    
    def _create_upload_session(self) -> Optional[Dict[str, Any]]:
        """إنشاء جلسة رفع"""
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.api_base_url}/share/video/upload/",
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("data"):
                    return result["data"]
                else:
                    self.logger.error(f"خطأ في إنشاء جلسة الرفع: {result}")
                    return None
            else:
                self.logger.error(f"فشل في إنشاء جلسة الرفع: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء جلسة الرفع: {str(e)}")
            return None
    
    def _upload_video_file(self, post: TikTokPost, upload_session: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """رفع ملف الفيديو"""
        try:
            upload_url = upload_session.get("upload_url")
            if not upload_url:
                return None
            
            # قراءة الملف
            with open(post.video_path, 'rb') as video_file:
                video_data = video_file.read()
            
            # رفع الملف
            files = {'video': ('video.mp4', video_data, 'video/mp4')}
            
            response = requests.post(upload_url, files=files)
            
            if response.status_code == 200:
                post.upload_progress = 100.0
                return {"upload_id": upload_session.get("upload_id")}
            else:
                self.logger.error(f"فشل في رفع الملف: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في رفع الملف: {str(e)}")
            return None
    
    def _publish_video(self, post: TikTokPost, upload_result: Dict[str, Any]) -> bool:
        """نشر الفيديو"""
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            # إعداد بيانات النشر
            publish_data = {
                "post_info": {
                    "title": post.title,
                    "description": post.description,
                    "privacy_level": post.privacy_level,
                    "disable_duet": post.disable_duet,
                    "disable_stitch": post.disable_stitch,
                    "disable_comment": not post.allows_comments,
                    "brand_content_toggle": post.brand_content_toggle,
                    "brand_organic_toggle": post.brand_organic_toggle
                },
                "source_info": {
                    "source": "FILE_UPLOAD",
                    "video_id": upload_result.get("upload_id")
                }
            }
            
            response = requests.post(
                f"{self.api_base_url}/share/video/publish/",
                headers=headers,
                json=publish_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("data"):
                    post.post_id = result["data"].get("share_id")
                    return True
                else:
                    self.logger.error(f"خطأ في نشر الفيديو: {result}")
                    return False
            else:
                self.logger.error(f"فشل في نشر الفيديو: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في نشر الفيديو: {str(e)}")
            return False
    
    def get_video_info(self, video_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات فيديو"""
        try:
            if not self.access_token:
                return None
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            params = {"video_id": video_id}
            
            response = requests.get(
                f"{self.api_base_url}/video/query/",
                headers=headers,
                params=params
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("data"):
                    return result["data"]
            
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
            return None
    
    def update_post_stats(self, post: TikTokPost) -> bool:
        """تحديث إحصائيات المنشور"""
        try:
            if not post.post_id:
                return False
            
            video_info = self.get_video_info(post.post_id)
            if video_info:
                video_data = video_info.get("video", {})
                post.views = video_data.get("view_count", 0)
                post.likes = video_data.get("like_count", 0)
                post.comments = video_data.get("comment_count", 0)
                post.shares = video_data.get("share_count", 0)
                
                # حساب معدل التفاعل
                if post.views > 0:
                    post.engagement_rate = ((post.likes + post.comments + post.shares) / post.views) * 100
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")
            return False
    
    def get_publishing_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النشر"""
        # تحديث الإحصائيات الإجمالية
        total_views = sum(post.views for post in self.posts)
        total_likes = sum(post.likes for post in self.posts)
        total_comments = sum(post.comments for post in self.posts)
        total_shares = sum(post.shares for post in self.posts)
        
        self.stats.update({
            "total_views": total_views,
            "total_likes": total_likes,
            "total_comments": total_comments,
            "total_shares": total_shares
        })
        
        # حساب معدل النجاح
        total_posts = self.stats["posts_published"] + self.stats["posts_failed"]
        if total_posts > 0:
            self.stats["success_rate"] = (self.stats["posts_published"] / total_posts) * 100
        
        return self.stats.copy()
    
    def get_posts_history(self) -> List[Dict[str, Any]]:
        """الحصول على تاريخ المنشورات"""
        return [post.to_dict() for post in self.posts]
