#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جالب محتوى TikTok - TikTok Content Fetcher
يجلب Live streams والمحتوى من TikTok
"""

import logging
import requests
import json
import time
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs

from .base_fetcher import BaseFetcher, ContentItem

class TikTokFetcher(BaseFetcher):
    """جالب محتوى TikTok"""
    
    def __init__(self, config_manager, security_manager):
        super().__init__("tiktok", config_manager, security_manager)
        
        # إعدادات TikTok API
        self.base_url = "https://www.tiktok.com"
        self.api_base = "https://www.tiktok.com/api"
        self.live_api = "https://webcast.tiktok.com"
        
        # رؤوس خاصة بـ TikTok
        self.session.headers.update({
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Origin': 'https://www.tiktok.com',
            'Referer': 'https://www.tiktok.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest'
        })
        
        # قائمة المستخدمين المراقبين
        self.monitored_users = self._load_monitored_users()
        
        # معرفات البث المباشر النشط
        self.active_live_streams = {}
        
        # معرفات المحتوى المعروف
        self.known_content_ids = set()
    
    def _load_monitored_users(self) -> List[str]:
        """تحميل قائمة المستخدمين المراقبين"""
        try:
            users = self.config_manager.get_setting("tiktok_settings", "monitored_users", [])
            return users if isinstance(users, list) else []
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المستخدمين المراقبين: {str(e)}")
            return []
    
    def add_monitored_user(self, username: str) -> bool:
        """إضافة مستخدم للمراقبة"""
        try:
            # إزالة @ إذا كانت موجودة
            username = username.lstrip('@')
            
            if username not in self.monitored_users:
                self.monitored_users.append(username)
                self.config_manager.set_setting(
                    "tiktok_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إضافة المستخدم للمراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المستخدم: {str(e)}")
            return False
    
    def remove_monitored_user(self, username: str) -> bool:
        """إزالة مستخدم من المراقبة"""
        try:
            username = username.lstrip('@')
            if username in self.monitored_users:
                self.monitored_users.remove(username)
                self.config_manager.set_setting(
                    "tiktok_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إزالة المستخدم من المراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إزالة المستخدم: {str(e)}")
            return False
    
    def _build_auth_headers(self, token: str) -> Dict[str, str]:
        """بناء رؤوس المصادقة لـ TikTok"""
        return {
            'Authorization': f'Bearer {token}',
            'X-TikTok-Token': token
        }
    
    def test_connection(self) -> bool:
        """اختبار الاتصال مع TikTok"""
        try:
            # اختبار بسيط للوصول لـ TikTok
            response = self.session.get(f"{self.base_url}/", timeout=10)
            
            if response.status_code == 200:
                self.logger.info("تم اختبار الاتصال بنجاح")
                return True
            else:
                self.logger.error(f"فشل اختبار الاتصال: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {str(e)}")
            return False
    
    def fetch_content(self, content_types: List[str] = None, 
                     limit: int = 10) -> List[ContentItem]:
        """جلب المحتوى من TikTok"""
        try:
            content_types = content_types or ["lives", "posts"]
            all_content = []
            
            if "lives" in content_types:
                live_streams = self._fetch_live_streams(limit // 2)
                all_content.extend(live_streams)
            
            if "posts" in content_types:
                posts = self._fetch_recent_posts(limit // 2)
                all_content.extend(posts)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            all_content.sort(key=lambda x: x.timestamp, reverse=True)
            
            self.stats["total_fetched"] += len(all_content)
            return all_content[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المحتوى: {str(e)}")
            return []
    
    def _fetch_live_streams(self, limit: int) -> List[ContentItem]:
        """جلب البث المباشر من المستخدمين المراقبين"""
        live_streams = []
        
        try:
            for username in self.monitored_users:
                user_lives = self._fetch_user_live_streams(username)
                live_streams.extend(user_lives)
                
                # تأخير بسيط لتجنب Rate Limiting
                time.sleep(2)
                
                if len(live_streams) >= limit:
                    break
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب البث المباشر: {str(e)}")
            return []
    
    def _fetch_user_live_streams(self, username: str) -> List[ContentItem]:
        """جلب البث المباشر لمستخدم معين"""
        try:
            # محاولة الوصول لصفحة المستخدم
            user_url = f"{self.base_url}/@{username}"
            response = self.session.get(user_url, timeout=15)
            
            if response.status_code != 200:
                self.logger.warning(f"لا يمكن الوصول لمستخدم: {username}")
                return []
            
            # البحث عن معلومات البث المباشر في HTML
            live_streams = self._parse_live_streams_from_html(response.text, username)
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب البث المباشر للمستخدم {username}: {str(e)}")
            return []
    
    def _parse_live_streams_from_html(self, html_content: str, username: str) -> List[ContentItem]:
        """استخراج معلومات البث المباشر من HTML"""
        live_streams = []
        
        try:
            # البحث عن JSON data في HTML
            json_pattern = r'<script id="__UNIVERSAL_DATA_FOR_REHYDRATION__"[^>]*>(.*?)</script>'
            match = re.search(json_pattern, html_content, re.DOTALL)
            
            if match:
                try:
                    data = json.loads(match.group(1))
                    live_streams = self._extract_live_streams_from_data(data, username)
                except json.JSONDecodeError:
                    self.logger.warning(f"لا يمكن تحليل JSON للمستخدم: {username}")
            
            # إذا لم نجد بث مباشر، ابحث عن مؤشرات أخرى
            if not live_streams:
                live_streams = self._check_for_live_indicators(html_content, username)
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل HTML للبث المباشر: {str(e)}")
            return []
    
    def _extract_live_streams_from_data(self, data: Dict, username: str) -> List[ContentItem]:
        """استخراج البث المباشر من بيانات JSON"""
        live_streams = []
        
        try:
            # البحث في هيكل البيانات عن البث المباشر
            if "LiveRoom" in data:
                live_data = data["LiveRoom"]
            elif "liveRoom" in data:
                live_data = data["liveRoom"]
            else:
                # البحث في مستويات أعمق
                live_data = self._deep_search_for_live_data(data)
            
            if live_data and isinstance(live_data, dict):
                live_item = self._create_live_stream_item(live_data, username)
                if live_item:
                    live_streams.append(live_item)
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج البث المباشر من البيانات: {str(e)}")
            return []
    
    def _deep_search_for_live_data(self, data: Any, depth: int = 0) -> Optional[Dict]:
        """البحث العميق عن بيانات البث المباشر"""
        if depth > 5:  # تجنب البحث العميق جداً
            return None
        
        if isinstance(data, dict):
            # البحث عن مفاتيح البث المباشر
            live_keys = ["live", "liveRoom", "LiveRoom", "stream", "broadcast"]
            for key in live_keys:
                if key in data and isinstance(data[key], dict):
                    return data[key]
            
            # البحث في القيم
            for value in data.values():
                result = self._deep_search_for_live_data(value, depth + 1)
                if result:
                    return result
        
        elif isinstance(data, list):
            for item in data:
                result = self._deep_search_for_live_data(item, depth + 1)
                if result:
                    return result
        
        return None
    
    def _check_for_live_indicators(self, html_content: str, username: str) -> List[ContentItem]:
        """البحث عن مؤشرات البث المباشر في HTML"""
        live_streams = []
        
        try:
            # البحث عن كلمات مفتاحية للبث المباشر
            live_indicators = [
                r'LIVE',
                r'live-stream',
                r'is-live',
                r'live-room',
                r'broadcasting'
            ]
            
            is_live = False
            for indicator in live_indicators:
                if re.search(indicator, html_content, re.IGNORECASE):
                    is_live = True
                    break
            
            if is_live:
                # إنشاء عنصر بث مباشر افتراضي
                live_item = ContentItem(
                    platform="tiktok",
                    content_type="live",
                    url=f"{self.base_url}/@{username}/live",
                    title=f"بث مباشر من {username}",
                    author=username,
                    timestamp=datetime.now(),
                    metadata={
                        "format": "live_stream",
                        "source": "html_indicators",
                        "status": "active"
                    }
                )
                live_streams.append(live_item)
                
                # تتبع البث المباشر النشط
                self.active_live_streams[username] = {
                    "start_time": datetime.now(),
                    "content_id": live_item.id
                }
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن مؤشرات البث المباشر: {str(e)}")
            return []
    
    def _create_live_stream_item(self, live_data: Dict, username: str) -> Optional[ContentItem]:
        """إنشاء عنصر بث مباشر من البيانات"""
        try:
            # استخراج URL البث
            stream_url = None
            if "stream_url" in live_data:
                stream_url = live_data["stream_url"]
            elif "playUrl" in live_data:
                stream_url = live_data["playUrl"]
            elif "url" in live_data:
                stream_url = live_data["url"]
            else:
                stream_url = f"{self.base_url}/@{username}/live"
            
            # استخراج معلومات إضافية
            title = live_data.get("title", f"بث مباشر من {username}")
            description = live_data.get("description", "")
            viewer_count = live_data.get("viewer_count", 0)
            
            return ContentItem(
                platform="tiktok",
                content_type="live",
                url=stream_url,
                title=title,
                description=description,
                author=username,
                timestamp=datetime.now(),
                metadata={
                    "format": "live_stream",
                    "source": "api_extraction",
                    "viewer_count": viewer_count,
                    "original_data": live_data
                }
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء عنصر بث مباشر: {str(e)}")
            return None
    
    def _fetch_recent_posts(self, limit: int) -> List[ContentItem]:
        """جلب المنشورات الحديثة من المستخدمين المراقبين"""
        posts = []
        
        try:
            for username in self.monitored_users:
                user_posts = self._fetch_user_posts(username, limit // len(self.monitored_users) + 1)
                posts.extend(user_posts)
                
                time.sleep(1)
                
                if len(posts) >= limit:
                    break
            
            return posts
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المنشورات: {str(e)}")
            return []
    
    def _fetch_user_posts(self, username: str, limit: int) -> List[ContentItem]:
        """جلب منشورات مستخدم معين"""
        try:
            user_url = f"{self.base_url}/@{username}"
            response = self.session.get(user_url, timeout=15)
            
            if response.status_code != 200:
                return []
            
            # استخراج منشورات من HTML
            posts = self._parse_posts_from_html(response.text, username)
            
            return posts[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب منشورات المستخدم {username}: {str(e)}")
            return []
    
    def _parse_posts_from_html(self, html_content: str, username: str) -> List[ContentItem]:
        """استخراج المنشورات من HTML"""
        posts = []
        
        try:
            # البحث عن روابط الفيديوهات
            video_pattern = r'https://www\.tiktok\.com/@[^/]+/video/(\d+)'
            video_matches = re.findall(video_pattern, html_content)
            
            for i, video_id in enumerate(video_matches[:5]):  # أول 5 فيديوهات
                video_url = f"https://www.tiktok.com/@{username}/video/{video_id}"
                
                post_item = ContentItem(
                    platform="tiktok",
                    content_type="post",
                    url=video_url,
                    title=f"فيديو من {username}",
                    author=username,
                    timestamp=datetime.now() - timedelta(hours=i),
                    metadata={
                        "format": "video",
                        "source": "html_extraction",
                        "video_id": video_id
                    }
                )
                
                if post_item.id not in self.known_content_ids:
                    posts.append(post_item)
                    self.known_content_ids.add(post_item.id)
            
            return posts
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج المنشورات: {str(e)}")
            return []
    
    def get_active_live_streams(self) -> Dict[str, Dict]:
        """الحصول على البث المباشر النشط"""
        return self.active_live_streams.copy()
    
    def stop_monitoring_live_stream(self, username: str):
        """إيقاف مراقبة بث مباشر معين"""
        if username in self.active_live_streams:
            del self.active_live_streams[username]
            self.logger.info(f"تم إيقاف مراقبة البث المباشر: {username}")
    
    def get_monitored_users(self) -> List[str]:
        """الحصول على قائمة المستخدمين المراقبين"""
        return self.monitored_users.copy()
    
    def get_platform_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات خاصة بـ TikTok"""
        base_stats = self.get_stats()
        base_stats.update({
            "monitored_users_count": len(self.monitored_users),
            "monitored_users": self.monitored_users,
            "active_live_streams": len(self.active_live_streams),
            "known_content": len(self.known_content_ids)
        })
        return base_stats
