# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# إعدادات التطبيق المحسّنة
APP_NAME = "Smart Content Creator"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "منشئ المحتوى الذكي - Enhanced Edition"

# البيانات المطلوبة
datas = [
    # إضافة ملفات التكوين إذا وجدت
    # ('config/', 'config/'),
    # ('assets/', 'assets/'),
]

# المكتبات المخفية - شاملة لجميع المكتبات المثبتة
hiddenimports = [
    # PyQt6 الكاملة
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    'PyQt6.QtMultimedia',
    'PyQt6.QtWebEngineWidgets',

    # qtawesome للأيقونات
    'qtawesome',
    'qtawesome.iconic_font',

    # مكتبات تحليل البيانات
    'pandas',
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_umath',
    'numpy.core._multiarray_tests',
    'numpy.linalg.lapack_lite',
    'numpy.linalg._umath_linalg',

    # مكتبات الرسوم البيانية
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_tkagg',
    'seaborn',
    'plotly',
    'plotly.graph_objects',
    'plotly.express',

    # مكتبات معالجة الصوت والفيديو
    'librosa',
    'librosa.core',
    'librosa.feature',
    'soundfile',
    'numba',
    'numba.core',
    'numba.typed',
    'scipy',
    'scipy.sparse',
    'scipy.sparse.csgraph',
    'scipy.spatial.distance',
    'scikit-learn',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.tree',

    # مكتبات الشبكة والتواصل
    'requests',
    'aiohttp',
    'aiohttp.web',
    'aiohttp.client',
    'urllib3',
    'certifi',

    # مكتبات وسائل التواصل الاجتماعي
    'selenium',
    'selenium.webdriver',
    'playwright',
    'playwright.sync_api',
    'instaloader',
    'tweepy',

    # مكتبات قواعد البيانات
    'sqlalchemy',
    'sqlalchemy.engine',
    'sqlalchemy.orm',
    'pymongo',
    'redis',

    # مكتبات الأمان والتشفير
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'bcrypt',

    # مكتبات معالجة الصور
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'cv2',

    # مكتبات أدوات مساعدة
    'rich',
    'rich.console',
    'rich.table',
    'typer',
    'click',
    'tqdm',
    'schedule',
    'psutil',

    # وحدات النظام
    'logging',
    'datetime',
    'pathlib',
    'json',
    'sqlite3',

    # وحدات التطبيق
    'src.gui.main_window',
    'src.core.config_manager',
]

# تحليل التطبيق
a = Analysis(
    ['main_fixed.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات غير المطلوبة لتقليل الحجم
        'tkinter',
        'tkinter.ttk',
        'turtle',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pydoc',
        'distutils',

        # مكتبات التطوير
        'pytest',
        'coverage',
        'mypy',
        'black',
        'flake8',

        # مكتبات اختيارية ثقيلة (يمكن إزالة هذا القسم إذا كنت تريد تضمينها)
        # 'torch',
        # 'tensorflow',
        # 'transformers',

        # مكتبات نادرة الاستخدام
        'jupyter',
        'notebook',
        'ipython',
        'spyder',

        # مكتبات النظام غير المطلوبة
        'curses',
        'readline',
        'rlcompleter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المطلوبة
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء الملف التنفيذي المحسّن
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=f"{APP_NAME.replace(' ', '_')}_v{APP_VERSION}",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # ضغط UPX لتقليل الحجم
    upx_exclude=[
        # استبعاد ملفات معينة من الضغط لتجنب المشاكل
        'vcruntime140.dll',
        'python3.dll',
        'python313.dll',
    ],
    runtime_tmpdir=None,
    console=False,  # تطبيق GUI بدون نافذة console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة أيقونة هنا لاحقاً
    version_file=None,  # يمكن إضافة ملف معلومات الإصدار
)
