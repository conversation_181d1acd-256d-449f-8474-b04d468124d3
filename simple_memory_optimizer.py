#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن الذاكرة المبسط
Simple Memory Optimizer
"""

import os
import gc
import sys
import time
import json
import tracemalloc
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

class SimpleMemoryOptimizer:
    """محسن الذاكرة المبسط"""
    
    def __init__(self):
        self.start_time = time.time()
        
        # إعداد المجلدات
        self.config_dir = Path("config/memory_optimization")
        self.reports_dir = Path("reports/memory_reports")
        
        # إنشاء المجلدات
        for directory in [self.config_dir, self.reports_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # بدء تتبع الذاكرة
        if not tracemalloc.is_tracing():
            tracemalloc.start()
        
        print("🧠 تم تهيئة محسن الذاكرة المبسط")
    
    def analyze_memory_usage(self) -> Dict[str, Any]:
        """تحليل استخدام الذاكرة الحالي"""
        print("\n🔍 تحليل استخدام الذاكرة...")
        
        try:
            # معلومات الذاكرة الحالية
            current, peak = tracemalloc.get_traced_memory()
            
            # تحليل الكائنات في الذاكرة
            objects = gc.get_objects()
            object_types = {}
            large_objects = []
            
            for obj in objects:
                obj_type = type(obj).__name__
                object_types[obj_type] = object_types.get(obj_type, 0) + 1
                
                # البحث عن الكائنات الكبيرة
                try:
                    obj_size = sys.getsizeof(obj)
                    if obj_size > 1024 * 1024:  # أكبر من 1MB
                        large_objects.append({
                            "type": obj_type,
                            "size_mb": obj_size / 1024 / 1024,
                            "id": id(obj)
                        })
                except:
                    pass
            
            # ترتيب أنواع الكائنات حسب العدد
            sorted_types = dict(sorted(object_types.items(), key=lambda x: x[1], reverse=True)[:15])
            
            analysis = {
                "timestamp": datetime.now().isoformat(),
                "memory_usage": {
                    "traced_current_mb": current / 1024 / 1024,
                    "traced_peak_mb": peak / 1024 / 1024
                },
                "object_analysis": {
                    "total_objects": len(objects),
                    "object_types": sorted_types,
                    "large_objects": sorted(large_objects, key=lambda x: x["size_mb"], reverse=True)[:10]
                },
                "gc_stats": {
                    "collections": gc.get_stats(),
                    "threshold": gc.get_threshold(),
                    "counts": gc.get_count()
                }
            }
            
            print(f"   📊 الذاكرة المتتبعة: {current / 1024 / 1024:.2f} MB")
            print(f"   📈 ذروة الاستخدام: {peak / 1024 / 1024:.2f} MB")
            print(f"   🔢 عدد الكائنات: {len(objects)}")
            print(f"   📦 أنواع الكائنات: {len(object_types)}")
            
            return analysis
            
        except Exception as e:
            error_msg = f"خطأ في تحليل الذاكرة: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}
    
    def detect_memory_leaks(self) -> List[Dict[str, Any]]:
        """كشف تسريبات الذاكرة المحتملة"""
        print("\n🔍 كشف تسريبات الذاكرة...")
        
        leaks = []
        
        try:
            # الحصول على إحصائيات الذاكرة
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            # تحليل أكبر مستهلكي الذاكرة
            for stat in top_stats[:10]:
                if stat.size > 10 * 1024 * 1024:  # أكبر من 10MB
                    leak = {
                        "location": f"{stat.traceback.format()[-1] if stat.traceback else 'Unknown'}",
                        "size_mb": stat.size / 1024 / 1024,
                        "object_count": stat.count,
                        "leak_type": "high_memory_usage",
                        "severity": "high" if stat.size > 50 * 1024 * 1024 else "medium"
                    }
                    leaks.append(leak)
            
            # كشف الكائنات المتراكمة
            objects = gc.get_objects()
            object_counts = {}
            
            for obj in objects:
                obj_type = type(obj).__name__
                object_counts[obj_type] = object_counts.get(obj_type, 0) + 1
            
            for obj_type, count in object_counts.items():
                if count > 1000:  # أكثر من 1000 كائن من نفس النوع
                    leak = {
                        "location": f"Object accumulation: {obj_type}",
                        "size_mb": 0,
                        "object_count": count,
                        "leak_type": "object_accumulation",
                        "severity": "medium"
                    }
                    leaks.append(leak)
            
            print(f"   ⚠️ تم العثور على {len(leaks)} تسريب محتمل")
            
        except Exception as e:
            print(f"   ❌ خطأ في كشف التسريبات: {e}")
        
        return leaks
    
    def optimize_memory(self) -> Dict[str, Any]:
        """تحسين استخدام الذاكرة"""
        print("\n🧠 تحسين استخدام الذاكرة...")
        
        try:
            # قياس الذاكرة قبل التحسين
            before_current, before_peak = tracemalloc.get_traced_memory()
            
            optimizations = []
            
            # 1. تنظيف الذاكرة متعدد المراحل
            total_collected = 0
            for generation in range(3):
                collected = gc.collect(generation)
                total_collected += collected
                optimizations.append(f"gc_generation_{generation}_collected_{collected}")
            
            # 2. تحسين إعدادات garbage collector
            old_threshold = gc.get_threshold()
            gc.set_threshold(700, 10, 10)
            optimizations.append(f"gc_threshold_optimized_from_{old_threshold}")
            
            # 3. تنظيف الكائنات الضعيفة المراجع
            import weakref
            weak_refs_cleaned = 0
            for obj in gc.get_objects():
                if isinstance(obj, weakref.ref) and obj() is None:
                    weak_refs_cleaned += 1
            optimizations.append(f"weak_refs_cleaned_{weak_refs_cleaned}")
            
            # 4. تنظيف الذاكرة المؤقتة
            cache_cleaned = 0
            try:
                import re
                if hasattr(re, '_cache'):
                    re._cache.clear()
                    cache_cleaned += 1
            except:
                pass
            optimizations.append(f"caches_cleaned_{cache_cleaned}")
            
            # 5. تنظيف الكائنات الفارغة
            empty_objects_cleaned = self._cleanup_empty_objects()
            optimizations.append(f"empty_objects_cleaned_{empty_objects_cleaned}")
            
            # قياس الذاكرة بعد التحسين
            after_current, after_peak = tracemalloc.get_traced_memory()
            
            # حساب التحسينات
            memory_saved = (before_current - after_current) / 1024 / 1024
            improvement_percent = (memory_saved / (before_current / 1024 / 1024)) * 100 if before_current > 0 else 0
            
            results = {
                "status": "completed",
                "optimizations": optimizations,
                "memory_before_mb": before_current / 1024 / 1024,
                "memory_after_mb": after_current / 1024 / 1024,
                "memory_saved_mb": memory_saved,
                "objects_collected": total_collected,
                "improvement_percent": improvement_percent
            }
            
            print(f"   💾 ذاكرة محررة: {memory_saved:.2f} MB")
            print(f"   🗑️ كائنات محررة: {total_collected}")
            print(f"   📈 تحسين الأداء: {improvement_percent:.1f}%")
            
            return results
            
        except Exception as e:
            error_msg = f"خطأ في تحسين الذاكرة: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}
    
    def _cleanup_empty_objects(self) -> int:
        """تنظيف الكائنات الفارغة"""
        try:
            cleaned = 0
            
            # تنظيف القوائم الفارغة
            empty_lists = [obj for obj in gc.get_objects() if isinstance(obj, list) and len(obj) == 0]
            cleaned += len(empty_lists)
            
            # تنظيف القواميس الفارغة
            empty_dicts = [obj for obj in gc.get_objects() if isinstance(obj, dict) and len(obj) == 0]
            cleaned += len(empty_dicts)
            
            return cleaned
        except Exception:
            return 0
    
    def analyze_resource_usage(self) -> Dict[str, Any]:
        """تحليل استخدام الموارد الأساسي"""
        print("\n🔧 تحليل استخدام الموارد...")
        
        try:
            # تحليل الخيوط
            active_threads = threading.active_count()
            main_thread_alive = threading.main_thread().is_alive()
            daemon_threads = len([t for t in threading.enumerate() if t.daemon])
            
            # تحليل الملفات المفتوحة (تقدير بسيط)
            open_files_estimate = len([obj for obj in gc.get_objects() if hasattr(obj, 'read') and hasattr(obj, 'close')])
            
            analysis = {
                "timestamp": datetime.now().isoformat(),
                "threads": {
                    "active_threads": active_threads,
                    "main_thread_alive": main_thread_alive,
                    "daemon_threads": daemon_threads
                },
                "files": {
                    "estimated_open_files": open_files_estimate
                },
                "system": {
                    "python_version": sys.version,
                    "platform": sys.platform,
                    "process_id": os.getpid()
                }
            }
            
            print(f"   🧵 خيوط نشطة: {active_threads}")
            print(f"   👻 خيوط daemon: {daemon_threads}")
            print(f"   📁 ملفات مفتوحة (تقدير): {open_files_estimate}")
            
            # كشف تسريبات محتملة
            potential_leaks = []
            if active_threads > 20:
                potential_leaks.append(f"threads_leak_{active_threads}")
            if open_files_estimate > 50:
                potential_leaks.append(f"file_handles_leak_{open_files_estimate}")
            
            analysis["potential_leaks"] = potential_leaks
            
            if potential_leaks:
                print(f"   ⚠️ تسريبات محتملة: {len(potential_leaks)}")
            else:
                print(f"   ✅ لم يتم العثور على تسريبات واضحة")
            
            return analysis
            
        except Exception as e:
            error_msg = f"خطأ في تحليل الموارد: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}

    def create_optimization_configs(self) -> Dict[str, Any]:
        """إنشاء ملفات إعدادات التحسين"""
        print("\n⚙️ إنشاء ملفات إعدادات التحسين...")

        try:
            # إعدادات تحسين الذاكرة
            memory_config = {
                "memory_optimization": {
                    "gc_enabled": True,
                    "gc_threshold": [700, 10, 10],
                    "auto_cleanup_enabled": True,
                    "cleanup_interval_seconds": 300,
                    "memory_threshold_mb": 512,
                    "weak_ref_cleanup_enabled": True,
                    "cache_cleanup_enabled": True
                },
                "monitoring": {
                    "memory_monitoring_enabled": True,
                    "check_interval_seconds": 60,
                    "alert_threshold_mb": 1024,
                    "log_memory_usage": True
                }
            }

            # إعدادات المراقبة
            monitoring_config = {
                "auto_monitoring": {
                    "enabled": True,
                    "check_interval_seconds": 60,
                    "memory_threshold_mb": 512
                },
                "alerts": {
                    "memory_alert_enabled": True,
                    "resource_leak_alert_enabled": True
                },
                "cleanup": {
                    "auto_cleanup_enabled": True,
                    "cleanup_interval_minutes": 30,
                    "memory_cleanup_threshold_mb": 256
                }
            }

            # حفظ ملفات الإعدادات
            configs = [
                ("memory_optimization_config.json", memory_config),
                ("monitoring_config.json", monitoring_config)
            ]

            created_files = []
            for filename, config in configs:
                config_file = self.config_dir / filename
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                created_files.append(str(config_file))
                print(f"   ✅ تم إنشاء: {config_file}")

            return {
                "status": "completed",
                "created_files": created_files,
                "config_directory": str(self.config_dir)
            }

        except Exception as e:
            error_msg = f"خطأ في إنشاء ملفات الإعدادات: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}

    def generate_report(self) -> Dict[str, Any]:
        """إنشاء تقرير شامل"""
        print("\n📋 إنشاء تقرير شامل...")

        try:
            # تحليل شامل
            memory_analysis = self.analyze_memory_usage()
            resource_analysis = self.analyze_resource_usage()
            memory_leaks = self.detect_memory_leaks()

            # التقرير الشامل
            report = {
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "report_version": "1.0",
                    "optimizer_version": "Simple 1.0"
                },
                "memory_analysis": memory_analysis,
                "resource_analysis": resource_analysis,
                "detected_leaks": {
                    "memory_leaks": memory_leaks,
                    "total_leaks": len(memory_leaks)
                },
                "performance_metrics": {
                    "memory_efficiency_score": self._calculate_memory_score(memory_analysis),
                    "resource_efficiency_score": self._calculate_resource_score(resource_analysis),
                    "overall_health_score": self._calculate_health_score(memory_leaks)
                }
            }

            # حفظ التقرير
            report_file = self.reports_dir / f"memory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            print(f"   📄 تم حفظ التقرير: {report_file}")

            return report

        except Exception as e:
            error_msg = f"خطأ في إنشاء التقرير: {str(e)}"
            print(f"   ❌ {error_msg}")
            return {"error": error_msg}

    def _calculate_memory_score(self, memory_analysis) -> float:
        """حساب نقاط كفاءة الذاكرة"""
        try:
            current_mb = memory_analysis.get("memory_usage", {}).get("traced_current_mb", 0)
            if current_mb < 100:
                return 100.0
            elif current_mb < 500:
                return 80.0
            elif current_mb < 1000:
                return 60.0
            else:
                return 40.0
        except:
            return 50.0

    def _calculate_resource_score(self, resource_analysis) -> float:
        """حساب نقاط كفاءة الموارد"""
        try:
            threads = resource_analysis.get("threads", {}).get("active_threads", 0)
            files = resource_analysis.get("files", {}).get("estimated_open_files", 0)

            score = 100.0
            if threads > 20:
                score -= 20
            if files > 50:
                score -= 20

            return max(score, 0.0)
        except:
            return 50.0

    def _calculate_health_score(self, memory_leaks) -> float:
        """حساب النقاط الإجمالية لصحة النظام"""
        try:
            score = 100.0
            score -= len(memory_leaks) * 10
            return max(score, 0.0)
        except:
            return 50.0

def main():
    """الوظيفة الرئيسية"""
    print("🚀 محسن الذاكرة والموارد المبسط")
    print("=" * 50)

    try:
        # إنشاء المحسن
        optimizer = SimpleMemoryOptimizer()

        # المرحلة 1: تحليل الوضع الحالي
        print("\n📊 المرحلة 1: تحليل الوضع الحالي")
        memory_analysis = optimizer.analyze_memory_usage()
        resource_analysis = optimizer.analyze_resource_usage()

        # المرحلة 2: كشف التسريبات
        print("\n🔍 المرحلة 2: كشف التسريبات")
        memory_leaks = optimizer.detect_memory_leaks()

        # المرحلة 3: تطبيق التحسينات
        print("\n⚡ المرحلة 3: تطبيق التحسينات")
        optimization_results = optimizer.optimize_memory()

        # المرحلة 4: إنشاء ملفات الإعدادات
        print("\n⚙️ المرحلة 4: إنشاء ملفات الإعدادات")
        config_results = optimizer.create_optimization_configs()

        # المرحلة 5: إنشاء التقرير الشامل
        print("\n📋 المرحلة 5: إنشاء التقرير الشامل")
        report = optimizer.generate_report()

        # عرض النتائج النهائية
        print("\n" + "=" * 50)
        print("🎉 تم إكمال تحسين الذاكرة والموارد بنجاح!")
        print("=" * 50)

        # ملخص النتائج
        if optimization_results.get("memory_saved_mb", 0) > 0:
            print(f"💾 ذاكرة محررة: {optimization_results['memory_saved_mb']:.2f} MB")

        if optimization_results.get("objects_collected", 0) > 0:
            print(f"🗑️ كائنات محررة: {optimization_results['objects_collected']}")

        if optimization_results.get("improvement_percent", 0) > 0:
            print(f"📈 تحسين الأداء: {optimization_results['improvement_percent']:.1f}%")

        print(f"⚠️ تسريبات ذاكرة مكتشفة: {len(memory_leaks)}")

        if report.get("performance_metrics"):
            metrics = report["performance_metrics"]
            print(f"🏆 نقاط كفاءة الذاكرة: {metrics.get('memory_efficiency_score', 0):.1f}/100")
            print(f"🏆 نقاط كفاءة الموارد: {metrics.get('resource_efficiency_score', 0):.1f}/100")
            print(f"🏆 النقاط الإجمالية: {metrics.get('overall_health_score', 0):.1f}/100")

        print("\n📁 الملفات المُنشأة:")
        if config_results.get("created_files"):
            for file_path in config_results["created_files"]:
                print(f"   ⚙️ {file_path}")

        print("\n✅ تم حفظ التقرير الشامل في مجلد reports/memory_reports/")

        return True

    except Exception as e:
        print(f"\n❌ خطأ في تحسين الذاكرة والموارد: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
