# -*- mode: python ; coding: utf-8 -*-
"""
ملف مواصفات PyInstaller لتجميع التطبيق
PyInstaller Spec File for Application Building
"""

import sys
import os
from pathlib import Path

# معلومات التطبيق
APP_NAME = "Smart Content Creator"
APP_VERSION = "1.0.0"
APP_ICON = "assets/icon.ico" if Path("assets/icon.ico").exists() else None

# مسار المشروع
project_root = Path.cwd()
src_path = project_root / "src"

# إضافة مسارات المشروع
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# البيانات والملفات المطلوبة
def collect_data_files():
    """جمع جميع ملفات البيانات المطلوبة"""
    datas = []
    
    # ملفات الإعدادات
    config_dir = project_root / "config"
    if config_dir.exists():
        for config_file in config_dir.rglob("*.json"):
            relative_path = config_file.relative_to(project_root)
            datas.append((str(config_file), str(relative_path.parent)))
    
    # ملفات الموارد
    resources_dir = project_root / "resources"
    if resources_dir.exists():
        for resource_file in resources_dir.rglob("*"):
            if resource_file.is_file():
                relative_path = resource_file.relative_to(project_root)
                datas.append((str(resource_file), str(relative_path.parent)))
    
    # ملفات الأصول (الأيقونات والصور)
    assets_dir = project_root / "assets"
    if assets_dir.exists():
        for asset_file in assets_dir.rglob("*"):
            if asset_file.is_file():
                relative_path = asset_file.relative_to(project_root)
                datas.append((str(asset_file), str(relative_path.parent)))
    
    # ملفات القوالب
    templates_dir = project_root / "templates"
    if templates_dir.exists():
        for template_file in templates_dir.rglob("*"):
            if template_file.is_file():
                relative_path = template_file.relative_to(project_root)
                datas.append((str(template_file), str(relative_path.parent)))
    
    # ملفات التوثيق المهمة
    docs = ["README.md", "LICENSE", "requirements.txt"]
    for doc in docs:
        doc_path = project_root / doc
        if doc_path.exists():
            datas.append((str(doc_path), "."))
    
    return datas

# المكتبات المخفية المطلوبة
hiddenimports = [
    # PyQt6
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    'PyQt6.QtMultimedia',
    
    # مكتبات الذكاء الاصطناعي
    'whisper',
    'openai',
    'librosa',
    'soundfile',
    
    # مكتبات معالجة الفيديو
    'moviepy',
    'opencv-python',
    'ffmpeg-python',
    
    # مكتبات الشبكة والويب
    'requests',
    'aiohttp',
    'selenium',
    'beautifulsoup4',
    'yt-dlp',
    
    # مكتبات الأمان
    'cryptography',
    'keyring',
    'pyotp',
    'qrcode',
    
    # مكتبات النظام
    'psutil',
    'schedule',
    'plyer',
    'tqdm',
    
    # مكتبات البيانات
    'pandas',
    'numpy',
    'scipy',
    'matplotlib',
    'seaborn',
    
    # وحدات التطبيق
    'src.gui.main_window',
    'src.content.content_manager',
    'src.ai.content_analyzer',
    'src.editing.video_editor',
    'src.publishing.tiktok_publisher',
    'src.core.config_manager',
    'src.core.security',
    'src.core.logger',
]

# الملفات الثنائية المطلوبة
binaries = []

# إضافة FFmpeg إذا كان متوفراً
ffmpeg_path = None
possible_ffmpeg_paths = [
    "C:/ffmpeg/bin/ffmpeg.exe",
    "C:/Program Files/ffmpeg/bin/ffmpeg.exe",
    "/usr/bin/ffmpeg",
    "/usr/local/bin/ffmpeg"
]

for path in possible_ffmpeg_paths:
    if Path(path).exists():
        ffmpeg_path = path
        break

if ffmpeg_path:
    binaries.append((ffmpeg_path, "ffmpeg"))

# تحليل التطبيق الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root), str(src_path)],
    binaries=binaries,
    datas=collect_data_files(),
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',  # استبعاد tkinter لأننا نستخدم PyQt6
        'matplotlib.backends._backend_tk',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المرغوب فيها
def remove_unwanted_files(a):
    """إزالة الملفات غير المرغوب فيها لتقليل حجم التطبيق"""
    unwanted_modules = [
        'test',
        'tests',
        'testing',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
        'tkinter',
    ]
    
    a.binaries = [x for x in a.binaries if not any(unwanted in x[0].lower() for unwanted in unwanted_modules)]
    a.datas = [x for x in a.datas if not any(unwanted in x[0].lower() for unwanted in unwanted_modules)]
    
    return a

a = remove_unwanted_files(a)

# إنشاء ملف PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إنشاء التطبيق القابل للتنفيذ
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=APP_NAME.replace(" ", "_"),
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # ضغط UPX لتقليل الحجم
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # تطبيق GUI بدون console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=APP_ICON,
)

# إنشاء مجلد التوزيع (اختياري)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=APP_NAME.replace(" ", "_") + "_dist"
)
