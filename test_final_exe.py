#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للملف التنفيذي النهائي
"""

import os
import subprocess
import sys
import time
from pathlib import Path

def test_exe_file():
    """اختبار الملف التنفيذي"""
    
    print("🔍 اختبار الملف التنفيذي النهائي")
    print("=" * 50)
    
    # البحث عن الملف التنفيذي
    exe_path = Path("dist/Smart_Content_Creator.exe")
    
    if not exe_path.exists():
        print("❌ الملف التنفيذي غير موجود!")
        return False
    
    # معلومات الملف
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"📄 اسم الملف: {exe_path.name}")
    print(f"📏 حجم الملف: {file_size:.1f} MB")
    print(f"📍 المسار: {exe_path.absolute()}")
    
    # اختبار تشغيل الملف
    print(f"\n🧪 اختبار تشغيل التطبيق...")
    
    try:
        # تشغيل الملف مع timeout قصير للتحقق من بدء التشغيل
        process = subprocess.Popen(
            [str(exe_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=exe_path.parent
        )
        
        # انتظار قصير للتحقق من بدء التشغيل
        time.sleep(3)
        
        # التحقق من حالة العملية
        poll_result = process.poll()
        
        if poll_result is None:
            # العملية ما زالت تعمل - هذا جيد!
            print("✅ التطبيق بدأ بنجاح ويعمل!")
            
            # إنهاء العملية بلطف
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ تم إغلاق التطبيق بنجاح")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ تم إجبار إغلاق التطبيق")
            
            return True
            
        elif poll_result == 0:
            print("✅ التطبيق انتهى بنجاح")
            return True
            
        else:
            print(f"❌ التطبيق انتهى برمز خطأ: {poll_result}")
            
            # قراءة رسائل الخطأ
            stdout, stderr = process.communicate()
            if stderr:
                print(f"❌ رسائل الخطأ:")
                print(stderr)
            if stdout:
                print(f"📤 الإخراج:")
                print(stdout)
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def check_warnings():
    """فحص ملف التحذيرات"""
    print(f"\n📋 فحص ملف التحذيرات...")
    
    warn_file = Path("build/smart_content_creator_simple/warn-smart_content_creator_simple.txt")
    
    if warn_file.exists():
        try:
            with open(warn_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"⚠️ عدد التحذيرات: {len(lines)}")
            
            # عرض آخر 5 تحذيرات
            if lines:
                print("📄 آخر التحذيرات:")
                for line in lines[-5:]:
                    if line.strip():
                        print(f"  {line.strip()}")
            else:
                print("✅ لا توجد تحذيرات")
                
        except Exception as e:
            print(f"❌ خطأ في قراءة ملف التحذيرات: {e}")
    else:
        print("✅ لا يوجد ملف تحذيرات")

def create_summary():
    """إنشاء ملخص النتائج"""
    print(f"\n📊 ملخص النتائج:")
    print("=" * 50)
    
    # معلومات الملف التنفيذي
    exe_path = Path("dist/Smart_Content_Creator.exe")
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ تم إنشاء الملف التنفيذي بنجاح")
        print(f"📏 الحجم: {file_size:.1f} MB")
        print(f"📍 المسار: {exe_path.absolute()}")
    else:
        print(f"❌ فشل في إنشاء الملف التنفيذي")
        return
    
    # معلومات البناء
    print(f"\n🔧 معلومات البناء:")
    print(f"  • تم استخدام PyInstaller")
    print(f"  • ملف المصدر: main_fixed.py")
    print(f"  • ملف التكوين: smart_content_creator_simple.spec")
    print(f"  • نوع البناء: مبسط (بدون مكتبات اختيارية)")
    
    # الميزات المتاحة
    print(f"\n✨ الميزات المتاحة:")
    print(f"  ✅ واجهة PyQt6")
    print(f"  ✅ نافذة رئيسية تفاعلية")
    print(f"  ✅ دعم اللغة العربية")
    print(f"  ✅ تشغيل مستقل (لا يحتاج Python)")
    
    # التوصيات
    print(f"\n💡 التوصيات:")
    print(f"  • يمكن توزيع الملف التنفيذي مباشرة")
    print(f"  • لا يحتاج تثبيت Python أو مكتبات إضافية")
    print(f"  • يعمل على أنظمة Windows 64-bit")
    print(f"  • يمكن إضافة المزيد من الميزات تدريجياً")

def main():
    """الدالة الرئيسية"""
    print("🎯 اختبار شامل للتطبيق النهائي")
    print("Smart Content Creator - منشئ المحتوى الذكي")
    print("=" * 60)
    
    # اختبار الملف التنفيذي
    success = test_exe_file()
    
    # فحص التحذيرات
    check_warnings()
    
    # إنشاء الملخص
    create_summary()
    
    # النتيجة النهائية
    print(f"\n🏁 النتيجة النهائية:")
    if success:
        print("🎉 تم حل مشكلة الملف التنفيذي بنجاح!")
        print("✅ التطبيق جاهز للاستخدام")
    else:
        print("❌ ما زالت هناك مشاكل تحتاج حل")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
