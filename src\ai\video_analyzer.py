#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل الفيديو - Video Analyzer
يحلل الفيديو ويحدد اللقطات المهمة والمثيرة بصرياً
"""

import logging
import os
import json
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import tempfile

class VideoFrame:
    """إطار فيديو مع معلوماته"""
    
    def __init__(self, timestamp: float, frame_number: int):
        self.timestamp = timestamp
        self.frame_number = frame_number
        self.brightness = 0.0
        self.contrast = 0.0
        self.motion_score = 0.0
        self.face_count = 0
        self.object_count = 0
        self.color_variance = 0.0
        self.sharpness = 0.0
        self.is_interesting = False
        self.features = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp,
            "frame_number": self.frame_number,
            "brightness": self.brightness,
            "contrast": self.contrast,
            "motion_score": self.motion_score,
            "face_count": self.face_count,
            "object_count": self.object_count,
            "color_variance": self.color_variance,
            "sharpness": self.sharpness,
            "is_interesting": self.is_interesting,
            "features": self.features
        }

class VideoSegment:
    """مقطع فيديو مع تحليله"""
    
    def __init__(self, start_time: float, end_time: float):
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.frames = []
        self.avg_motion = 0.0
        self.avg_brightness = 0.0
        self.scene_changes = 0
        self.face_appearances = 0
        self.visual_interest_score = 0.0
        self.dominant_colors = []
        self.is_action_packed = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "frame_count": len(self.frames),
            "avg_motion": self.avg_motion,
            "avg_brightness": self.avg_brightness,
            "scene_changes": self.scene_changes,
            "face_appearances": self.face_appearances,
            "visual_interest_score": self.visual_interest_score,
            "dominant_colors": self.dominant_colors,
            "is_action_packed": self.is_action_packed
        }

class VideoAnalyzer:
    """محلل الفيديو الذكي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات التحليل
        self.settings = self._load_video_settings()
        
        # تحميل نماذج الكشف
        self.face_cascade = None
        self.body_cascade = None
        self._load_detection_models()
        
        # متغيرات التحليل
        self.prev_frame = None
        self.motion_threshold = self.settings.get("motion_threshold", 30.0)
        self.brightness_threshold = self.settings.get("brightness_threshold", 50.0)
    
    def _load_video_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات تحليل الفيديو"""
        try:
            return self.config_manager.get_setting("ai_settings", "video", {
                "sample_rate": 1.0,  # إطار كل ثانية
                "motion_threshold": 30.0,
                "brightness_threshold": 50.0,
                "contrast_threshold": 30.0,
                "face_detection": True,
                "object_detection": False,  # يتطلب نماذج إضافية
                "scene_change_threshold": 0.3,
                "segment_duration": 10.0,  # ثواني
                "min_segment_duration": 3.0,
                "max_segments": 20
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الفيديو: {str(e)}")
            return {
                "sample_rate": 1.0,
                "motion_threshold": 30.0,
                "brightness_threshold": 50.0,
                "contrast_threshold": 30.0,
                "face_detection": True,
                "object_detection": False,
                "scene_change_threshold": 0.3,
                "segment_duration": 10.0,
                "min_segment_duration": 3.0,
                "max_segments": 20
            }
    
    def _load_detection_models(self):
        """تحميل نماذج الكشف"""
        try:
            if self.settings.get("face_detection", True):
                # تحميل نموذج كشف الوجوه
                face_cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
                if os.path.exists(face_cascade_path):
                    self.face_cascade = cv2.CascadeClassifier(face_cascade_path)
                    self.logger.info("تم تحميل نموذج كشف الوجوه")
                else:
                    self.logger.warning("نموذج كشف الوجوه غير متاح")
            
            # يمكن إضافة نماذج أخرى هنا
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل نماذج الكشف: {str(e)}")
    
    def analyze_video(self, file_path: str) -> Dict[str, Any]:
        """تحليل الفيديو الرئيسي"""
        try:
            self.logger.info(f"بدء تحليل الفيديو: {file_path}")
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")
            
            # فتح الفيديو
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                raise ValueError("لا يمكن فتح ملف الفيديو")
            
            # الحصول على معلومات الفيديو
            video_info = self._get_video_info(cap)
            
            # تحليل الإطارات
            frames_analysis = self._analyze_frames(cap)
            
            # تقسيم إلى مقاطع
            segments = self._create_segments(frames_analysis, video_info)
            
            # تحديد اللحظات المهمة
            interesting_moments = self._find_visual_highlights(segments)
            
            # حساب التقييم الشامل
            quality_score = self._calculate_video_quality_score(
                video_info, segments, interesting_moments
            )
            
            # إغلاق الفيديو
            cap.release()
            
            result = {
                "file_path": file_path,
                "video_info": video_info,
                "frames_analyzed": len(frames_analysis),
                "segments": [seg.to_dict() for seg in segments],
                "interesting_moments": interesting_moments,
                "quality_score": quality_score,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info("انتهى تحليل الفيديو بنجاح")
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الفيديو: {str(e)}")
            return {
                "error": str(e),
                "file_path": file_path,
                "quality_score": 0.0,
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _get_video_info(self, cap: cv2.VideoCapture) -> Dict[str, Any]:
        """الحصول على معلومات الفيديو"""
        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            return {
                "fps": fps,
                "frame_count": frame_count,
                "width": width,
                "height": height,
                "duration": duration,
                "resolution": f"{width}x{height}",
                "aspect_ratio": width / height if height > 0 else 0
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
            return {}
    
    def _analyze_frames(self, cap: cv2.VideoCapture) -> List[VideoFrame]:
        """تحليل إطارات الفيديو"""
        frames_analysis = []
        frame_number = 0
        sample_rate = self.settings.get("sample_rate", 1.0)
        
        try:
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_interval = int(fps / sample_rate) if fps > 0 else 1
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحليل الإطار كل فترة محددة
                if frame_number % frame_interval == 0:
                    timestamp = frame_number / fps if fps > 0 else frame_number
                    frame_analysis = self._analyze_single_frame(frame, timestamp, frame_number)
                    frames_analysis.append(frame_analysis)
                
                frame_number += 1
                
                # تحديد عدد الإطارات المحللة لتجنب الإفراط
                if len(frames_analysis) >= 1000:  # حد أقصى
                    break
            
            return frames_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الإطارات: {str(e)}")
            return []
    
    def _analyze_single_frame(self, frame: np.ndarray, 
                             timestamp: float, frame_number: int) -> VideoFrame:
        """تحليل إطار واحد"""
        frame_analysis = VideoFrame(timestamp, frame_number)
        
        try:
            # تحويل إلى رمادي للتحليل
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # حساب السطوع
            frame_analysis.brightness = np.mean(gray)
            
            # حساب التباين
            frame_analysis.contrast = np.std(gray)
            
            # حساب الحدة (Sharpness)
            frame_analysis.sharpness = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # حساب تنوع الألوان
            frame_analysis.color_variance = np.var(frame.reshape(-1, 3), axis=0).mean()
            
            # كشف الوجوه
            if self.face_cascade is not None:
                faces = self.face_cascade.detectMultiScale(
                    gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
                )
                frame_analysis.face_count = len(faces)
            
            # حساب الحركة (مقارنة مع الإطار السابق)
            if self.prev_frame is not None:
                frame_analysis.motion_score = self._calculate_motion(gray, self.prev_frame)
            
            # تحديد ما إذا كان الإطار مثيراً للاهتمام
            frame_analysis.is_interesting = self._is_frame_interesting(frame_analysis)
            
            # حفظ الإطار الحالي للمقارنة التالية
            self.prev_frame = gray.copy()
            
            return frame_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الإطار: {str(e)}")
            return frame_analysis
    
    def _calculate_motion(self, current_frame: np.ndarray, 
                         prev_frame: np.ndarray) -> float:
        """حساب مقدار الحركة بين إطارين"""
        try:
            # حساب الفرق المطلق
            diff = cv2.absdiff(current_frame, prev_frame)
            
            # تطبيق عتبة
            _, thresh = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)
            
            # حساب نسبة البكسلات المتغيرة
            motion_pixels = np.sum(thresh > 0)
            total_pixels = thresh.shape[0] * thresh.shape[1]
            
            return (motion_pixels / total_pixels) * 100
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الحركة: {str(e)}")
            return 0.0
    
    def _is_frame_interesting(self, frame: VideoFrame) -> bool:
        """تحديد ما إذا كان الإطار مثيراً للاهتمام"""
        try:
            interest_score = 0
            
            # حركة عالية
            if frame.motion_score > self.motion_threshold:
                interest_score += 2
            
            # وجود وجوه
            if frame.face_count > 0:
                interest_score += frame.face_count
            
            # سطوع جيد (لا مظلم جداً ولا مضيء جداً)
            if 50 < frame.brightness < 200:
                interest_score += 1
            
            # تباين جيد
            if frame.contrast > 30:
                interest_score += 1
            
            # حدة عالية
            if frame.sharpness > 100:
                interest_score += 1
            
            # تنوع ألوان
            if frame.color_variance > 1000:
                interest_score += 1
            
            return interest_score >= 3
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد إثارة الإطار: {str(e)}")
            return False
    
    def _create_segments(self, frames: List[VideoFrame], 
                        video_info: Dict[str, Any]) -> List[VideoSegment]:
        """تقسيم الفيديو إلى مقاطع"""
        segments = []
        
        try:
            if not frames:
                return segments
            
            segment_duration = self.settings.get("segment_duration", 10.0)
            min_duration = self.settings.get("min_segment_duration", 3.0)
            max_segments = self.settings.get("max_segments", 20)
            
            current_segment = None
            last_scene_change = 0
            
            for i, frame in enumerate(frames):
                # بدء مقطع جديد
                if current_segment is None:
                    current_segment = VideoSegment(frame.timestamp, frame.timestamp)
                
                # إضافة الإطار للمقطع الحالي
                current_segment.frames.append(frame)
                current_segment.end_time = frame.timestamp
                current_segment.duration = current_segment.end_time - current_segment.start_time
                
                # تحديد تغيير المشهد
                scene_change = self._detect_scene_change(frame, frames, i)
                if scene_change:
                    current_segment.scene_changes += 1
                    last_scene_change = i
                
                # إنهاء المقطع إذا وصل للمدة المطلوبة أو تغير المشهد
                should_end_segment = (
                    current_segment.duration >= segment_duration or
                    (scene_change and current_segment.duration >= min_duration) or
                    len(segments) >= max_segments
                )
                
                if should_end_segment:
                    # حساب إحصائيات المقطع
                    self._calculate_segment_stats(current_segment)
                    segments.append(current_segment)
                    current_segment = None
            
            # إضافة المقطع الأخير إذا كان موجوداً
            if current_segment and current_segment.duration >= min_duration:
                self._calculate_segment_stats(current_segment)
                segments.append(current_segment)
            
            return segments
            
        except Exception as e:
            self.logger.error(f"خطأ في تقسيم المقاطع: {str(e)}")
            return []
    
    def _detect_scene_change(self, frame: VideoFrame, 
                           all_frames: List[VideoFrame], index: int) -> bool:
        """كشف تغيير المشهد"""
        try:
            if index == 0:
                return False
            
            prev_frame = all_frames[index - 1]
            threshold = self.settings.get("scene_change_threshold", 0.3)
            
            # مقارنة السطوع والتباين
            brightness_diff = abs(frame.brightness - prev_frame.brightness)
            contrast_diff = abs(frame.contrast - prev_frame.contrast)
            
            # تطبيع القيم
            brightness_change = brightness_diff / 255.0
            contrast_change = contrast_diff / 100.0
            
            # تحديد تغيير المشهد
            return (brightness_change > threshold or 
                   contrast_change > threshold or
                   frame.motion_score > self.motion_threshold * 2)
            
        except Exception as e:
            self.logger.error(f"خطأ في كشف تغيير المشهد: {str(e)}")
            return False
    
    def _calculate_segment_stats(self, segment: VideoSegment):
        """حساب إحصائيات المقطع"""
        try:
            if not segment.frames:
                return
            
            # متوسط الحركة
            motions = [f.motion_score for f in segment.frames]
            segment.avg_motion = np.mean(motions) if motions else 0.0
            
            # متوسط السطوع
            brightness_values = [f.brightness for f in segment.frames]
            segment.avg_brightness = np.mean(brightness_values) if brightness_values else 0.0
            
            # عدد ظهور الوجوه
            segment.face_appearances = sum(f.face_count for f in segment.frames)
            
            # تحديد ما إذا كان مليئاً بالحركة
            high_motion_frames = [f for f in segment.frames if f.motion_score > self.motion_threshold]
            segment.is_action_packed = len(high_motion_frames) > len(segment.frames) * 0.3
            
            # حساب نقاط الإثارة البصرية
            segment.visual_interest_score = self._calculate_visual_interest(segment)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب إحصائيات المقطع: {str(e)}")
    
    def _calculate_visual_interest(self, segment: VideoSegment) -> float:
        """حساب نقاط الإثارة البصرية للمقطع"""
        try:
            score = 0.0
            
            if not segment.frames:
                return score
            
            # نقاط للحركة
            score += min(0.3, segment.avg_motion / 100.0)
            
            # نقاط لظهور الوجوه
            score += min(0.2, segment.face_appearances * 0.05)
            
            # نقاط لتغييرات المشهد
            score += min(0.2, segment.scene_changes * 0.1)
            
            # نقاط للإطارات المثيرة
            interesting_frames = [f for f in segment.frames if f.is_interesting]
            score += min(0.2, len(interesting_frames) / len(segment.frames))
            
            # نقاط للحدة والوضوح
            avg_sharpness = np.mean([f.sharpness for f in segment.frames])
            score += min(0.1, avg_sharpness / 1000.0)
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الإثارة البصرية: {str(e)}")
            return 0.0
    
    def _find_visual_highlights(self, segments: List[VideoSegment]) -> List[Dict[str, Any]]:
        """تحديد أبرز اللحظات البصرية"""
        highlights = []
        
        try:
            for segment in segments:
                # تحديد المقاطع المثيرة
                if (segment.visual_interest_score > 0.6 or 
                    segment.is_action_packed or 
                    segment.face_appearances > 5):
                    
                    highlights.append({
                        "start_time": segment.start_time,
                        "end_time": segment.end_time,
                        "duration": segment.duration,
                        "score": segment.visual_interest_score,
                        "avg_motion": segment.avg_motion,
                        "face_appearances": segment.face_appearances,
                        "scene_changes": segment.scene_changes,
                        "is_action_packed": segment.is_action_packed,
                        "reason": self._get_highlight_reason(segment)
                    })
            
            # ترتيب حسب النقاط
            highlights.sort(key=lambda x: x["score"], reverse=True)
            
            return highlights
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد اللحظات المميزة: {str(e)}")
            return []
    
    def _get_highlight_reason(self, segment: VideoSegment) -> str:
        """تحديد سبب كون المقطع مميزاً"""
        reasons = []
        
        if segment.is_action_packed:
            reasons.append("حركة عالية")
        
        if segment.face_appearances > 5:
            reasons.append("ظهور وجوه")
        
        if segment.scene_changes > 2:
            reasons.append("تغييرات مشهد")
        
        if segment.visual_interest_score > 0.8:
            reasons.append("إثارة بصرية عالية")
        
        return " - ".join(reasons) if reasons else "مثير بصرياً"
    
    def _calculate_video_quality_score(self, video_info: Dict[str, Any],
                                     segments: List[VideoSegment],
                                     highlights: List[Dict[str, Any]]) -> float:
        """حساب تقييم جودة الفيديو"""
        try:
            score = 0.0
            
            # تقييم الجودة التقنية (40%)
            technical_score = 0.0
            
            # دقة الفيديو
            width = video_info.get("width", 0)
            height = video_info.get("height", 0)
            if width >= 1280 and height >= 720:  # HD
                technical_score += 0.4
            elif width >= 854 and height >= 480:  # SD
                technical_score += 0.2
            
            # معدل الإطارات
            fps = video_info.get("fps", 0)
            if fps >= 30:
                technical_score += 0.3
            elif fps >= 24:
                technical_score += 0.2
            
            # نسبة العرض إلى الارتفاع
            aspect_ratio = video_info.get("aspect_ratio", 0)
            if 1.5 <= aspect_ratio <= 2.0:  # نسب جيدة للمحتوى
                technical_score += 0.3
            
            score += technical_score * 0.4
            
            # تقييم المحتوى البصري (40%)
            visual_score = 0.0
            
            if segments:
                # متوسط الإثارة البصرية
                avg_visual_interest = np.mean([seg.visual_interest_score for seg in segments])
                visual_score += avg_visual_interest * 0.5
                
                # نسبة المقاطع المثيرة
                interesting_segments = [seg for seg in segments if seg.visual_interest_score > 0.6]
                visual_score += min(0.3, len(interesting_segments) / len(segments))
                
                # تنوع المحتوى
                action_segments = [seg for seg in segments if seg.is_action_packed]
                visual_score += min(0.2, len(action_segments) / len(segments))
            
            score += visual_score * 0.4
            
            # تقييم اللحظات المميزة (20%)
            highlights_score = 0.0
            
            if highlights:
                # عدد اللحظات المميزة
                highlights_score += min(0.5, len(highlights) * 0.1)
                
                # جودة اللحظات المميزة
                avg_highlight_score = np.mean([h["score"] for h in highlights])
                highlights_score += avg_highlight_score * 0.5
            
            score += highlights_score * 0.2
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب تقييم الفيديو: {str(e)}")
            return 0.0
