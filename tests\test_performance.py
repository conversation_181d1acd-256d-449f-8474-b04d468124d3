#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات الأداء والتحسين
Performance and Optimization Tests
"""

import pytest
import time
import psutil
import threading
import multiprocessing
from unittest.mock import Mock, patch
from pathlib import Path
import tempfile
import numpy as np
import gc

# استيراد الوحدات للاختبار
try:
    from src.content_fetcher.content_manager import ContentManager
    from src.ai_analysis.video_analyzer import VideoAnalyzer
    from src.automated_editing.video_editor import VideoEditor
    from src.automated_publishing.platform_manager import PlatformManager
    from src.security.advanced_encryption import AdvancedEncryptionSystem
except ImportError:
    # في حالة عدم وجود الوحدات، سنقوم بإنشاء كائنات وهمية للاختبار
    ContentManager = Mock
    VideoAnalyzer = Mock
    VideoEditor = Mock
    PlatformManager = Mock
    AdvancedEncryptionSystem = Mock

class PerformanceMonitor:
    """مراقب الأداء للاختبارات"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.start_memory = None
        self.end_memory = None
        self.start_cpu = None
        self.end_cpu = None
    
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.start_time = time.time()
        self.start_memory = psutil.virtual_memory().used
        self.start_cpu = psutil.cpu_percent(interval=None)
        gc.collect()  # تنظيف الذاكرة قبل البدء
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.end_time = time.time()
        self.end_memory = psutil.virtual_memory().used
        self.end_cpu = psutil.cpu_percent(interval=None)
    
    def get_results(self):
        """الحصول على نتائج الأداء"""
        return {
            "execution_time": self.end_time - self.start_time if self.start_time else 0,
            "memory_usage": (self.end_memory - self.start_memory) / 1024 / 1024,  # MB
            "cpu_usage": self.end_cpu - self.start_cpu if self.start_cpu else 0
        }

@pytest.fixture
def performance_monitor():
    """مراقب الأداء للاختبارات"""
    return PerformanceMonitor()

@pytest.mark.performance
class TestContentFetchingPerformance:
    """اختبارات أداء جلب المحتوى"""
    
    @pytest.fixture
    def content_manager(self, temp_dir):
        """إنشاء مدير المحتوى للاختبار"""
        if ContentManager == Mock:
            return Mock()
        return ContentManager(str(temp_dir))
    
    def test_single_user_fetch_performance(self, content_manager, performance_monitor):
        """اختبار أداء جلب محتوى مستخدم واحد"""
        performance_monitor.start_monitoring()
        
        if hasattr(content_manager, 'fetch_user_content'):
            with patch.object(content_manager, '_fetch_from_api') as mock_fetch:
                # محاكاة استجابة API
                mock_fetch.return_value = [
                    {"id": f"video_{i}", "url": f"https://example.com/video_{i}.mp4"}
                    for i in range(50)  # 50 فيديو
                ]
                
                result = content_manager.fetch_user_content("test_user", "tiktok")
                
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 5.0  # أقل من 5 ثوان
        assert results["memory_usage"] < 100  # أقل من 100 MB
        
        print(f"أداء جلب المحتوى: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")
    
    def test_multiple_users_fetch_performance(self, content_manager, performance_monitor):
        """اختبار أداء جلب محتوى عدة مستخدمين"""
        users = [f"user_{i}" for i in range(10)]
        
        performance_monitor.start_monitoring()
        
        if hasattr(content_manager, 'fetch_multiple_users_content'):
            with patch.object(content_manager, '_fetch_from_api') as mock_fetch:
                mock_fetch.return_value = [
                    {"id": f"video_{i}", "url": f"https://example.com/video_{i}.mp4"}
                    for i in range(20)  # 20 فيديو لكل مستخدم
                ]
                
                results = content_manager.fetch_multiple_users_content(users, "tiktok")
                
        performance_monitor.stop_monitoring()
        perf_results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert perf_results["execution_time"] < 30.0  # أقل من 30 ثانية
        assert perf_results["memory_usage"] < 500  # أقل من 500 MB
        
        print(f"أداء جلب متعدد المستخدمين: {perf_results['execution_time']:.2f}s, {perf_results['memory_usage']:.2f}MB")
    
    def test_concurrent_fetch_performance(self, content_manager, performance_monitor):
        """اختبار أداء الجلب المتزامن"""
        def fetch_user_content(user_id):
            if hasattr(content_manager, 'fetch_user_content'):
                with patch.object(content_manager, '_fetch_from_api') as mock_fetch:
                    mock_fetch.return_value = [
                        {"id": f"video_{i}", "url": f"https://example.com/video_{i}.mp4"}
                        for i in range(10)
                    ]
                    return content_manager.fetch_user_content(f"user_{user_id}", "tiktok")
            return []
        
        performance_monitor.start_monitoring()
        
        # تشغيل 5 خيوط متزامنة
        threads = []
        for i in range(5):
            thread = threading.Thread(target=fetch_user_content, args=(i,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 15.0  # أقل من 15 ثانية
        assert results["memory_usage"] < 300  # أقل من 300 MB
        
        print(f"أداء الجلب المتزامن: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")

@pytest.mark.performance
class TestAIAnalysisPerformance:
    """اختبارات أداء تحليل الذكاء الاصطناعي"""
    
    @pytest.fixture
    def video_analyzer(self, temp_dir):
        """إنشاء محلل الفيديو للاختبار"""
        if VideoAnalyzer == Mock:
            return Mock()
        return VideoAnalyzer(str(temp_dir))
    
    @pytest.fixture
    def sample_video_data(self):
        """بيانات فيديو تجريبية"""
        # إنشاء بيانات فيديو وهمية
        return np.random.randint(0, 255, (30, 480, 640, 3), dtype=np.uint8)  # 30 إطار
    
    def test_single_video_analysis_performance(self, video_analyzer, sample_video_data, performance_monitor):
        """اختبار أداء تحليل فيديو واحد"""
        performance_monitor.start_monitoring()
        
        if hasattr(video_analyzer, 'analyze_video'):
            with patch.object(video_analyzer, '_load_video_frames') as mock_load:
                mock_load.return_value = sample_video_data
                
                result = video_analyzer.analyze_video("test_video.mp4")
                
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 10.0  # أقل من 10 ثوان
        assert results["memory_usage"] < 200  # أقل من 200 MB
        
        print(f"أداء تحليل فيديو واحد: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")
    
    def test_batch_video_analysis_performance(self, video_analyzer, sample_video_data, performance_monitor):
        """اختبار أداء تحليل مجموعة فيديوهات"""
        video_files = [f"video_{i}.mp4" for i in range(10)]
        
        performance_monitor.start_monitoring()
        
        if hasattr(video_analyzer, 'analyze_batch'):
            with patch.object(video_analyzer, '_load_video_frames') as mock_load:
                mock_load.return_value = sample_video_data
                
                results = video_analyzer.analyze_batch(video_files)
                
        performance_monitor.stop_monitoring()
        perf_results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert perf_results["execution_time"] < 60.0  # أقل من دقيقة
        assert perf_results["memory_usage"] < 1000  # أقل من 1 GB
        
        print(f"أداء تحليل مجموعة فيديوهات: {perf_results['execution_time']:.2f}s, {perf_results['memory_usage']:.2f}MB")
    
    def test_real_time_analysis_performance(self, video_analyzer, performance_monitor):
        """اختبار أداء التحليل في الوقت الفعلي"""
        performance_monitor.start_monitoring()
        
        if hasattr(video_analyzer, 'start_real_time_analysis'):
            with patch.object(video_analyzer, '_process_frame') as mock_process:
                mock_process.return_value = {"objects": [], "emotions": [], "quality": 0.8}
                
                # محاكاة تحليل 30 إطار (ثانية واحدة بـ 30 FPS)
                for i in range(30):
                    frame_data = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                    video_analyzer._process_frame(frame_data)
                
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء للوقت الفعلي
        assert results["execution_time"] < 2.0  # أقل من ثانيتين لمعالجة ثانية واحدة
        
        print(f"أداء التحليل في الوقت الفعلي: {results['execution_time']:.2f}s")

@pytest.mark.performance
class TestVideoEditingPerformance:
    """اختبارات أداء المونتاج"""
    
    @pytest.fixture
    def video_editor(self, temp_dir):
        """إنشاء محرر الفيديو للاختبار"""
        if VideoEditor == Mock:
            return Mock()
        return VideoEditor(str(temp_dir))
    
    def test_video_merge_performance(self, video_editor, performance_monitor):
        """اختبار أداء دمج الفيديوهات"""
        video_clips = [f"clip_{i}.mp4" for i in range(5)]
        
        performance_monitor.start_monitoring()
        
        if hasattr(video_editor, 'merge_videos'):
            with patch('moviepy.editor.VideoFileClip') as mock_clip:
                with patch('moviepy.editor.concatenate_videoclips') as mock_concat:
                    mock_video = Mock()
                    mock_video.duration = 10.0
                    mock_clip.return_value = mock_video
                    mock_concat.return_value = mock_video
                    
                    result = video_editor.merge_videos(video_clips)
                    
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 30.0  # أقل من 30 ثانية
        assert results["memory_usage"] < 500  # أقل من 500 MB
        
        print(f"أداء دمج الفيديوهات: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")
    
    def test_effects_application_performance(self, video_editor, performance_monitor):
        """اختبار أداء تطبيق التأثيرات"""
        performance_monitor.start_monitoring()
        
        if hasattr(video_editor, 'apply_effects'):
            with patch('moviepy.editor.VideoFileClip') as mock_clip:
                mock_video = Mock()
                mock_video.duration = 30.0  # فيديو 30 ثانية
                mock_clip.return_value = mock_video
                
                effects = ["color_correction", "blur", "speed_change", "transition"]
                result = video_editor.apply_effects("test_video.mp4", effects)
                
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 45.0  # أقل من 45 ثانية
        assert results["memory_usage"] < 800  # أقل من 800 MB
        
        print(f"أداء تطبيق التأثيرات: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")

@pytest.mark.performance
class TestEncryptionPerformance:
    """اختبارات أداء التشفير"""
    
    @pytest.fixture
    def encryption_system(self, temp_dir):
        """إنشاء نظام التشفير للاختبار"""
        if AdvancedEncryptionSystem == Mock:
            return Mock()
        return AdvancedEncryptionSystem(str(temp_dir))
    
    def test_large_file_encryption_performance(self, encryption_system, temp_dir, performance_monitor):
        """اختبار أداء تشفير ملف كبير"""
        # إنشاء ملف كبير (10 MB)
        large_file = temp_dir / "large_file.dat"
        large_data = b"A" * (10 * 1024 * 1024)  # 10 MB
        large_file.write_bytes(large_data)
        
        performance_monitor.start_monitoring()
        
        if hasattr(encryption_system, 'encrypt_file'):
            from src.security.advanced_encryption import EncryptionLevel
            encrypted_file = encryption_system.encrypt_file(
                str(large_file), 
                EncryptionLevel.STANDARD
            )
            
        performance_monitor.stop_monitoring()
        results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert results["execution_time"] < 15.0  # أقل من 15 ثانية لـ 10 MB
        assert results["memory_usage"] < 100  # أقل من 100 MB إضافية
        
        print(f"أداء تشفير ملف كبير: {results['execution_time']:.2f}s, {results['memory_usage']:.2f}MB")
    
    def test_multiple_files_encryption_performance(self, encryption_system, temp_dir, performance_monitor):
        """اختبار أداء تشفير ملفات متعددة"""
        # إنشاء عدة ملفات صغيرة
        files = []
        for i in range(20):
            file_path = temp_dir / f"file_{i}.txt"
            file_path.write_text(f"محتوى الملف رقم {i}" * 100)  # حوالي 2 KB لكل ملف
            files.append(str(file_path))
        
        performance_monitor.start_monitoring()
        
        if hasattr(encryption_system, 'encrypt_multiple_files'):
            from src.security.advanced_encryption import EncryptionLevel
            results = encryption_system.encrypt_multiple_files(
                files, 
                EncryptionLevel.BASIC
            )
            
        performance_monitor.stop_monitoring()
        perf_results = performance_monitor.get_results()
        
        # التحقق من معايير الأداء
        assert perf_results["execution_time"] < 10.0  # أقل من 10 ثوان
        assert perf_results["memory_usage"] < 50  # أقل من 50 MB
        
        print(f"أداء تشفير ملفات متعددة: {perf_results['execution_time']:.2f}s, {perf_results['memory_usage']:.2f}MB")

@pytest.mark.performance
class TestSystemResourceUsage:
    """اختبارات استخدام موارد النظام"""
    
    def test_memory_leak_detection(self):
        """اختبار اكتشاف تسريب الذاكرة"""
        initial_memory = psutil.virtual_memory().used
        
        # محاكاة عمليات متكررة
        for i in range(100):
            # إنشاء بيانات كبيرة ومسحها
            large_data = np.random.random((1000, 1000))
            del large_data
            
            if i % 10 == 0:
                gc.collect()  # تنظيف الذاكرة
        
        final_memory = psutil.virtual_memory().used
        memory_increase = (final_memory - initial_memory) / 1024 / 1024  # MB
        
        # التحقق من عدم وجود تسريب كبير للذاكرة
        assert memory_increase < 50  # أقل من 50 MB زيادة
        
        print(f"زيادة الذاكرة: {memory_increase:.2f}MB")
    
    def test_cpu_usage_monitoring(self):
        """اختبار مراقبة استخدام المعالج"""
        # قياس استخدام المعالج قبل العملية
        cpu_before = psutil.cpu_percent(interval=1)
        
        # محاكاة عملية مكثفة للمعالج
        start_time = time.time()
        while time.time() - start_time < 2:  # تشغيل لمدة ثانيتين
            _ = sum(i * i for i in range(1000))
        
        # قياس استخدام المعالج بعد العملية
        cpu_after = psutil.cpu_percent(interval=1)
        
        print(f"استخدام المعالج: قبل {cpu_before}%, بعد {cpu_after}%")
        
        # التحقق من أن النظام لا يستهلك المعالج بشكل مفرط
        assert cpu_after < 90  # أقل من 90%
    
    def test_disk_io_performance(self, temp_dir):
        """اختبار أداء عمليات القرص"""
        test_file = temp_dir / "io_test.dat"
        data_size = 1024 * 1024  # 1 MB
        test_data = b"A" * data_size
        
        # اختبار الكتابة
        start_time = time.time()
        test_file.write_bytes(test_data)
        write_time = time.time() - start_time
        
        # اختبار القراءة
        start_time = time.time()
        read_data = test_file.read_bytes()
        read_time = time.time() - start_time
        
        # التحقق من الأداء
        assert write_time < 1.0  # أقل من ثانية للكتابة
        assert read_time < 0.5   # أقل من نصف ثانية للقراءة
        assert read_data == test_data  # التحقق من صحة البيانات
        
        print(f"أداء القرص: كتابة {write_time:.3f}s, قراءة {read_time:.3f}s")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
