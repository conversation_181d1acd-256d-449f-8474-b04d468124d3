#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء التطبيق النهائي
Final Application Build Script
"""

import os
import sys
import shutil
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

class AppBuilder:
    """فئة بناء التطبيق"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.app_name = "Smart_Content_Creator"
        self.version = "1.0.0"
        
        # إعداد السجلات
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        import logging
        
        # إنشاء مجلد السجلات
        logs_dir = self.project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        
        # إعداد السجل
        log_file = logs_dir / f"build_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        
    def log(self, message, level="info"):
        """تسجيل رسالة"""
        print(f"🔧 {message}")
        if hasattr(self.logger, level):
            getattr(self.logger, level)(message)
    
    def check_requirements(self):
        """فحص المتطلبات"""
        self.log("فحص المتطلبات...")
        
        # فحص Python
        python_version = sys.version_info
        if python_version < (3, 8):
            raise Exception(f"يتطلب Python 3.8+ (الحالي: {python_version.major}.{python_version.minor})")
        
        self.log(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # فحص PyInstaller
        try:
            result = subprocess.run(["py", "-m", "PyInstaller", "--version"],
                                  capture_output=True, text=True, check=True)
            version = result.stdout.strip()
            self.log(f"✅ PyInstaller {version}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("❌ PyInstaller غير مثبت", "error")
            return False
        
        # فحص الملفات الأساسية
        required_files = [
            "main.py",
            "requirements.txt",
            "smart_content_creator.spec"
        ]
        
        for file in required_files:
            if not (self.project_root / file).exists():
                self.log(f"❌ ملف مطلوب مفقود: {file}", "error")
                return False
            self.log(f"✅ {file}")
        
        # فحص المجلدات الأساسية
        required_dirs = ["src", "config"]
        for dir_name in required_dirs:
            if not (self.project_root / dir_name).exists():
                self.log(f"❌ مجلد مطلوب مفقود: {dir_name}", "error")
                return False
            self.log(f"✅ {dir_name}/")
        
        return True
    
    def clean_build_dirs(self):
        """تنظيف مجلدات البناء"""
        self.log("تنظيف مجلدات البناء السابقة...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"✅ تم حذف {dir_path.name}")
                except Exception as e:
                    self.log(f"⚠️ تعذر حذف {dir_path.name}: {e}", "warning")
    
    def install_dependencies(self):
        """تثبيت المتطلبات"""
        self.log("تثبيت المتطلبات...")
        
        try:
            # تحديث pip
            subprocess.run([
                "py", "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True)

            # تثبيت المتطلبات الأساسية للبناء
            subprocess.run([
                "py", "-m", "pip", "install", "-r", "requirements_build.txt"
            ], check=True, capture_output=True)
            
            self.log("✅ تم تثبيت جميع المتطلبات")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ فشل في تثبيت المتطلبات: {e}", "error")
            return False
    
    def create_version_info(self):
        """إنشاء ملف معلومات الإصدار"""
        self.log("إنشاء ملف معلومات الإصدار...")
        
        version_info = f"""
# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Augment Code'),
        StringStruct(u'FileDescription', u'Smart Content Creator - منشئ المحتوى الذكي'),
        StringStruct(u'FileVersion', u'{self.version}'),
        StringStruct(u'InternalName', u'{self.app_name}'),
        StringStruct(u'LegalCopyright', u'© 2025 Augment Code. All rights reserved.'),
        StringStruct(u'OriginalFilename', u'{self.app_name}.exe'),
        StringStruct(u'ProductName', u'Smart Content Creator'),
        StringStruct(u'ProductVersion', u'{self.version}')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
        
        version_file = self.project_root / "version_info.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        self.log("✅ تم إنشاء ملف معلومات الإصدار")
        return str(version_file)
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        self.log("بدء بناء الملف التنفيذي...")
        
        try:
            # إنشاء ملف معلومات الإصدار
            version_file = self.create_version_info()
            
            # تشغيل PyInstaller
            cmd = [
                "py", "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                "smart_content_creator.spec"
            ]
            
            self.log(f"تشغيل الأمر: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8'
            )
            
            # عرض التقدم
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(f"   {output.strip()}")
            
            return_code = process.poll()
            
            if return_code == 0:
                self.log("✅ تم بناء الملف التنفيذي بنجاح")
                return True
            else:
                self.log(f"❌ فشل في بناء الملف التنفيذي (كود الخروج: {return_code})", "error")
                return False
                
        except Exception as e:
            self.log(f"❌ خطأ في بناء الملف التنفيذي: {e}", "error")
            return False
    
    def verify_build(self):
        """التحقق من البناء"""
        self.log("التحقق من البناء...")
        
        exe_file = self.dist_dir / f"{self.app_name}.exe"
        
        if not exe_file.exists():
            self.log("❌ لم يتم العثور على الملف التنفيذي", "error")
            return False
        
        # فحص حجم الملف
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        self.log(f"📦 حجم الملف التنفيذي: {file_size:.1f} MB")
        
        if file_size < 10:
            self.log("⚠️ حجم الملف صغير جداً، قد يكون هناك مشكلة", "warning")
        
        self.log("✅ تم التحقق من البناء بنجاح")
        return True
    
    def create_installer_info(self):
        """إنشاء معلومات المثبت"""
        self.log("إنشاء معلومات المثبت...")
        
        installer_info = {
            "app_name": "Smart Content Creator",
            "app_version": self.version,
            "app_description": "تطبيق ذكي لجمع ومعالجة ونشر المحتوى تلقائياً",
            "developer": "Augment Code",
            "build_date": datetime.now().isoformat(),
            "executable_file": f"{self.app_name}.exe",
            "requirements": {
                "os": "Windows 10/11",
                "ram": "4 GB minimum, 8 GB recommended",
                "storage": "2 GB free space",
                "internet": "Required for content fetching and publishing"
            },
            "features": [
                "جمع المحتوى من Snapchat و TikTok/Kick",
                "تحليل ذكي للمحتوى باستخدام AI",
                "مونتاج تلقائي للفيديو",
                "نشر تلقائي على TikTok",
                "أنظمة أمان متقدمة",
                "واجهة مستخدم عربية"
            ]
        }
        
        info_file = self.dist_dir / "installer_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(installer_info, f, indent=2, ensure_ascii=False)
        
        self.log("✅ تم إنشاء معلومات المثبت")
    
    def build(self):
        """بناء التطبيق الكامل"""
        start_time = time.time()
        
        self.log("🚀 بدء بناء التطبيق النهائي")
        self.log("=" * 50)
        
        try:
            # 1. فحص المتطلبات
            if not self.check_requirements():
                return False
            
            # 2. تنظيف المجلدات
            self.clean_build_dirs()
            
            # 3. تثبيت المتطلبات
            if not self.install_dependencies():
                return False
            
            # 4. بناء الملف التنفيذي
            if not self.build_executable():
                return False
            
            # 5. التحقق من البناء
            if not self.verify_build():
                return False
            
            # 6. إنشاء معلومات المثبت
            self.create_installer_info()
            
            # النتائج النهائية
            build_time = time.time() - start_time
            
            self.log("=" * 50)
            self.log("🎉 تم بناء التطبيق بنجاح!")
            self.log(f"⏱️ وقت البناء: {build_time:.1f} ثانية")
            self.log(f"📁 مجلد التوزيع: {self.dist_dir}")
            self.log(f"🎯 الملف التنفيذي: {self.app_name}.exe")
            
            return True
            
        except Exception as e:
            self.log(f"❌ فشل في بناء التطبيق: {e}", "error")
            return False

def main():
    """الوظيفة الرئيسية"""
    builder = AppBuilder()
    success = builder.build()
    
    if success:
        print("\n✅ تم إكمال البناء بنجاح!")
        print("يمكنك الآن العثور على التطبيق في مجلد 'dist'")
    else:
        print("\n❌ فشل في بناء التطبيق!")
        print("راجع السجلات للحصول على تفاصيل أكثر")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
