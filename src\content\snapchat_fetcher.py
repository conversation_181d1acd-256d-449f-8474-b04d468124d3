#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جالب محتوى Snapchat - Snapchat Content Fetcher
يجلب Stories والمحتوى من Snapchat باستخدام API غير رسمي
"""

import logging
import requests
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import re
from urllib.parse import urlparse, parse_qs

from .base_fetcher import BaseFetcher, ContentItem

class SnapchatFetcher(BaseFetcher):
    """جالب محتوى Snapchat"""
    
    def __init__(self, config_manager, security_manager):
        super().__init__("snapchat", config_manager, security_manager)
        
        # إعدادات Snapchat API
        self.base_url = "https://story.snapchat.com"
        self.api_base = "https://ms.sc-jpl.com"
        
        # رؤوس خاصة بـ Snapchat
        self.session.headers.update({
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Origin': 'https://story.snapchat.com',
            'Referer': 'https://story.snapchat.com/',
            'X-Snapchat-Client-Auth-Token': '',
            'X-Snapchat-Client-Version': '12.0.0'
        })
        
        # قائمة المستخدمين المراقبين
        self.monitored_users = self._load_monitored_users()
        
        # معرفات المحتوى المعروف
        self.known_story_ids = set()
    
    def _load_monitored_users(self) -> List[str]:
        """تحميل قائمة المستخدمين المراقبين"""
        try:
            users = self.config_manager.get_setting("snapchat_settings", "monitored_users", [])
            return users if isinstance(users, list) else []
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المستخدمين المراقبين: {str(e)}")
            return []
    
    def add_monitored_user(self, username: str) -> bool:
        """إضافة مستخدم للمراقبة"""
        try:
            if username not in self.monitored_users:
                self.monitored_users.append(username)
                self.config_manager.set_setting(
                    "snapchat_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إضافة المستخدم للمراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المستخدم: {str(e)}")
            return False
    
    def remove_monitored_user(self, username: str) -> bool:
        """إزالة مستخدم من المراقبة"""
        try:
            if username in self.monitored_users:
                self.monitored_users.remove(username)
                self.config_manager.set_setting(
                    "snapchat_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إزالة المستخدم من المراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إزالة المستخدم: {str(e)}")
            return False
    
    def _build_auth_headers(self, token: str) -> Dict[str, str]:
        """بناء رؤوس المصادقة لـ Snapchat"""
        return {
            'X-Snapchat-Client-Auth-Token': token,
            'Authorization': f'Bearer {token}'
        }
    
    def test_connection(self) -> bool:
        """اختبار الاتصال مع Snapchat"""
        try:
            if not self.is_authenticated():
                self.logger.warning("لا يوجد رمز مصادقة")
                return False
            
            # محاولة الوصول لصفحة المستخدم
            headers = self.get_auth_headers()
            self.session.headers.update(headers)
            
            # اختبار بسيط للاتصال
            response = self.session.get(f"{self.base_url}/", timeout=10)
            
            if response.status_code == 200:
                self.logger.info("تم اختبار الاتصال بنجاح")
                return True
            else:
                self.logger.error(f"فشل اختبار الاتصال: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {str(e)}")
            return False
    
    def fetch_content(self, content_types: List[str] = None, 
                     limit: int = 10) -> List[ContentItem]:
        """جلب المحتوى من Snapchat"""
        try:
            if not self.is_authenticated():
                raise ValueError("غير مصادق مع Snapchat")
            
            content_types = content_types or ["stories"]
            all_content = []
            
            # تحديث رؤوس المصادقة
            headers = self.get_auth_headers()
            self.session.headers.update(headers)
            
            if "stories" in content_types:
                stories = self._fetch_stories(limit)
                all_content.extend(stories)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            all_content.sort(key=lambda x: x.timestamp, reverse=True)
            
            self.stats["total_fetched"] += len(all_content)
            return all_content[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المحتوى: {str(e)}")
            return []
    
    def _fetch_stories(self, limit: int) -> List[ContentItem]:
        """جلب Stories من المستخدمين المراقبين"""
        stories = []
        
        try:
            for username in self.monitored_users:
                user_stories = self._fetch_user_stories(username, limit // len(self.monitored_users) + 1)
                stories.extend(user_stories)
                
                # تأخير بسيط لتجنب Rate Limiting
                time.sleep(1)
                
                if len(stories) >= limit:
                    break
            
            return stories
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب Stories: {str(e)}")
            return []
    
    def _fetch_user_stories(self, username: str, limit: int) -> List[ContentItem]:
        """جلب Stories لمستخدم معين"""
        try:
            # محاولة الوصول لصفحة المستخدم
            user_url = f"{self.base_url}/add/{username}"
            response = self.session.get(user_url, timeout=15)
            
            if response.status_code != 200:
                self.logger.warning(f"لا يمكن الوصول لمستخدم: {username}")
                return []
            
            # استخراج معلومات Stories من HTML
            stories = self._parse_stories_from_html(response.text, username)
            
            # فلترة Stories الجديدة فقط
            new_stories = []
            for story in stories:
                if story.id not in self.known_story_ids:
                    new_stories.append(story)
                    self.known_story_ids.add(story.id)
            
            return new_stories[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب stories للمستخدم {username}: {str(e)}")
            return []
    
    def _parse_stories_from_html(self, html_content: str, username: str) -> List[ContentItem]:
        """استخراج معلومات Stories من HTML"""
        stories = []
        
        try:
            # البحث عن JSON data في HTML
            json_pattern = r'window\.__INITIAL_STATE__\s*=\s*({.*?});'
            match = re.search(json_pattern, html_content, re.DOTALL)
            
            if not match:
                # محاولة نمط آخر
                json_pattern = r'<script[^>]*>.*?var\s+initialState\s*=\s*({.*?});'
                match = re.search(json_pattern, html_content, re.DOTALL)
            
            if match:
                try:
                    data = json.loads(match.group(1))
                    stories = self._extract_stories_from_data(data, username)
                except json.JSONDecodeError:
                    self.logger.warning(f"لا يمكن تحليل JSON للمستخدم: {username}")
            
            # إذا فشل استخراج JSON، استخدم regex للبحث عن روابط الفيديو
            if not stories:
                stories = self._extract_stories_with_regex(html_content, username)
            
            return stories
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل HTML: {str(e)}")
            return []
    
    def _extract_stories_from_data(self, data: Dict, username: str) -> List[ContentItem]:
        """استخراج Stories من بيانات JSON"""
        stories = []
        
        try:
            # البحث في هيكل البيانات عن Stories
            if "stories" in data:
                story_data = data["stories"]
            elif "user" in data and "stories" in data["user"]:
                story_data = data["user"]["stories"]
            else:
                return []
            
            for story in story_data:
                if isinstance(story, dict):
                    content_item = self._create_story_item(story, username)
                    if content_item:
                        stories.append(content_item)
            
            return stories
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج Stories من البيانات: {str(e)}")
            return []
    
    def _extract_stories_with_regex(self, html_content: str, username: str) -> List[ContentItem]:
        """استخراج Stories باستخدام regex كبديل"""
        stories = []
        
        try:
            # البحث عن روابط الفيديو
            video_pattern = r'https://[^"\']*\.mp4[^"\']*'
            video_urls = re.findall(video_pattern, html_content)
            
            # البحث عن روابط الصور
            image_pattern = r'https://[^"\']*\.(jpg|jpeg|png)[^"\']*'
            image_urls = re.findall(image_pattern, html_content)
            
            # إنشاء عناصر محتوى من الروابط
            for i, url in enumerate(video_urls[:5]):  # أول 5 فيديوهات
                story_item = ContentItem(
                    platform="snapchat",
                    content_type="story",
                    url=url,
                    title=f"Story من {username}",
                    author=username,
                    timestamp=datetime.now() - timedelta(minutes=i*5),
                    metadata={"format": "video", "source": "regex_extraction"}
                )
                stories.append(story_item)
            
            for i, (url, ext) in enumerate(image_urls[:3]):  # أول 3 صور
                story_item = ContentItem(
                    platform="snapchat",
                    content_type="story",
                    url=url,
                    title=f"Story صورة من {username}",
                    author=username,
                    timestamp=datetime.now() - timedelta(minutes=(len(video_urls)+i)*5),
                    metadata={"format": "image", "source": "regex_extraction"}
                )
                stories.append(story_item)
            
            return stories
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج Stories بـ regex: {str(e)}")
            return []
    
    def _create_story_item(self, story_data: Dict, username: str) -> Optional[ContentItem]:
        """إنشاء عنصر Story من البيانات"""
        try:
            # استخراج URL المحتوى
            media_url = None
            if "media_url" in story_data:
                media_url = story_data["media_url"]
            elif "url" in story_data:
                media_url = story_data["url"]
            elif "video_url" in story_data:
                media_url = story_data["video_url"]
            
            if not media_url:
                return None
            
            # استخراج معلومات إضافية
            title = story_data.get("title", f"Story من {username}")
            description = story_data.get("description", "")
            
            # تحديد نوع المحتوى
            content_format = "video" if any(ext in media_url.lower() for ext in ['.mp4', '.mov']) else "image"
            
            # إنشاء timestamp
            timestamp = datetime.now()
            if "timestamp" in story_data:
                try:
                    timestamp = datetime.fromtimestamp(story_data["timestamp"])
                except:
                    pass
            
            return ContentItem(
                platform="snapchat",
                content_type="story",
                url=media_url,
                title=title,
                description=description,
                author=username,
                timestamp=timestamp,
                metadata={
                    "format": content_format,
                    "source": "api_extraction",
                    "original_data": story_data
                }
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء عنصر Story: {str(e)}")
            return None
    
    def authenticate_with_credentials(self, username: str, password: str) -> bool:
        """المصادقة باستخدام اسم المستخدم وكلمة المرور"""
        try:
            # هذه وظيفة تجريبية - في التطبيق الحقيقي ستحتاج لتنفيذ OAuth
            self.logger.warning("المصادقة التلقائية غير مدعومة حالياً")
            self.logger.info("يرجى الحصول على رمز المصادقة يدوياً وإدخاله في الإعدادات")
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في المصادقة: {str(e)}")
            return False
    
    def get_monitored_users(self) -> List[str]:
        """الحصول على قائمة المستخدمين المراقبين"""
        return self.monitored_users.copy()
    
    def get_platform_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات خاصة بـ Snapchat"""
        base_stats = self.get_stats()
        base_stats.update({
            "monitored_users_count": len(self.monitored_users),
            "monitored_users": self.monitored_users,
            "known_stories": len(self.known_story_ids)
        })
        return base_stats
