#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التشفير المتقدم - Advanced Encryption System
نظام تشفير شامل للبيانات الحساسة مع دعم تشفير متعدد المستويات
"""

import os
import json
import base64
import hashlib
import secrets
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

# مكتبات التشفير
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

# استيراد keyring بشكل اختياري
try:
    import keyring
    KEYRING_AVAILABLE = True
except ImportError:
    KEYRING_AVAILABLE = False
    keyring = None

class EncryptionLevel(Enum):
    """مستويات التشفير"""
    BASIC = "basic"           # تشفير أساسي - Fernet
    STANDARD = "standard"     # تشفير قياسي - AES-256
    ADVANCED = "advanced"     # تشفير متقدم - RSA + AES
    MILITARY = "military"     # تشفير عسكري - متعدد الطبقات

class KeyType(Enum):
    """أنواع المفاتيح"""
    SYMMETRIC = "symmetric"   # مفاتيح متماثلة
    ASYMMETRIC = "asymmetric" # مفاتيح غير متماثلة
    DERIVED = "derived"       # مفاتيح مشتقة
    SESSION = "session"       # مفاتيح الجلسة

@dataclass
class EncryptionKey:
    """مفتاح التشفير"""
    key_id: str
    key_type: KeyType
    encryption_level: EncryptionLevel
    key_data: bytes
    salt: Optional[bytes] = None
    created_at: datetime = None
    expires_at: Optional[datetime] = None
    usage_count: int = 0
    max_usage: Optional[int] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class EncryptedData:
    """البيانات المشفرة"""
    data_id: str
    encrypted_content: bytes
    encryption_level: EncryptionLevel
    key_id: str
    salt: Optional[bytes] = None
    iv: Optional[bytes] = None
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}

class AdvancedEncryptionSystem:
    """نظام التشفير المتقدم"""
    
    def __init__(self, config_manager=None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # مجلدات النظام
        self.security_dir = Path.home() / ".smart_content_app" / "security"
        self.keys_dir = self.security_dir / "keys"
        self.encrypted_data_dir = self.security_dir / "encrypted_data"
        
        # إنشاء المجلدات
        self.security_dir.mkdir(parents=True, exist_ok=True)
        self.keys_dir.mkdir(exist_ok=True)
        self.encrypted_data_dir.mkdir(exist_ok=True)
        
        # ملفات النظام
        self.master_key_file = self.security_dir / "master.key"
        self.keys_index_file = self.security_dir / "keys_index.json"
        self.encryption_log_file = self.security_dir / "encryption.log"
        
        # إعداد النظام
        self._setup_encryption_system()
        
        # مخزن المفاتيح
        self.keys_cache = {}
        self.session_keys = {}
        
        # إحصائيات التشفير
        self.encryption_stats = {
            "total_encryptions": 0,
            "total_decryptions": 0,
            "failed_operations": 0,
            "keys_generated": 0,
            "last_operation": None
        }
        
        # إعدادات التشفير
        self.encryption_settings = {
            "default_level": EncryptionLevel.STANDARD,
            "key_rotation_days": 30,
            "max_key_usage": 10000,
            "auto_cleanup": True,
            "backup_keys": True
        }
        
        self.logger.info("تم تهيئة نظام التشفير المتقدم")
    
    def _setup_encryption_system(self):
        """إعداد نظام التشفير"""
        try:
            # إنشاء أو تحميل المفتاح الرئيسي
            self.master_key = self._get_or_create_master_key()
            
            # تحميل فهرس المفاتيح
            self._load_keys_index()
            
            # إعداد تسجيل العمليات
            self._setup_encryption_logging()
            
            self.logger.info("تم إعداد نظام التشفير بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد نظام التشفير: {str(e)}")
            raise
    
    def _get_or_create_master_key(self) -> bytes:
        """الحصول على أو إنشاء المفتاح الرئيسي"""
        try:
            # محاولة الحصول على المفتاح من keyring إذا كان متاحاً
            if KEYRING_AVAILABLE:
                master_key_b64 = keyring.get_password("smart_content_app", "master_encryption_key")

                if master_key_b64:
                    master_key = base64.b64decode(master_key_b64)
                    self.logger.info("تم تحميل المفتاح الرئيسي من keyring")
                    return master_key

            # محاولة تحميل المفتاح من الملف المحلي
            if self.master_key_file.exists():
                with open(self.master_key_file, 'rb') as f:
                    encrypted_master_key = f.read()

                # فك تشفير المفتاح الرئيسي باستخدام معلومات النظام
                system_key = self._get_system_key()
                fernet = Fernet(base64.urlsafe_b64encode(system_key))
                master_key = fernet.decrypt(encrypted_master_key)

                self.logger.info("تم تحميل المفتاح الرئيسي من الملف المحلي")
                return master_key

            # إنشاء مفتاح رئيسي جديد
            master_key = secrets.token_bytes(32)  # 256-bit key

            # حفظ المفتاح في keyring إذا كان متاحاً
            if KEYRING_AVAILABLE:
                master_key_b64 = base64.b64encode(master_key).decode()
                keyring.set_password("smart_content_app", "master_encryption_key", master_key_b64)

            # حفظ نسخة احتياطية مشفرة
            self._backup_master_key(master_key)

            self.logger.info("تم إنشاء مفتاح رئيسي جديد")
            return master_key

        except Exception as e:
            self.logger.error(f"خطأ في إدارة المفتاح الرئيسي: {str(e)}")
            # إنشاء مفتاح مؤقت للطوارئ
            return secrets.token_bytes(32)

    def _get_system_key(self) -> bytes:
        """الحصول على مفتاح النظام للتشفير المحلي"""
        try:
            # استخدام معلومات النظام لإنشاء مفتاح ثابت
            import platform
            system_info = f"{platform.node()}{platform.system()}{platform.processor()}"
            system_key = hashlib.sha256(system_info.encode()).digest()
            return system_key
        except Exception as e:
            self.logger.warning(f"خطأ في الحصول على مفتاح النظام: {str(e)}")
            # مفتاح افتراضي في حالة الخطأ
            return hashlib.sha256(b"default_system_key").digest()
    
    def _backup_master_key(self, master_key: bytes):
        """إنشاء نسخة احتياطية من المفتاح الرئيسي"""
        try:
            # تشفير المفتاح الرئيسي بكلمة مرور مشتقة من معلومات النظام
            system_info = f"{os.getenv('COMPUTERNAME', 'unknown')}{os.getenv('USERNAME', 'user')}"
            password = hashlib.sha256(system_info.encode()).digest()
            
            # إنشاء مفتاح مشتق
            salt = secrets.token_bytes(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            derived_key = kdf.derive(password)
            
            # تشفير المفتاح الرئيسي
            fernet = Fernet(base64.urlsafe_b64encode(derived_key))
            encrypted_master_key = fernet.encrypt(master_key)
            
            # حفظ النسخة الاحتياطية
            backup_data = {
                "salt": base64.b64encode(salt).decode(),
                "encrypted_key": base64.b64encode(encrypted_master_key).decode(),
                "created_at": datetime.now().isoformat()
            }
            
            with open(self.master_key_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2)
            
            self.logger.info("تم إنشاء نسخة احتياطية من المفتاح الرئيسي")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء نسخة احتياطية من المفتاح الرئيسي: {str(e)}")
    
    def _load_keys_index(self):
        """تحميل فهرس المفاتيح"""
        try:
            if self.keys_index_file.exists():
                with open(self.keys_index_file, 'r', encoding='utf-8') as f:
                    self.keys_index = json.load(f)
            else:
                self.keys_index = {}
            
            self.logger.info(f"تم تحميل فهرس المفاتيح: {len(self.keys_index)} مفتاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل فهرس المفاتيح: {str(e)}")
            self.keys_index = {}
    
    def _setup_encryption_logging(self):
        """إعداد تسجيل عمليات التشفير"""
        try:
            # إنشاء logger منفصل لعمليات التشفير
            encryption_logger = logging.getLogger("encryption_operations")
            encryption_logger.setLevel(logging.INFO)
            
            # إنشاء handler للملف
            file_handler = logging.FileHandler(self.encryption_log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # تنسيق الرسائل
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            encryption_logger.addHandler(file_handler)
            self.encryption_logger = encryption_logger
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد تسجيل التشفير: {str(e)}")
            self.encryption_logger = self.logger
    
    def generate_key(self, key_type: KeyType, encryption_level: EncryptionLevel, 
                    expires_in_days: Optional[int] = None, max_usage: Optional[int] = None) -> EncryptionKey:
        """إنتاج مفتاح تشفير جديد"""
        try:
            key_id = f"{key_type.value}_{encryption_level.value}_{secrets.token_hex(8)}"
            
            # إنتاج المفتاح حسب النوع والمستوى
            if key_type == KeyType.SYMMETRIC:
                key_data = self._generate_symmetric_key(encryption_level)
            elif key_type == KeyType.ASYMMETRIC:
                key_data = self._generate_asymmetric_key(encryption_level)
            elif key_type == KeyType.DERIVED:
                key_data, salt = self._generate_derived_key(encryption_level)
            else:  # SESSION
                key_data = self._generate_session_key(encryption_level)
            
            # تحديد تاريخ انتهاء الصلاحية
            expires_at = None
            if expires_in_days:
                expires_at = datetime.now() + timedelta(days=expires_in_days)
            
            # إنشاء كائن المفتاح
            encryption_key = EncryptionKey(
                key_id=key_id,
                key_type=key_type,
                encryption_level=encryption_level,
                key_data=key_data,
                salt=salt if key_type == KeyType.DERIVED else None,
                expires_at=expires_at,
                max_usage=max_usage
            )
            
            # حفظ المفتاح
            self._save_key(encryption_key)
            
            # تحديث الإحصائيات
            self.encryption_stats["keys_generated"] += 1
            self.encryption_stats["last_operation"] = datetime.now().isoformat()
            
            self.encryption_logger.info(f"تم إنتاج مفتاح جديد: {key_id}")
            return encryption_key
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج المفتاح: {str(e)}")
            raise
    
    def _generate_symmetric_key(self, level: EncryptionLevel) -> bytes:
        """إنتاج مفتاح متماثل"""
        if level == EncryptionLevel.BASIC:
            return Fernet.generate_key()
        elif level in [EncryptionLevel.STANDARD, EncryptionLevel.ADVANCED]:
            return secrets.token_bytes(32)  # AES-256
        else:  # MILITARY
            return secrets.token_bytes(32)  # سيتم استخدامه في تشفير متعدد الطبقات
    
    def _generate_asymmetric_key(self, level: EncryptionLevel) -> bytes:
        """إنتاج مفتاح غير متماثل"""
        if level == EncryptionLevel.BASIC:
            key_size = 2048
        elif level == EncryptionLevel.STANDARD:
            key_size = 3072
        elif level == EncryptionLevel.ADVANCED:
            key_size = 4096
        else:  # MILITARY
            key_size = 4096
        
        # إنتاج مفتاح RSA
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size,
            backend=default_backend()
        )
        
        # تسلسل المفتاح الخاص
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        return private_pem
    
    def _generate_derived_key(self, level: EncryptionLevel) -> Tuple[bytes, bytes]:
        """إنتاج مفتاح مشتق"""
        # إنتاج salt عشوائي
        salt = secrets.token_bytes(16)
        
        # استخدام المفتاح الرئيسي كأساس
        password = self.master_key
        
        # تحديد عدد التكرارات حسب المستوى
        iterations = {
            EncryptionLevel.BASIC: 100000,
            EncryptionLevel.STANDARD: 200000,
            EncryptionLevel.ADVANCED: 500000,
            EncryptionLevel.MILITARY: 1000000
        }
        
        # إنتاج المفتاح المشتق
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=iterations[level],
            backend=default_backend()
        )
        
        derived_key = kdf.derive(password)
        return derived_key, salt
    
    def _generate_session_key(self, level: EncryptionLevel) -> bytes:
        """إنتاج مفتاح جلسة"""
        # مفاتيح الجلسة دائماً مؤقتة وقوية
        return secrets.token_bytes(32)

    def _save_key(self, encryption_key: EncryptionKey):
        """حفظ المفتاح بشكل آمن"""
        try:
            # تشفير بيانات المفتاح بالمفتاح الرئيسي
            key_data_dict = asdict(encryption_key)
            key_data_dict["key_data"] = base64.b64encode(encryption_key.key_data).decode()
            if encryption_key.salt:
                key_data_dict["salt"] = base64.b64encode(encryption_key.salt).decode()

            # تحويل التواريخ إلى نصوص
            key_data_dict["created_at"] = encryption_key.created_at.isoformat()
            if encryption_key.expires_at:
                key_data_dict["expires_at"] = encryption_key.expires_at.isoformat()

            # تحويل Enums إلى نصوص
            key_data_dict["key_type"] = encryption_key.key_type.value
            key_data_dict["encryption_level"] = encryption_key.encryption_level.value

            # تشفير البيانات
            key_json = json.dumps(key_data_dict, ensure_ascii=False)
            fernet = Fernet(base64.urlsafe_b64encode(self.master_key))
            encrypted_key_data = fernet.encrypt(key_json.encode())

            # حفظ المفتاح المشفر
            key_file = self.keys_dir / f"{encryption_key.key_id}.key"
            with open(key_file, 'wb') as f:
                f.write(encrypted_key_data)

            # تحديث فهرس المفاتيح
            self.keys_index[encryption_key.key_id] = {
                "key_type": encryption_key.key_type.value,
                "encryption_level": encryption_key.encryption_level.value,
                "created_at": encryption_key.created_at.isoformat(),
                "expires_at": encryption_key.expires_at.isoformat() if encryption_key.expires_at else None,
                "usage_count": encryption_key.usage_count,
                "max_usage": encryption_key.max_usage,
                "file_path": str(key_file)
            }

            # حفظ فهرس المفاتيح
            with open(self.keys_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.keys_index, f, ensure_ascii=False, indent=2)

            # إضافة إلى الذاكرة المؤقتة
            self.keys_cache[encryption_key.key_id] = encryption_key

            self.logger.info(f"تم حفظ المفتاح: {encryption_key.key_id}")

        except Exception as e:
            self.logger.error(f"خطأ في حفظ المفتاح: {str(e)}")
            raise

    def _load_key(self, key_id: str) -> Optional[EncryptionKey]:
        """تحميل مفتاح من التخزين"""
        try:
            # التحقق من الذاكرة المؤقتة أولاً
            if key_id in self.keys_cache:
                return self.keys_cache[key_id]

            # التحقق من وجود المفتاح في الفهرس
            if key_id not in self.keys_index:
                return None

            # تحميل المفتاح من الملف
            key_info = self.keys_index[key_id]
            key_file = Path(key_info["file_path"])

            if not key_file.exists():
                self.logger.warning(f"ملف المفتاح غير موجود: {key_file}")
                return None

            # فك تشفير المفتاح
            with open(key_file, 'rb') as f:
                encrypted_key_data = f.read()

            fernet = Fernet(base64.urlsafe_b64encode(self.master_key))
            decrypted_data = fernet.decrypt(encrypted_key_data)
            key_data_dict = json.loads(decrypted_data.decode())

            # إعادة بناء كائن المفتاح
            encryption_key = EncryptionKey(
                key_id=key_data_dict["key_id"],
                key_type=KeyType(key_data_dict["key_type"]),
                encryption_level=EncryptionLevel(key_data_dict["encryption_level"]),
                key_data=base64.b64decode(key_data_dict["key_data"]),
                salt=base64.b64decode(key_data_dict["salt"]) if key_data_dict.get("salt") else None,
                created_at=datetime.fromisoformat(key_data_dict["created_at"]),
                expires_at=datetime.fromisoformat(key_data_dict["expires_at"]) if key_data_dict.get("expires_at") else None,
                usage_count=key_data_dict.get("usage_count", 0),
                max_usage=key_data_dict.get("max_usage")
            )

            # إضافة إلى الذاكرة المؤقتة
            self.keys_cache[key_id] = encryption_key

            return encryption_key

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المفتاح {key_id}: {str(e)}")
            return None

    def _is_key_valid(self, encryption_key: EncryptionKey) -> bool:
        """التحقق من صلاحية المفتاح"""
        try:
            # التحقق من انتهاء الصلاحية
            if encryption_key.expires_at and datetime.now() > encryption_key.expires_at:
                return False

            # التحقق من عدد الاستخدامات
            if encryption_key.max_usage and encryption_key.usage_count >= encryption_key.max_usage:
                return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من صلاحية المفتاح: {str(e)}")
            return False

    def encrypt_data(self, data: Union[str, bytes, Dict[str, Any]],
                    encryption_level: EncryptionLevel = None,
                    key_id: Optional[str] = None) -> EncryptedData:
        """تشفير البيانات"""
        try:
            # تحديد مستوى التشفير
            if encryption_level is None:
                encryption_level = self.encryption_settings["default_level"]

            # تحضير البيانات للتشفير
            if isinstance(data, dict):
                data_bytes = json.dumps(data, ensure_ascii=False).encode()
            elif isinstance(data, str):
                data_bytes = data.encode()
            else:
                data_bytes = data

            # الحصول على أو إنشاء مفتاح التشفير
            if key_id:
                encryption_key = self._load_key(key_id)
                if not encryption_key:
                    raise ValueError(f"المفتاح غير موجود: {key_id}")
            else:
                encryption_key = self.generate_key(
                    KeyType.SYMMETRIC,
                    encryption_level,
                    expires_in_days=self.encryption_settings["key_rotation_days"],
                    max_usage=self.encryption_settings["max_key_usage"]
                )

            # التحقق من صلاحية المفتاح
            if not self._is_key_valid(encryption_key):
                raise ValueError("المفتاح غير صالح أو منتهي الصلاحية")

            # تشفير البيانات حسب المستوى
            if encryption_level == EncryptionLevel.BASIC:
                encrypted_content, iv = self._encrypt_basic(data_bytes, encryption_key)
            elif encryption_level == EncryptionLevel.STANDARD:
                encrypted_content, iv = self._encrypt_standard(data_bytes, encryption_key)
            elif encryption_level == EncryptionLevel.ADVANCED:
                encrypted_content, iv = self._encrypt_advanced(data_bytes, encryption_key)
            else:  # MILITARY
                encrypted_content, iv = self._encrypt_military(data_bytes, encryption_key)

            # إنشاء معرف فريد للبيانات المشفرة
            data_id = f"enc_{encryption_level.value}_{secrets.token_hex(8)}"

            # إنشاء كائن البيانات المشفرة
            encrypted_data = EncryptedData(
                data_id=data_id,
                encrypted_content=encrypted_content,
                encryption_level=encryption_level,
                key_id=encryption_key.key_id,
                iv=iv,
                metadata={
                    "original_size": len(data_bytes),
                    "encrypted_size": len(encrypted_content),
                    "compression_ratio": len(encrypted_content) / len(data_bytes)
                }
            )

            # تحديث استخدام المفتاح
            encryption_key.usage_count += 1
            self._save_key(encryption_key)

            # تحديث الإحصائيات
            self.encryption_stats["total_encryptions"] += 1
            self.encryption_stats["last_operation"] = datetime.now().isoformat()

            self.encryption_logger.info(f"تم تشفير البيانات: {data_id} بالمفتاح: {encryption_key.key_id}")
            return encrypted_data

        except Exception as e:
            self.encryption_stats["failed_operations"] += 1
            self.logger.error(f"خطأ في تشفير البيانات: {str(e)}")
            raise

    def decrypt_data(self, encrypted_data: EncryptedData) -> Union[str, bytes, Dict[str, Any]]:
        """فك تشفير البيانات"""
        try:
            # تحميل مفتاح فك التشفير
            encryption_key = self._load_key(encrypted_data.key_id)
            if not encryption_key:
                raise ValueError(f"مفتاح فك التشفير غير موجود: {encrypted_data.key_id}")

            # فك التشفير حسب المستوى
            if encrypted_data.encryption_level == EncryptionLevel.BASIC:
                decrypted_bytes = self._decrypt_basic(encrypted_data.encrypted_content, encryption_key)
            elif encrypted_data.encryption_level == EncryptionLevel.STANDARD:
                decrypted_bytes = self._decrypt_standard(encrypted_data.encrypted_content, encrypted_data.iv, encryption_key)
            elif encrypted_data.encryption_level == EncryptionLevel.ADVANCED:
                decrypted_bytes = self._decrypt_advanced(encrypted_data.encrypted_content, encrypted_data.iv, encryption_key)
            else:  # MILITARY
                decrypted_bytes = self._decrypt_military(encrypted_data.encrypted_content, encrypted_data.iv, encryption_key)

            # تحديث الإحصائيات
            self.encryption_stats["total_decryptions"] += 1
            self.encryption_stats["last_operation"] = datetime.now().isoformat()

            self.encryption_logger.info(f"تم فك تشفير البيانات: {encrypted_data.data_id}")

            # محاولة تحويل البيانات إلى النوع الأصلي
            try:
                # محاولة تحويل إلى JSON
                return json.loads(decrypted_bytes.decode())
            except:
                try:
                    # محاولة تحويل إلى نص
                    return decrypted_bytes.decode()
                except:
                    # إرجاع البيانات الخام
                    return decrypted_bytes

        except Exception as e:
            self.encryption_stats["failed_operations"] += 1
            self.logger.error(f"خطأ في فك تشفير البيانات: {str(e)}")
            raise

    def _encrypt_basic(self, data: bytes, encryption_key: EncryptionKey) -> Tuple[bytes, Optional[bytes]]:
        """تشفير أساسي باستخدام Fernet"""
        try:
            fernet = Fernet(encryption_key.key_data)
            encrypted_data = fernet.encrypt(data)
            return encrypted_data, None  # Fernet لا يحتاج IV منفصل

        except Exception as e:
            self.logger.error(f"خطأ في التشفير الأساسي: {str(e)}")
            raise

    def _decrypt_basic(self, encrypted_data: bytes, encryption_key: EncryptionKey) -> bytes:
        """فك التشفير الأساسي"""
        try:
            fernet = Fernet(encryption_key.key_data)
            decrypted_data = fernet.decrypt(encrypted_data)
            return decrypted_data

        except Exception as e:
            self.logger.error(f"خطأ في فك التشفير الأساسي: {str(e)}")
            raise

    def _encrypt_standard(self, data: bytes, encryption_key: EncryptionKey) -> Tuple[bytes, bytes]:
        """تشفير قياسي باستخدام AES-256-CBC"""
        try:
            # إنتاج IV عشوائي
            iv = secrets.token_bytes(16)

            # إنشاء cipher
            cipher = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.CBC(iv),
                backend=default_backend()
            )

            # إضافة padding للبيانات
            padded_data = self._add_padding(data, 16)

            # تشفير البيانات
            encryptor = cipher.encryptor()
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

            return encrypted_data, iv

        except Exception as e:
            self.logger.error(f"خطأ في التشفير القياسي: {str(e)}")
            raise

    def _decrypt_standard(self, encrypted_data: bytes, iv: bytes, encryption_key: EncryptionKey) -> bytes:
        """فك التشفير القياسي"""
        try:
            # إنشاء cipher
            cipher = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.CBC(iv),
                backend=default_backend()
            )

            # فك التشفير
            decryptor = cipher.decryptor()
            padded_data = decryptor.update(encrypted_data) + decryptor.finalize()

            # إزالة padding
            decrypted_data = self._remove_padding(padded_data)

            return decrypted_data

        except Exception as e:
            self.logger.error(f"خطأ في فك التشفير القياسي: {str(e)}")
            raise

    def _encrypt_advanced(self, data: bytes, encryption_key: EncryptionKey) -> Tuple[bytes, bytes]:
        """تشفير متقدم باستخدام AES-256-GCM"""
        try:
            # إنتاج IV عشوائي
            iv = secrets.token_bytes(12)  # GCM يستخدم 12 bytes

            # إنشاء cipher
            cipher = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.GCM(iv),
                backend=default_backend()
            )

            # تشفير البيانات
            encryptor = cipher.encryptor()
            encrypted_data = encryptor.update(data) + encryptor.finalize()

            # إضافة authentication tag
            auth_tag = encryptor.tag
            encrypted_with_tag = encrypted_data + auth_tag

            return encrypted_with_tag, iv

        except Exception as e:
            self.logger.error(f"خطأ في التشفير المتقدم: {str(e)}")
            raise

    def _decrypt_advanced(self, encrypted_data: bytes, iv: bytes, encryption_key: EncryptionKey) -> bytes:
        """فك التشفير المتقدم"""
        try:
            # فصل البيانات المشفرة عن authentication tag
            auth_tag = encrypted_data[-16:]  # آخر 16 bytes
            encrypted_content = encrypted_data[:-16]

            # إنشاء cipher
            cipher = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.GCM(iv, auth_tag),
                backend=default_backend()
            )

            # فك التشفير
            decryptor = cipher.decryptor()
            decrypted_data = decryptor.update(encrypted_content) + decryptor.finalize()

            return decrypted_data

        except Exception as e:
            self.logger.error(f"خطأ في فك التشفير المتقدم: {str(e)}")
            raise

    def _encrypt_military(self, data: bytes, encryption_key: EncryptionKey) -> Tuple[bytes, bytes]:
        """تشفير عسكري متعدد الطبقات"""
        try:
            # الطبقة الأولى: ضغط البيانات
            import zlib
            compressed_data = zlib.compress(data, level=9)

            # الطبقة الثانية: تشفير AES-256-GCM
            iv1 = secrets.token_bytes(12)
            cipher1 = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.GCM(iv1),
                backend=default_backend()
            )
            encryptor1 = cipher1.encryptor()
            encrypted_layer1 = encryptor1.update(compressed_data) + encryptor1.finalize()
            auth_tag1 = encryptor1.tag

            # الطبقة الثالثة: مفتاح ثانوي مشتق
            secondary_key = hashlib.sha256(encryption_key.key_data + b"secondary").digest()
            iv2 = secrets.token_bytes(12)
            cipher2 = Cipher(
                algorithms.AES(secondary_key),
                modes.GCM(iv2),
                backend=default_backend()
            )
            encryptor2 = cipher2.encryptor()
            encrypted_layer2 = encryptor2.update(encrypted_layer1 + auth_tag1) + encryptor2.finalize()
            auth_tag2 = encryptor2.tag

            # الطبقة الرابعة: XOR مع مفتاح مشتق من الوقت
            time_key = hashlib.sha256(str(int(datetime.now().timestamp())).encode()).digest()
            xor_encrypted = bytes(a ^ b for a, b in zip(encrypted_layer2, time_key * (len(encrypted_layer2) // 32 + 1)))

            # تجميع جميع المعلومات المطلوبة لفك التشفير
            military_iv = iv1 + iv2 + auth_tag1 + auth_tag2
            final_encrypted = xor_encrypted

            return final_encrypted, military_iv

        except Exception as e:
            self.logger.error(f"خطأ في التشفير العسكري: {str(e)}")
            raise

    def _decrypt_military(self, encrypted_data: bytes, military_iv: bytes, encryption_key: EncryptionKey) -> bytes:
        """فك التشفير العسكري"""
        try:
            # استخراج المعلومات من military_iv
            iv1 = military_iv[:12]
            iv2 = military_iv[12:24]
            auth_tag1 = military_iv[24:40]
            auth_tag2 = military_iv[40:56]

            # الطبقة الأولى: فك XOR
            time_key = hashlib.sha256(str(int(datetime.now().timestamp())).encode()).digest()
            xor_decrypted = bytes(a ^ b for a, b in zip(encrypted_data, time_key * (len(encrypted_data) // 32 + 1)))

            # الطبقة الثانية: فك التشفير الثانوي
            secondary_key = hashlib.sha256(encryption_key.key_data + b"secondary").digest()
            cipher2 = Cipher(
                algorithms.AES(secondary_key),
                modes.GCM(iv2, auth_tag2),
                backend=default_backend()
            )
            decryptor2 = cipher2.decryptor()
            decrypted_layer2 = decryptor2.update(xor_decrypted) + decryptor2.finalize()

            # فصل البيانات عن auth_tag1
            encrypted_layer1 = decrypted_layer2[:-16]

            # الطبقة الثالثة: فك التشفير الأساسي
            cipher1 = Cipher(
                algorithms.AES(encryption_key.key_data),
                modes.GCM(iv1, auth_tag1),
                backend=default_backend()
            )
            decryptor1 = cipher1.decryptor()
            compressed_data = decryptor1.update(encrypted_layer1) + decryptor1.finalize()

            # الطبقة الرابعة: فك الضغط
            import zlib
            original_data = zlib.decompress(compressed_data)

            return original_data

        except Exception as e:
            self.logger.error(f"خطأ في فك التشفير العسكري: {str(e)}")
            raise

    def _add_padding(self, data: bytes, block_size: int) -> bytes:
        """إضافة padding للبيانات"""
        padding_length = block_size - (len(data) % block_size)
        padding = bytes([padding_length] * padding_length)
        return data + padding

    def _remove_padding(self, padded_data: bytes) -> bytes:
        """إزالة padding من البيانات"""
        padding_length = padded_data[-1]
        return padded_data[:-padding_length]

    def encrypt_file(self, file_path: Union[str, Path],
                    encryption_level: EncryptionLevel = None,
                    output_path: Optional[Union[str, Path]] = None) -> str:
        """تشفير ملف كامل"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")

            # قراءة الملف
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # تشفير البيانات
            encrypted_data = self.encrypt_data(file_data, encryption_level)

            # تحديد مسار الإخراج
            if output_path is None:
                output_path = file_path.with_suffix(file_path.suffix + '.enc')
            else:
                output_path = Path(output_path)

            # حفظ البيانات المشفرة
            encrypted_file_data = {
                "data_id": encrypted_data.data_id,
                "encrypted_content": base64.b64encode(encrypted_data.encrypted_content).decode(),
                "encryption_level": encrypted_data.encryption_level.value,
                "key_id": encrypted_data.key_id,
                "iv": base64.b64encode(encrypted_data.iv).decode() if encrypted_data.iv else None,
                "metadata": encrypted_data.metadata,
                "created_at": encrypted_data.created_at.isoformat(),
                "original_filename": file_path.name
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تشفير الملف: {file_path} -> {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"خطأ في تشفير الملف: {str(e)}")
            raise

    def decrypt_file(self, encrypted_file_path: Union[str, Path],
                    output_path: Optional[Union[str, Path]] = None) -> str:
        """فك تشفير ملف"""
        try:
            encrypted_file_path = Path(encrypted_file_path)
            if not encrypted_file_path.exists():
                raise FileNotFoundError(f"الملف المشفر غير موجود: {encrypted_file_path}")

            # قراءة البيانات المشفرة
            with open(encrypted_file_path, 'r', encoding='utf-8') as f:
                encrypted_file_data = json.load(f)

            # إعادة بناء كائن البيانات المشفرة
            encrypted_data = EncryptedData(
                data_id=encrypted_file_data["data_id"],
                encrypted_content=base64.b64decode(encrypted_file_data["encrypted_content"]),
                encryption_level=EncryptionLevel(encrypted_file_data["encryption_level"]),
                key_id=encrypted_file_data["key_id"],
                iv=base64.b64decode(encrypted_file_data["iv"]) if encrypted_file_data.get("iv") else None,
                metadata=encrypted_file_data.get("metadata", {}),
                created_at=datetime.fromisoformat(encrypted_file_data["created_at"])
            )

            # فك تشفير البيانات
            decrypted_data = self.decrypt_data(encrypted_data)

            # تحديد مسار الإخراج
            if output_path is None:
                original_filename = encrypted_file_data.get("original_filename", "decrypted_file")
                output_path = encrypted_file_path.parent / original_filename
            else:
                output_path = Path(output_path)

            # حفظ البيانات المفكوكة التشفير
            with open(output_path, 'wb') as f:
                if isinstance(decrypted_data, bytes):
                    f.write(decrypted_data)
                else:
                    f.write(str(decrypted_data).encode())

            self.logger.info(f"تم فك تشفير الملف: {encrypted_file_path} -> {output_path}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير الملف: {str(e)}")
            raise

    def rotate_keys(self, force_rotation: bool = False) -> List[str]:
        """تدوير المفاتيح المنتهية الصلاحية"""
        try:
            rotated_keys = []

            for key_id, key_info in self.keys_index.items():
                # تحميل المفتاح
                encryption_key = self._load_key(key_id)
                if not encryption_key:
                    continue

                # التحقق من الحاجة للتدوير
                needs_rotation = force_rotation

                if not needs_rotation:
                    # التحقق من انتهاء الصلاحية
                    if encryption_key.expires_at and datetime.now() > encryption_key.expires_at:
                        needs_rotation = True

                    # التحقق من عدد الاستخدامات
                    if encryption_key.max_usage and encryption_key.usage_count >= encryption_key.max_usage:
                        needs_rotation = True

                if needs_rotation:
                    # إنشاء مفتاح جديد
                    new_key = self.generate_key(
                        encryption_key.key_type,
                        encryption_key.encryption_level,
                        expires_in_days=self.encryption_settings["key_rotation_days"],
                        max_usage=self.encryption_settings["max_key_usage"]
                    )

                    rotated_keys.append(f"{key_id} -> {new_key.key_id}")

                    # حذف المفتاح القديم (اختياري)
                    if self.encryption_settings.get("auto_cleanup", True):
                        self._delete_key(key_id)

            if rotated_keys:
                self.logger.info(f"تم تدوير {len(rotated_keys)} مفتاح")

            return rotated_keys

        except Exception as e:
            self.logger.error(f"خطأ في تدوير المفاتيح: {str(e)}")
            return []

    def _delete_key(self, key_id: str):
        """حذف مفتاح"""
        try:
            # حذف من الذاكرة المؤقتة
            if key_id in self.keys_cache:
                del self.keys_cache[key_id]

            # حذف من الفهرس
            if key_id in self.keys_index:
                key_info = self.keys_index[key_id]
                key_file = Path(key_info["file_path"])

                # حذف الملف
                if key_file.exists():
                    key_file.unlink()

                # حذف من الفهرس
                del self.keys_index[key_id]

                # حفظ الفهرس المحدث
                with open(self.keys_index_file, 'w', encoding='utf-8') as f:
                    json.dump(self.keys_index, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم حذف المفتاح: {key_id}")

        except Exception as e:
            self.logger.error(f"خطأ في حذف المفتاح {key_id}: {str(e)}")

    def get_encryption_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التشفير"""
        try:
            stats = self.encryption_stats.copy()

            # إضافة معلومات المفاتيح
            stats["total_keys"] = len(self.keys_index)
            stats["cached_keys"] = len(self.keys_cache)
            stats["session_keys"] = len(self.session_keys)

            # إحصائيات المفاتيح حسب النوع
            key_types = {}
            encryption_levels = {}

            for key_info in self.keys_index.values():
                key_type = key_info["key_type"]
                encryption_level = key_info["encryption_level"]

                key_types[key_type] = key_types.get(key_type, 0) + 1
                encryption_levels[encryption_level] = encryption_levels.get(encryption_level, 0) + 1

            stats["key_types_distribution"] = key_types
            stats["encryption_levels_distribution"] = encryption_levels

            # معلومات النظام
            stats["system_info"] = {
                "security_dir": str(self.security_dir),
                "keys_dir": str(self.keys_dir),
                "master_key_exists": bool(self.master_key),
                "encryption_settings": self.encryption_settings
            }

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات التشفير: {str(e)}")
            return {}

    def cleanup_expired_keys(self) -> int:
        """تنظيف المفاتيح المنتهية الصلاحية"""
        try:
            cleaned_count = 0
            expired_keys = []

            for key_id, key_info in self.keys_index.items():
                if key_info.get("expires_at"):
                    expires_at = datetime.fromisoformat(key_info["expires_at"])
                    if datetime.now() > expires_at:
                        expired_keys.append(key_id)

            for key_id in expired_keys:
                self._delete_key(key_id)
                cleaned_count += 1

            if cleaned_count > 0:
                self.logger.info(f"تم تنظيف {cleaned_count} مفتاح منتهي الصلاحية")

            return cleaned_count

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المفاتيح المنتهية الصلاحية: {str(e)}")
            return 0

    def backup_encryption_system(self, backup_path: Union[str, Path]) -> bool:
        """إنشاء نسخة احتياطية من نظام التشفير"""
        try:
            backup_path = Path(backup_path)
            backup_path.mkdir(parents=True, exist_ok=True)

            # نسخ فهرس المفاتيح
            import shutil
            shutil.copy2(self.keys_index_file, backup_path / "keys_index.json")

            # نسخ المفاتيح
            keys_backup_dir = backup_path / "keys"
            keys_backup_dir.mkdir(exist_ok=True)

            for key_file in self.keys_dir.glob("*.key"):
                shutil.copy2(key_file, keys_backup_dir / key_file.name)

            # نسخ ملف المفتاح الرئيسي
            if self.master_key_file.exists():
                shutil.copy2(self.master_key_file, backup_path / "master.key")

            # نسخ سجل التشفير
            if self.encryption_log_file.exists():
                shutil.copy2(self.encryption_log_file, backup_path / "encryption.log")

            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                "backup_date": datetime.now().isoformat(),
                "total_keys": len(self.keys_index),
                "encryption_stats": self.encryption_stats,
                "system_version": "1.0.0"
            }

            with open(backup_path / "backup_info.json", 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم إنشاء نسخة احتياطية في: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return False
