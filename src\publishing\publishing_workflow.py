#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سير عمل النشر - Publishing Workflow
يدمج جميع مكونات النشر في workflow واحد
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

from .tiktok_publisher import TikTokPublisher, TikTokPost
from .content_optimizer import ContentOptimizer, OptimizationResult
from .publishing_scheduler import PublishingScheduler, ScheduledPost
from .hashtag_generator import HashtagGenerator, HashtagSet
from .description_generator import DescriptionGenerator, PostDescription

class PublishingStatus(Enum):
    """حالة النشر"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    OPTIMIZING = "optimizing"
    GENERATING_CONTENT = "generating_content"
    SCHEDULING = "scheduling"
    PUBLISHING = "publishing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class PublishingJob:
    """مهمة النشر"""
    job_id: str
    video_path: str
    content_analysis: Dict[str, Any]
    platform: str
    publish_immediately: bool
    custom_settings: Optional[Dict[str, Any]]
    status: PublishingStatus
    created_at: datetime
    updated_at: datetime
    
    # نتائج المعالجة
    optimization_result: Optional[OptimizationResult] = None
    hashtags: Optional[HashtagSet] = None
    description: Optional[PostDescription] = None
    scheduled_post: Optional[ScheduledPost] = None
    published_post: Optional[TikTokPost] = None
    
    # معلومات الخطأ
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "job_id": self.job_id,
            "video_path": self.video_path,
            "platform": self.platform,
            "publish_immediately": self.publish_immediately,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "optimization_result": self.optimization_result.to_dict() if self.optimization_result else None,
            "hashtags": self.hashtags.to_dict() if self.hashtags else None,
            "description": self.description.to_dict() if self.description else None,
            "scheduled_post": self.scheduled_post.to_dict() if self.scheduled_post else None,
            "published_post": self.published_post.to_dict() if self.published_post else None,
            "error_message": self.error_message,
            "retry_count": self.retry_count
        }

class PublishingWorkflow:
    """سير عمل النشر الشامل"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تهيئة المكونات
        self.content_optimizer = ContentOptimizer(config_manager)
        self.hashtag_generator = HashtagGenerator(config_manager)
        self.description_generator = DescriptionGenerator(config_manager)
        self.publishing_scheduler = PublishingScheduler(config_manager)
        self.tiktok_publisher = TikTokPublisher(config_manager)
        
        # إعدادات سير العمل
        self.workflow_settings = self._load_workflow_settings()
        
        # قائمة المهام النشطة
        self.active_jobs: Dict[str, PublishingJob] = {}
        
        # إحصائيات سير العمل
        self.workflow_stats = {
            "total_jobs_processed": 0,
            "successful_publications": 0,
            "failed_publications": 0,
            "average_processing_time": 0,
            "last_updated": datetime.now()
        }
    
    def _load_workflow_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات سير العمل"""
        try:
            return self.config_manager.get_setting("publishing_settings", "workflow", {
                "max_concurrent_jobs": 3,
                "auto_retry_failed": True,
                "max_retry_attempts": 3,
                "retry_delay_minutes": 30,
                "auto_optimize_content": True,
                "auto_generate_hashtags": True,
                "auto_generate_description": True,
                "auto_schedule_optimal_time": True,
                "quality_threshold": 70.0,
                "engagement_threshold": 60.0,
                "enable_notifications": True,
                "backup_original_files": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات سير العمل: {str(e)}")
            return {"max_concurrent_jobs": 3, "auto_retry_failed": True}
    
    async def submit_publishing_job(self, video_path: str, content_analysis: Dict[str, Any],
                                  platform: str = "tiktok", publish_immediately: bool = False,
                                  custom_settings: Optional[Dict[str, Any]] = None) -> str:
        """إرسال مهمة نشر جديدة"""
        try:
            # إنشاء معرف فريد للمهمة
            job_id = f"job_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.active_jobs)}"
            
            # إنشاء مهمة النشر
            job = PublishingJob(
                job_id=job_id,
                video_path=video_path,
                content_analysis=content_analysis,
                platform=platform,
                publish_immediately=publish_immediately,
                custom_settings=custom_settings or {},
                status=PublishingStatus.PENDING,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # إضافة المهمة للقائمة النشطة
            self.active_jobs[job_id] = job
            
            self.logger.info(f"تم إرسال مهمة نشر جديدة: {job_id}")
            
            # بدء معالجة المهمة
            asyncio.create_task(self._process_publishing_job(job_id))
            
            return job_id
            
        except Exception as e:
            self.logger.error(f"خطأ في إرسال مهمة النشر: {str(e)}")
            raise
    
    async def _process_publishing_job(self, job_id: str):
        """معالجة مهمة النشر"""
        try:
            job = self.active_jobs.get(job_id)
            if not job:
                self.logger.error(f"مهمة غير موجودة: {job_id}")
                return
            
            start_time = datetime.now()
            
            self.logger.info(f"بدء معالجة مهمة النشر: {job_id}")
            
            # الخطوة 1: تحليل وتحسين المحتوى
            await self._step_optimize_content(job)
            
            # الخطوة 2: إنشاء الهاشتاغات
            await self._step_generate_hashtags(job)
            
            # الخطوة 3: إنشاء الوصف
            await self._step_generate_description(job)
            
            # الخطوة 4: جدولة أو نشر فوري
            if job.publish_immediately:
                await self._step_publish_immediately(job)
            else:
                await self._step_schedule_publication(job)
            
            # تحديث الإحصائيات
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_workflow_stats(job, processing_time, success=True)
            
            self.logger.info(f"تم إكمال معالجة مهمة النشر: {job_id}")
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة مهمة النشر {job_id}: {str(e)}")
            await self._handle_job_error(job_id, str(e))
    
    async def _step_optimize_content(self, job: PublishingJob):
        """خطوة تحسين المحتوى"""
        try:
            job.status = PublishingStatus.OPTIMIZING
            job.updated_at = datetime.now()
            
            if self.workflow_settings.get("auto_optimize_content", True):
                self.logger.info(f"تحسين المحتوى للمهمة: {job.job_id}")
                
                # تحسين المحتوى
                optimization_result = await asyncio.to_thread(
                    self.content_optimizer.optimize_content,
                    job.video_path,
                    job.platform,
                    job.content_analysis
                )
                
                job.optimization_result = optimization_result
                
                # التحقق من جودة المحتوى
                quality_threshold = self.workflow_settings.get("quality_threshold", 70.0)
                if optimization_result.quality_score < quality_threshold:
                    self.logger.warning(f"جودة المحتوى أقل من المطلوب: {optimization_result.quality_score}")
                
                self.logger.info(f"تم تحسين المحتوى بنجاح - النقاط: {optimization_result.quality_score}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحسين المحتوى: {str(e)}")
            raise
    
    async def _step_generate_hashtags(self, job: PublishingJob):
        """خطوة إنشاء الهاشتاغات"""
        try:
            job.status = PublishingStatus.GENERATING_CONTENT
            job.updated_at = datetime.now()
            
            if self.workflow_settings.get("auto_generate_hashtags", True):
                self.logger.info(f"إنشاء الهاشتاغات للمهمة: {job.job_id}")
                
                # إنشاء الهاشتاغات
                hashtags = await asyncio.to_thread(
                    self.hashtag_generator.generate_hashtags,
                    job.content_analysis,
                    job.platform
                )
                
                job.hashtags = hashtags
                
                self.logger.info(f"تم إنشاء {len(hashtags.hashtags)} هاشتاغ")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الهاشتاغات: {str(e)}")
            raise
    
    async def _step_generate_description(self, job: PublishingJob):
        """خطوة إنشاء الوصف"""
        try:
            if self.workflow_settings.get("auto_generate_description", True):
                self.logger.info(f"إنشاء الوصف للمهمة: {job.job_id}")
                
                # إنشاء الوصف
                hashtag_list = job.hashtags.hashtags if job.hashtags else []
                description = await asyncio.to_thread(
                    self.description_generator.generate_description,
                    job.content_analysis,
                    job.platform,
                    hashtag_list
                )
                
                job.description = description
                
                self.logger.info(f"تم إنشاء وصف بطول {description.character_count} حرف")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الوصف: {str(e)}")
            raise
    
    async def _step_schedule_publication(self, job: PublishingJob):
        """خطوة جدولة النشر"""
        try:
            job.status = PublishingStatus.SCHEDULING
            job.updated_at = datetime.now()
            
            self.logger.info(f"جدولة النشر للمهمة: {job.job_id}")
            
            # تحديد وقت النشر الأمثل
            if self.workflow_settings.get("auto_schedule_optimal_time", True):
                optimal_time = await asyncio.to_thread(
                    self.publishing_scheduler.calculate_optimal_posting_time,
                    job.platform
                )
            else:
                # نشر خلال ساعة
                optimal_time = datetime.now() + timedelta(hours=1)
            
            # إنشاء منشور مجدول
            scheduled_post = await asyncio.to_thread(
                self.publishing_scheduler.schedule_post,
                job.video_path,
                job.description.emoji_enhanced if job.description else "محتوى رائع",
                job.hashtags.hashtags if job.hashtags else [],
                job.platform,
                optimal_time
            )
            
            job.scheduled_post = scheduled_post
            job.status = PublishingStatus.COMPLETED
            job.updated_at = datetime.now()
            
            self.logger.info(f"تم جدولة النشر في: {optimal_time}")
            
        except Exception as e:
            self.logger.error(f"خطأ في جدولة النشر: {str(e)}")
            raise
    
    async def _step_publish_immediately(self, job: PublishingJob):
        """خطوة النشر الفوري"""
        try:
            job.status = PublishingStatus.PUBLISHING
            job.updated_at = datetime.now()
            
            self.logger.info(f"النشر الفوري للمهمة: {job.job_id}")
            
            # إنشاء منشور TikTok
            tiktok_post = TikTokPost(
                video_path=job.optimization_result.optimized_video_path if job.optimization_result else job.video_path,
                description=job.description.emoji_enhanced if job.description else "محتوى رائع",
                hashtags=job.hashtags.hashtags if job.hashtags else [],
                privacy_level="public",
                allow_comments=True,
                allow_duet=True,
                allow_stitch=True
            )
            
            # نشر الفيديو
            published_post = await asyncio.to_thread(
                self.tiktok_publisher.publish_video,
                tiktok_post
            )
            
            job.published_post = published_post
            job.status = PublishingStatus.COMPLETED
            job.updated_at = datetime.now()
            
            self.logger.info(f"تم النشر بنجاح - معرف المنشور: {published_post.post_id}")
            
        except Exception as e:
            self.logger.error(f"خطأ في النشر الفوري: {str(e)}")
            raise

    async def _handle_job_error(self, job_id: str, error_message: str):
        """معالجة خطأ في المهمة"""
        try:
            job = self.active_jobs.get(job_id)
            if not job:
                return

            job.error_message = error_message
            job.retry_count += 1
            job.status = PublishingStatus.FAILED
            job.updated_at = datetime.now()

            # تحديث الإحصائيات
            self._update_workflow_stats(job, 0, success=False)

            # إعادة المحاولة إذا كانت مفعلة
            if (self.workflow_settings.get("auto_retry_failed", True) and
                job.retry_count < self.workflow_settings.get("max_retry_attempts", 3)):

                retry_delay = self.workflow_settings.get("retry_delay_minutes", 30)
                self.logger.info(f"إعادة جدولة المهمة {job_id} بعد {retry_delay} دقيقة")

                # إعادة جدولة المهمة
                await asyncio.sleep(retry_delay * 60)
                job.status = PublishingStatus.PENDING
                asyncio.create_task(self._process_publishing_job(job_id))
            else:
                self.logger.error(f"فشل نهائي في المهمة {job_id}: {error_message}")

        except Exception as e:
            self.logger.error(f"خطأ في معالجة خطأ المهمة: {str(e)}")

    def _update_workflow_stats(self, job: PublishingJob, processing_time: float, success: bool):
        """تحديث إحصائيات سير العمل"""
        try:
            self.workflow_stats["total_jobs_processed"] += 1

            if success:
                self.workflow_stats["successful_publications"] += 1
            else:
                self.workflow_stats["failed_publications"] += 1

            # تحديث متوسط وقت المعالجة
            current_avg = self.workflow_stats["average_processing_time"]
            total_jobs = self.workflow_stats["total_jobs_processed"]

            if total_jobs > 1:
                self.workflow_stats["average_processing_time"] = (
                    (current_avg * (total_jobs - 1) + processing_time) / total_jobs
                )
            else:
                self.workflow_stats["average_processing_time"] = processing_time

            self.workflow_stats["last_updated"] = datetime.now()

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات سير العمل: {str(e)}")

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على حالة المهمة"""
        try:
            job = self.active_jobs.get(job_id)
            if job:
                return job.to_dict()
            return None

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حالة المهمة: {str(e)}")
            return None

    def get_all_jobs(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المهام"""
        try:
            return [job.to_dict() for job in self.active_jobs.values()]

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على جميع المهام: {str(e)}")
            return []

    def cancel_job(self, job_id: str) -> bool:
        """إلغاء مهمة"""
        try:
            job = self.active_jobs.get(job_id)
            if job and job.status in [PublishingStatus.PENDING, PublishingStatus.ANALYZING,
                                    PublishingStatus.OPTIMIZING, PublishingStatus.GENERATING_CONTENT]:
                job.status = PublishingStatus.CANCELLED
                job.updated_at = datetime.now()

                self.logger.info(f"تم إلغاء المهمة: {job_id}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"خطأ في إلغاء المهمة: {str(e)}")
            return False

    def cleanup_completed_jobs(self, older_than_hours: int = 24):
        """تنظيف المهام المكتملة"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            jobs_to_remove = []

            for job_id, job in self.active_jobs.items():
                if (job.status in [PublishingStatus.COMPLETED, PublishingStatus.FAILED,
                                 PublishingStatus.CANCELLED] and
                    job.updated_at < cutoff_time):
                    jobs_to_remove.append(job_id)

            for job_id in jobs_to_remove:
                del self.active_jobs[job_id]

            self.logger.info(f"تم تنظيف {len(jobs_to_remove)} مهمة مكتملة")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المهام: {str(e)}")

    def get_workflow_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات سير العمل"""
        try:
            # إحصائيات أساسية
            stats = self.workflow_stats.copy()

            # إحصائيات المهام النشطة
            active_stats = {
                "total_active_jobs": len(self.active_jobs),
                "pending_jobs": len([j for j in self.active_jobs.values() if j.status == PublishingStatus.PENDING]),
                "processing_jobs": len([j for j in self.active_jobs.values() if j.status in [
                    PublishingStatus.ANALYZING, PublishingStatus.OPTIMIZING,
                    PublishingStatus.GENERATING_CONTENT, PublishingStatus.SCHEDULING,
                    PublishingStatus.PUBLISHING
                ]]),
                "completed_jobs": len([j for j in self.active_jobs.values() if j.status == PublishingStatus.COMPLETED]),
                "failed_jobs": len([j for j in self.active_jobs.values() if j.status == PublishingStatus.FAILED])
            }

            # معدل النجاح
            total_processed = stats["total_jobs_processed"]
            if total_processed > 0:
                stats["success_rate"] = (stats["successful_publications"] / total_processed) * 100
            else:
                stats["success_rate"] = 0

            # دمج الإحصائيات
            stats.update(active_stats)

            # إحصائيات المكونات
            stats["component_stats"] = {
                "content_optimizer": self.content_optimizer.get_optimizer_stats(),
                "hashtag_generator": self.hashtag_generator.get_generator_stats(),
                "description_generator": self.description_generator.get_generator_statistics(),
                "publishing_scheduler": self.publishing_scheduler.get_scheduler_stats()
            }

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات سير العمل: {str(e)}")
            return {}

    async def batch_publish(self, video_paths: List[str], content_analyses: List[Dict[str, Any]],
                          platform: str = "tiktok", publish_immediately: bool = False,
                          custom_settings: Optional[Dict[str, Any]] = None) -> List[str]:
        """نشر مجموعة من الفيديوهات"""
        try:
            if len(video_paths) != len(content_analyses):
                raise ValueError("عدد الفيديوهات يجب أن يساوي عدد التحليلات")

            job_ids = []
            max_concurrent = self.workflow_settings.get("max_concurrent_jobs", 3)

            # تقسيم المهام إلى مجموعات
            for i in range(0, len(video_paths), max_concurrent):
                batch = list(zip(
                    video_paths[i:i+max_concurrent],
                    content_analyses[i:i+max_concurrent]
                ))

                # إرسال مجموعة المهام
                batch_job_ids = []
                for video_path, content_analysis in batch:
                    job_id = await self.submit_publishing_job(
                        video_path, content_analysis, platform,
                        publish_immediately, custom_settings
                    )
                    batch_job_ids.append(job_id)

                job_ids.extend(batch_job_ids)

                # انتظار إكمال المجموعة قبل البدء في التالية
                if i + max_concurrent < len(video_paths):
                    await self._wait_for_jobs_completion(batch_job_ids)

            self.logger.info(f"تم إرسال {len(job_ids)} مهمة نشر مجمعة")
            return job_ids

        except Exception as e:
            self.logger.error(f"خطأ في النشر المجمع: {str(e)}")
            raise

    async def _wait_for_jobs_completion(self, job_ids: List[str], timeout_minutes: int = 30):
        """انتظار إكمال مجموعة من المهام"""
        try:
            timeout = datetime.now() + timedelta(minutes=timeout_minutes)

            while datetime.now() < timeout:
                all_completed = True

                for job_id in job_ids:
                    job = self.active_jobs.get(job_id)
                    if job and job.status not in [PublishingStatus.COMPLETED,
                                                 PublishingStatus.FAILED,
                                                 PublishingStatus.CANCELLED]:
                        all_completed = False
                        break

                if all_completed:
                    break

                await asyncio.sleep(10)  # فحص كل 10 ثوان

        except Exception as e:
            self.logger.error(f"خطأ في انتظار إكمال المهام: {str(e)}")

    def update_workflow_settings(self, new_settings: Dict[str, Any]):
        """تحديث إعدادات سير العمل"""
        try:
            self.workflow_settings.update(new_settings)

            # حفظ الإعدادات
            self.config_manager.set_setting(
                "publishing_settings", "workflow", self.workflow_settings
            )

            self.logger.info("تم تحديث إعدادات سير العمل")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات سير العمل: {str(e)}")

    async def shutdown(self):
        """إيقاف سير العمل"""
        try:
            self.logger.info("بدء إيقاف سير العمل...")

            # إلغاء جميع المهام المعلقة
            for job_id, job in self.active_jobs.items():
                if job.status == PublishingStatus.PENDING:
                    self.cancel_job(job_id)

            # انتظار إكمال المهام النشطة
            active_job_ids = [
                job_id for job_id, job in self.active_jobs.items()
                if job.status in [PublishingStatus.ANALYZING, PublishingStatus.OPTIMIZING,
                                PublishingStatus.GENERATING_CONTENT, PublishingStatus.SCHEDULING,
                                PublishingStatus.PUBLISHING]
            ]

            if active_job_ids:
                self.logger.info(f"انتظار إكمال {len(active_job_ids)} مهمة نشطة...")
                await self._wait_for_jobs_completion(active_job_ids, timeout_minutes=5)

            # إيقاف المكونات
            await asyncio.to_thread(self.publishing_scheduler.stop_scheduler)

            self.logger.info("تم إيقاف سير العمل بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف سير العمل: {str(e)}")

    def export_workflow_data(self, file_path: str):
        """تصدير بيانات سير العمل"""
        try:
            import json

            export_data = {
                "workflow_stats": self.workflow_stats,
                "workflow_settings": self.workflow_settings,
                "active_jobs": [job.to_dict() for job in self.active_jobs.values()],
                "export_date": datetime.now().isoformat()
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير بيانات سير العمل إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير بيانات سير العمل: {str(e)}")
