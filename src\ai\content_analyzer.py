#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل المحتوى الرئيسي - Content Analyzer
ينسق بين جميع أدوات التحليل الذكي ويقدم تحليلاً شاملاً للمحتوى
"""

import logging
import os
import json
import gc
import time
import weakref
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache
import multiprocessing as mp

# محاولة استيراد مكتبات التحسين الاختيارية
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import asyncio
    import aiofiles
    ASYNC_AVAILABLE = True
except ImportError:
    ASYNC_AVAILABLE = False

from .audio_analyzer import AudioAnalyzer
from .video_analyzer import VideoAnalyzer
from .clip_extractor import ClipExtractor
from .viral_predictor import ViralPredictor
from ..content.base_fetcher import ContentItem
from ..core.logger import activity_logger

class AnalysisResult:
    """نتيجة تحليل المحتوى"""
    
    def __init__(self, content_item: ContentItem):
        self.content_item = content_item
        self.timestamp = datetime.now()
        
        # نتائج التحليل
        self.audio_analysis = {}
        self.video_analysis = {}
        self.extracted_clips = []
        self.viral_score = 0.0
        self.viral_factors = {}
        
        # تقييم شامل
        self.overall_score = 0.0
        self.recommendation = ""
        self.tags = []
        self.best_moments = []
        
        # معلومات إضافية
        self.processing_time = 0.0
        self.errors = []
        self.warnings = []
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل النتيجة إلى قاموس"""
        return {
            "content_id": self.content_item.id,
            "content_url": self.content_item.url,
            "platform": self.content_item.platform,
            "timestamp": self.timestamp.isoformat(),
            "audio_analysis": self.audio_analysis,
            "video_analysis": self.video_analysis,
            "extracted_clips": [clip.to_dict() if hasattr(clip, 'to_dict') else clip for clip in self.extracted_clips],
            "viral_score": self.viral_score,
            "viral_factors": self.viral_factors,
            "overall_score": self.overall_score,
            "recommendation": self.recommendation,
            "tags": self.tags,
            "best_moments": self.best_moments,
            "processing_time": self.processing_time,
            "errors": self.errors,
            "warnings": self.warnings
        }

class ContentAnalyzer:
    """محلل المحتوى الرئيسي"""
    
    def __init__(self, config_manager, security_manager):
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)
        
        # إنشاء محللات فرعية
        self.audio_analyzer = AudioAnalyzer(config_manager)
        self.video_analyzer = VideoAnalyzer(config_manager)
        self.clip_extractor = ClipExtractor(config_manager)
        self.viral_predictor = ViralPredictor(config_manager)
        
        # إعدادات التحليل
        self.analysis_settings = self._load_analysis_settings()
        
        # مجلد حفظ النتائج
        self.results_dir = Path("data/analysis_results")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # إحصائيات
        self.stats = {
            "total_analyzed": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "average_processing_time": 0.0,
            "last_analysis": None
        }
        
        # خيط التحليل
        self.analysis_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="ContentAnalysis")

        # تحسينات الأداء
        self.analysis_cache = {}  # تخزين مؤقت للنتائج
        self.cache_ttl = 3600  # ساعة واحدة
        self.model_cache = {}  # تخزين مؤقت للنماذج
        self.batch_queue = []  # قائمة انتظار المعالجة الدفعية
        self.batch_size = 3  # حجم الدفعة
        self.batch_timeout = 10  # مهلة الدفعة بالثواني

        # مراقبة الأداء
        self.performance_stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "batch_processed": 0,
            "memory_usage": 0,
            "processing_times": [],
            "gpu_usage": 0 if not PSUTIL_AVAILABLE else 0
        }

        # معالجات الأحداث (weak references لمنع تسريب الذاكرة)
        self.event_handlers = weakref.WeakSet()

        # إعدادات التحسين
        self.optimization_settings = {
            "enable_caching": True,
            "enable_batch_processing": True,
            "enable_gpu_acceleration": True,
            "max_memory_usage_mb": 2048,
            "cleanup_interval": 300  # 5 دقائق
        }

        # بدء خيط التنظيف
        self._start_cleanup_thread()
    
    def _load_analysis_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات التحليل"""
        try:
            return self.config_manager.get_setting("ai_settings", "analysis", {
                "enable_audio_analysis": True,
                "enable_video_analysis": True,
                "enable_clip_extraction": True,
                "enable_viral_prediction": True,
                "min_clip_duration": 5,  # ثواني
                "max_clip_duration": 60,  # ثواني
                "quality_threshold": 0.6,  # عتبة الجودة
                "viral_threshold": 0.7,  # عتبة الانتشار
                "max_clips_per_content": 5,
                "parallel_processing": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات التحليل: {str(e)}")
            return {
                "enable_audio_analysis": True,
                "enable_video_analysis": True,
                "enable_clip_extraction": True,
                "enable_viral_prediction": True,
                "min_clip_duration": 5,
                "max_clip_duration": 60,
                "quality_threshold": 0.6,
                "viral_threshold": 0.7,
                "max_clips_per_content": 5,
                "parallel_processing": True
            }
    
    def analyze_content(self, content_item: ContentItem, 
                       download_path: str = None) -> AnalysisResult:
        """تحليل محتوى معين"""
        start_time = datetime.now()
        result = AnalysisResult(content_item)
        
        try:
            self.logger.info(f"بدء تحليل المحتوى: {content_item.title}")
            
            # التحقق من وجود الملف
            if not download_path or not os.path.exists(download_path):
                result.errors.append("ملف المحتوى غير موجود")
                return result
            
            # التحقق من الأمان
            if not self.security_manager.validate_file_safety(download_path):
                result.errors.append("فشل في التحقق من أمان الملف")
                return result
            
            # تحديد نوع الملف
            file_type = self._detect_file_type(download_path)
            if not file_type:
                result.errors.append("نوع ملف غير مدعوم")
                return result
            
            # تشغيل التحليلات المختلفة
            if self.analysis_settings.get("parallel_processing", True):
                self._run_parallel_analysis(result, download_path, file_type)
            else:
                self._run_sequential_analysis(result, download_path, file_type)
            
            # حساب التقييم الشامل
            self._calculate_overall_score(result)
            
            # إنشاء التوصيات
            self._generate_recommendations(result)
            
            # حفظ النتائج
            self._save_analysis_result(result)
            
            # تحديث الإحصائيات
            self.stats["successful_analyses"] += 1
            activity_logger.log_content_analyzed(content_item.platform, True)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المحتوى: {str(e)}")
            result.errors.append(str(e))
            self.stats["failed_analyses"] += 1
            activity_logger.log_content_analyzed(content_item.platform, False)
        
        finally:
            # حساب وقت المعالجة
            processing_time = (datetime.now() - start_time).total_seconds()
            result.processing_time = processing_time
            
            # تحديث الإحصائيات
            self.stats["total_analyzed"] += 1
            self.stats["last_analysis"] = datetime.now()
            self._update_average_processing_time(processing_time)
            
            self.logger.info(f"انتهى تحليل المحتوى في {processing_time:.2f} ثانية")
        
        return result
    
    def _detect_file_type(self, file_path: str) -> Optional[str]:
        """تحديد نوع الملف"""
        try:
            file_extension = Path(file_path).suffix.lower()
            
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
            audio_extensions = ['.mp3', '.wav', '.aac', '.ogg', '.m4a']
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
            
            if file_extension in video_extensions:
                return "video"
            elif file_extension in audio_extensions:
                return "audio"
            elif file_extension in image_extensions:
                return "image"
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديد نوع الملف: {str(e)}")
            return None
    
    def _run_parallel_analysis(self, result: AnalysisResult, 
                              file_path: str, file_type: str):
        """تشغيل التحليل المتوازي"""
        futures = []
        
        try:
            # تحليل الصوت
            if (file_type in ["video", "audio"] and 
                self.analysis_settings.get("enable_audio_analysis", True)):
                future = self.analysis_executor.submit(
                    self.audio_analyzer.analyze_audio, file_path
                )
                futures.append(("audio", future))
            
            # تحليل الفيديو
            if (file_type == "video" and 
                self.analysis_settings.get("enable_video_analysis", True)):
                future = self.analysis_executor.submit(
                    self.video_analyzer.analyze_video, file_path
                )
                futures.append(("video", future))
            
            # استخراج المقاطع
            if (file_type == "video" and 
                self.analysis_settings.get("enable_clip_extraction", True)):
                future = self.analysis_executor.submit(
                    self.clip_extractor.extract_clips, file_path
                )
                futures.append(("clips", future))
            
            # جمع النتائج
            for analysis_type, future in futures:
                try:
                    analysis_result = future.result(timeout=300)  # 5 دقائق timeout
                    
                    if analysis_type == "audio":
                        result.audio_analysis = analysis_result
                    elif analysis_type == "video":
                        result.video_analysis = analysis_result
                    elif analysis_type == "clips":
                        result.extracted_clips = analysis_result
                        
                except Exception as e:
                    self.logger.error(f"خطأ في تحليل {analysis_type}: {str(e)}")
                    result.errors.append(f"فشل تحليل {analysis_type}: {str(e)}")
            
            # التنبؤ بالانتشار (يحتاج نتائج التحليلات الأخرى)
            if self.analysis_settings.get("enable_viral_prediction", True):
                viral_result = self.viral_predictor.predict_virality(
                    result.audio_analysis,
                    result.video_analysis,
                    result.content_item
                )
                result.viral_score = viral_result.get("score", 0.0)
                result.viral_factors = viral_result.get("factors", {})
            
        except Exception as e:
            self.logger.error(f"خطأ في التحليل المتوازي: {str(e)}")
            result.errors.append(f"خطأ في التحليل المتوازي: {str(e)}")
    
    def _run_sequential_analysis(self, result: AnalysisResult, 
                                file_path: str, file_type: str):
        """تشغيل التحليل المتسلسل"""
        try:
            # تحليل الصوت
            if (file_type in ["video", "audio"] and 
                self.analysis_settings.get("enable_audio_analysis", True)):
                try:
                    result.audio_analysis = self.audio_analyzer.analyze_audio(file_path)
                except Exception as e:
                    self.logger.error(f"خطأ في تحليل الصوت: {str(e)}")
                    result.errors.append(f"فشل تحليل الصوت: {str(e)}")
            
            # تحليل الفيديو
            if (file_type == "video" and 
                self.analysis_settings.get("enable_video_analysis", True)):
                try:
                    result.video_analysis = self.video_analyzer.analyze_video(file_path)
                except Exception as e:
                    self.logger.error(f"خطأ في تحليل الفيديو: {str(e)}")
                    result.errors.append(f"فشل تحليل الفيديو: {str(e)}")
            
            # استخراج المقاطع
            if (file_type == "video" and 
                self.analysis_settings.get("enable_clip_extraction", True)):
                try:
                    result.extracted_clips = self.clip_extractor.extract_clips(file_path)
                except Exception as e:
                    self.logger.error(f"خطأ في استخراج المقاطع: {str(e)}")
                    result.errors.append(f"فشل استخراج المقاطع: {str(e)}")
            
            # التنبؤ بالانتشار
            if self.analysis_settings.get("enable_viral_prediction", True):
                try:
                    viral_result = self.viral_predictor.predict_virality(
                        result.audio_analysis,
                        result.video_analysis,
                        result.content_item
                    )
                    result.viral_score = viral_result.get("score", 0.0)
                    result.viral_factors = viral_result.get("factors", {})
                except Exception as e:
                    self.logger.error(f"خطأ في التنبؤ بالانتشار: {str(e)}")
                    result.errors.append(f"فشل التنبؤ بالانتشار: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"خطأ في التحليل المتسلسل: {str(e)}")
            result.errors.append(f"خطأ في التحليل المتسلسل: {str(e)}")
    
    def _calculate_overall_score(self, result: AnalysisResult):
        """حساب التقييم الشامل"""
        try:
            scores = []
            weights = []
            
            # تقييم الصوت
            if result.audio_analysis:
                audio_score = result.audio_analysis.get("quality_score", 0.0)
                scores.append(audio_score)
                weights.append(0.3)
            
            # تقييم الفيديو
            if result.video_analysis:
                video_score = result.video_analysis.get("quality_score", 0.0)
                scores.append(video_score)
                weights.append(0.4)
            
            # تقييم الانتشار
            if result.viral_score > 0:
                scores.append(result.viral_score)
                weights.append(0.3)
            
            # حساب المتوسط المرجح
            if scores and weights:
                total_weight = sum(weights)
                weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
                result.overall_score = weighted_sum / total_weight
            else:
                result.overall_score = 0.0
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب التقييم الشامل: {str(e)}")
            result.overall_score = 0.0
    
    def _generate_recommendations(self, result: AnalysisResult):
        """إنشاء التوصيات"""
        try:
            score = result.overall_score
            viral_score = result.viral_score
            
            if score >= 0.8:
                result.recommendation = "محتوى ممتاز - يُنصح بالنشر فوراً"
                result.tags.append("ممتاز")
            elif score >= 0.6:
                result.recommendation = "محتوى جيد - يمكن تحسينه قبل النشر"
                result.tags.append("جيد")
            elif score >= 0.4:
                result.recommendation = "محتوى متوسط - يحتاج تحسينات كبيرة"
                result.tags.append("متوسط")
            else:
                result.recommendation = "محتوى ضعيف - لا يُنصح بالنشر"
                result.tags.append("ضعيف")
            
            # تقييم الانتشار
            if viral_score >= self.analysis_settings.get("viral_threshold", 0.7):
                result.tags.append("فيروسي")
                result.recommendation += " - إمكانية انتشار عالية"
            
            # تحديد أفضل اللحظات
            if result.extracted_clips:
                # ترتيب المقاطع حسب الجودة
                sorted_clips = sorted(
                    result.extracted_clips,
                    key=lambda x: x.get("score", 0.0) if isinstance(x, dict) else 0.0,
                    reverse=True
                )
                
                max_clips = self.analysis_settings.get("max_clips_per_content", 5)
                result.best_moments = sorted_clips[:max_clips]
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            result.recommendation = "خطأ في التحليل"
    
    def _save_analysis_result(self, result: AnalysisResult):
        """حفظ نتيجة التحليل"""
        try:
            # إنشاء اسم ملف آمن
            safe_filename = self.security_manager.generate_secure_filename(
                f"analysis_{result.content_item.id}_{int(result.timestamp.timestamp())}.json"
            )
            
            result_file = self.results_dir / safe_filename
            
            # حفظ النتيجة
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"تم حفظ نتيجة التحليل: {result_file}")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ نتيجة التحليل: {str(e)}")
    
    def _update_average_processing_time(self, processing_time: float):
        """تحديث متوسط وقت المعالجة"""
        try:
            current_avg = self.stats["average_processing_time"]
            total_analyzed = self.stats["total_analyzed"]
            
            if total_analyzed == 1:
                self.stats["average_processing_time"] = processing_time
            else:
                # حساب المتوسط المتحرك
                self.stats["average_processing_time"] = (
                    (current_avg * (total_analyzed - 1) + processing_time) / total_analyzed
                )
        except Exception as e:
            self.logger.error(f"خطأ في تحديث متوسط وقت المعالجة: {str(e)}")
    
    def analyze_batch(self, content_items: List[Tuple[ContentItem, str]]) -> List[AnalysisResult]:
        """تحليل مجموعة من المحتويات"""
        results = []
        
        try:
            self.logger.info(f"بدء تحليل مجموعة من {len(content_items)} محتوى")
            
            for content_item, download_path in content_items:
                try:
                    result = self.analyze_content(content_item, download_path)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"خطأ في تحليل المحتوى {content_item.id}: {str(e)}")
                    # إنشاء نتيجة فارغة مع الخطأ
                    error_result = AnalysisResult(content_item)
                    error_result.errors.append(str(e))
                    results.append(error_result)
            
            self.logger.info(f"انتهى تحليل المجموعة - {len(results)} نتيجة")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المجموعة: {str(e)}")
        
        return results
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحليل"""
        return self.stats.copy()
    
    def update_analysis_settings(self, settings: Dict[str, Any]):
        """تحديث إعدادات التحليل"""
        try:
            self.analysis_settings.update(settings)
            self.config_manager.set_setting("ai_settings", "analysis", self.analysis_settings)
            self.logger.info("تم تحديث إعدادات التحليل")
        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات التحليل: {str(e)}")
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            self.analysis_executor.shutdown(wait=True)
            self.cleanup_resources()
            self.logger.info("تم تنظيف موارد محلل المحتوى")
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {str(e)}")

    # ==================== دوال التحسين الجديدة ====================

    def _start_cleanup_thread(self):
        """بدء خيط التنظيف الدوري"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.optimization_settings["cleanup_interval"])
                    self._cleanup_cache()
                    self._cleanup_memory()
                except Exception as e:
                    self.logger.error(f"خطأ في خيط التنظيف: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    @lru_cache(maxsize=128)
    def get_cached_analysis(self, content_hash: str) -> Optional[Dict[str, Any]]:
        """الحصول على تحليل من التخزين المؤقت"""
        if not self.optimization_settings["enable_caching"]:
            return None

        try:
            if content_hash in self.analysis_cache:
                cache_entry = self.analysis_cache[content_hash]
                # فحص انتهاء الصلاحية
                if time.time() - cache_entry["timestamp"] < self.cache_ttl:
                    self.performance_stats["cache_hits"] += 1
                    return cache_entry["data"]
                else:
                    # إزالة البيانات المنتهية الصلاحية
                    del self.analysis_cache[content_hash]

            self.performance_stats["cache_misses"] += 1
            return None

        except Exception as e:
            self.logger.error(f"خطأ في جلب التحليل من التخزين المؤقت: {e}")
            return None

    def cache_analysis(self, content_hash: str, analysis_data: Dict[str, Any]):
        """حفظ تحليل في التخزين المؤقت"""
        if not self.optimization_settings["enable_caching"]:
            return

        try:
            self.analysis_cache[content_hash] = {
                "data": analysis_data,
                "timestamp": time.time()
            }
        except Exception as e:
            self.logger.error(f"خطأ في حفظ التحليل في التخزين المؤقت: {e}")

    def _cleanup_cache(self):
        """تنظيف التخزين المؤقت من البيانات المنتهية الصلاحية"""
        try:
            current_time = time.time()
            expired_keys = []

            for key, entry in self.analysis_cache.items():
                if current_time - entry["timestamp"] > self.cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.analysis_cache[key]

            if expired_keys:
                self.logger.debug(f"تم تنظيف {len(expired_keys)} عنصر من التخزين المؤقت")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف التخزين المؤقت: {e}")

    def _cleanup_memory(self):
        """تنظيف الذاكرة وتحسين الأداء"""
        try:
            # تنظيف نماذج غير مستخدمة
            if hasattr(self, 'model_cache'):
                for model_name in list(self.model_cache.keys()):
                    # إزالة النماذج غير المستخدمة لفترة طويلة
                    if time.time() - self.model_cache[model_name].get("last_used", 0) > 1800:  # 30 دقيقة
                        del self.model_cache[model_name]

            # تشغيل garbage collection
            collected = gc.collect()

            # تحديث إحصائيات الذاكرة
            if PSUTIL_AVAILABLE:
                process = psutil.Process()
                self.performance_stats["memory_usage"] = process.memory_info().rss / 1024 / 1024  # MB

            self.logger.debug(f"تم تنظيف الذاكرة - تم تحرير {collected} كائن")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الذاكرة: {e}")

    async def analyze_content_async(self, content_item: ContentItem,
                                   download_path: str = None) -> AnalysisResult:
        """تحليل المحتوى بشكل غير متزامن"""
        if not ASYNC_AVAILABLE:
            # fallback للطريقة التقليدية
            return self.analyze_content(content_item, download_path)

        try:
            # تشغيل التحليل في executor منفصل
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.analysis_executor,
                self.analyze_content,
                content_item,
                download_path
            )
            return result

        except Exception as e:
            self.logger.error(f"خطأ في التحليل غير المتزامن: {e}")
            # fallback للطريقة التقليدية
            return self.analyze_content(content_item, download_path)

    def batch_analyze_content(self, content_items: List[Tuple[ContentItem, str]]) -> List[AnalysisResult]:
        """معالجة دفعية محسنة للمحتوى"""
        if not self.optimization_settings["enable_batch_processing"]:
            return self.analyze_batch(content_items)

        try:
            results = []

            # تقسيم إلى دفعات
            for i in range(0, len(content_items), self.batch_size):
                batch = content_items[i:i + self.batch_size]

                # معالجة الدفعة بالتوازي
                with ThreadPoolExecutor(max_workers=min(len(batch), mp.cpu_count())) as executor:
                    futures = []
                    for content_item, download_path in batch:
                        future = executor.submit(self.analyze_content, content_item, download_path)
                        futures.append(future)

                    # جمع النتائج
                    for future in as_completed(futures):
                        try:
                            result = future.result(timeout=60)  # مهلة دقيقة واحدة
                            results.append(result)
                        except Exception as e:
                            self.logger.error(f"خطأ في معالجة دفعية: {e}")

                self.performance_stats["batch_processed"] += 1

            return results

        except Exception as e:
            self.logger.error(f"خطأ في المعالجة الدفعية: {e}")
            return self.analyze_batch(content_items)

    def optimize_models(self):
        """تحسين النماذج المحملة"""
        try:
            # تحسين نماذج التحليل
            if hasattr(self.audio_analyzer, 'optimize_model'):
                self.audio_analyzer.optimize_model()

            if hasattr(self.video_analyzer, 'optimize_model'):
                self.video_analyzer.optimize_model()

            if hasattr(self.viral_predictor, 'optimize_model'):
                self.viral_predictor.optimize_model()

            self.logger.info("تم تحسين النماذج بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في تحسين النماذج: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        try:
            stats = self.performance_stats.copy()

            # إضافة إحصائيات إضافية
            stats.update({
                "cache_size": len(self.analysis_cache),
                "cache_hit_ratio": (
                    stats["cache_hits"] / max(stats["cache_hits"] + stats["cache_misses"], 1)
                ),
                "average_processing_time": (
                    sum(stats["processing_times"]) / max(len(stats["processing_times"]), 1)
                    if stats["processing_times"] else 0
                ),
                "total_analyses": self.stats["total_analyzed"],
                "success_rate": (
                    self.stats["successful_analyses"] / max(self.stats["total_analyzed"], 1)
                )
            })

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في جلب إحصائيات الأداء: {e}")
            return {}

    def cleanup_resources(self):
        """تنظيف شامل للموارد"""
        try:
            # تنظيف التخزين المؤقت
            self.analysis_cache.clear()
            self.model_cache.clear()

            # تنظيف قائمة الانتظار
            self.batch_queue.clear()

            # تنظيف الذاكرة
            self._cleanup_memory()

            # إعادة تعيين الإحصائيات
            self.performance_stats = {
                "cache_hits": 0,
                "cache_misses": 0,
                "batch_processed": 0,
                "memory_usage": 0,
                "processing_times": [],
                "gpu_usage": 0
            }

            self.logger.info("تم تنظيف جميع الموارد")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {e}")
