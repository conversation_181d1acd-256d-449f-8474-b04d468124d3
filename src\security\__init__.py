#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الأمان والحماية - Security Module
تحتوي على جميع أنظمة الحماية والتشفير والأمان
"""

from .advanced_encryption import (
    AdvancedEncryptionSystem,
    EncryptionLevel,
    KeyType,
    EncryptionKey,
    EncryptedData
)

from .tamper_protection import (
    TamperProtectionSystem,
    ProtectionLevel,
    IntegrityStatus,
    FileIntegrity,
    TamperAlert,
    create_tamper_protection_system,
    protect_file_quick,
    verify_file_quick,
    protect_directory_quick,
    scan_protected_files_quick,
    get_protection_report_quick
)

from .advanced_authentication import (
    AdvancedAuthenticationSystem,
    AuthenticationMethod,
    SessionStatus,
    SecurityLevel,
    User,
    Session,
    AuthenticationAttempt
)

from .malware_protection import (
    MalwareProtectionSystem,
    ThreatLevel,
    ThreatType,
    ScanStatus,
    ThreatDetection,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ProcessInfo,
    create_malware_protection_system,
    scan_file_for_malware,
    scan_directory_for_malware
)

from .secure_backup import (
    SecureBackupSystem,
    BackupType,
    BackupStatus,
    CompressionLevel,
    BackupItem,
    BackupJob,
    BackupRecord,
    create_secure_backup_system,
    backup_files
)

__all__ = [
    # Advanced Encryption System
    'AdvancedEncryptionSystem',
    'EncryptionLevel',
    'KeyType',
    'EncryptionKey',
    'EncryptedData',

    # Tamper Protection System
    'TamperProtectionSystem',
    'ProtectionLevel',
    'IntegrityStatus',
    'FileIntegrity',
    'TamperAlert',
    'create_tamper_protection_system',
    'protect_file_quick',
    'verify_file_quick',
    'protect_directory_quick',
    'scan_protected_files_quick',
    'get_protection_report_quick',

    # Advanced Authentication System
    'AdvancedAuthenticationSystem',
    'AuthenticationMethod',
    'SessionStatus',
    'SecurityLevel',
    'User',
    'Session',
    'AuthenticationAttempt',

    # Malware Protection System
    'MalwareProtectionSystem',
    'ThreatLevel',
    'ThreatType',
    'ScanStatus',
    'ThreatDetection',
    'ScanResult',
    'ProcessInfo',
    'create_malware_protection_system',
    'scan_file_for_malware',
    'scan_directory_for_malware',

    # Secure Backup System
    'SecureBackupSystem',
    'BackupType',
    'BackupStatus',
    'CompressionLevel',
    'BackupItem',
    'BackupJob',
    'BackupRecord',
    'create_secure_backup_system',
    'backup_files'
]
