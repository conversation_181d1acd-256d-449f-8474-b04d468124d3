{"frame_processing": {"batch_size": 16, "skip_frames": 2, "resize_dimensions": [640, 480], "color_space": "RGB", "compression_quality": 85}, "feature_extraction": {"use_gpu": true, "parallel_workers": 4, "cache_features": true, "feature_cache_size": 1000}, "motion_detection": {"algorithm": "optical_flow", "sensitivity": 0.3, "min_area": 500}, "object_detection": {"confidence_threshold": 0.5, "nms_threshold": 0.4, "max_detections": 100}}