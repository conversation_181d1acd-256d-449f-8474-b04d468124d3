# 📦 دليل تثبيت المكتبات - منشئ المحتوى الذكي

## 🎯 خيارات التثبيت

### 1️⃣ **التثبيت السريع (الأساسي)**
للبدء السريع مع الميزات الأساسية:

```bash
pip install -r requirements_essential.txt
```

**المكتبات المشمولة:**
- واجهة المستخدم (PyQt6)
- معالجة فيديو أساسية (moviepy, opencv)
- تفاعل مع الشبكة (requests, selenium)
- تحليل بيانات بسيط (pandas, numpy)
- أمان أساسي (cryptography, bcrypt)

---

### 2️⃣ **التثبيت الشامل (كامل)**
للحصول على جميع الميزات:

```bash
pip install -r requirements_complete.txt
```

**⚠️ تحذير:** قد يستغرق وقتاً طويلاً (30-60 دقيقة)

---

### 3️⃣ **التثبيت التدريجي (موصى به)**

#### المرحلة 1: الأساسيات
```bash
pip install PyQt6 PyQt6-tools requests pillow numpy pandas
```

#### المرحلة 2: معالجة الفيديو
```bash
pip install moviepy opencv-python ffmpeg-python pydub
```

#### المرحلة 3: الذكاء الاصطناعي
```bash
pip install torch torchvision transformers openai-whisper
```

#### المرحلة 4: وسائل التواصل
```bash
pip install selenium playwright instaloader yt-dlp
```

#### المرحلة 5: أدوات متقدمة
```bash
pip install cryptography schedule tqdm rich psutil
```

---

## 🔧 **متطلبات النظام**

### Windows:
- Python 3.8+ (موصى به 3.11+)
- 8GB RAM (16GB للذكاء الاصطناعي)
- 10GB مساحة فارغة
- Visual Studio Build Tools (للمكتبات المترجمة)

### تثبيت Visual Studio Build Tools:
```bash
# تحميل من:
https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

---

## 🚨 **مشاكل شائعة وحلولها**

### 1. مشكلة PyTorch
```bash
# للـ CPU فقط
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# للـ GPU (CUDA)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. مشكلة OpenCV
```bash
pip uninstall opencv-python opencv-contrib-python
pip install opencv-python-headless
```

### 3. مشكلة FFmpeg
```bash
# Windows
choco install ffmpeg
# أو تحميل من: https://ffmpeg.org/download.html
```

### 4. مشكلة Selenium
```bash
pip install selenium webdriver-manager
```

### 5. مشكلة المكتبات المترجمة
```bash
pip install --upgrade pip setuptools wheel
pip install --only-binary=all package_name
```

---

## 📋 **فحص التثبيت**

### تشغيل فحص شامل:
```bash
python required_libraries.py
```

### فحص مكتبة محددة:
```python
import importlib
try:
    importlib.import_module('PyQt6')
    print("✅ PyQt6 مثبت")
except ImportError:
    print("❌ PyQt6 غير مثبت")
```

---

## 🎯 **تثبيت حسب الاستخدام**

### للمطورين:
```bash
pip install -r requirements_essential.txt
pip install pytest black flake8 mypy
```

### لمنشئي المحتوى:
```bash
pip install -r requirements_essential.txt
pip install moviepy opencv-python yt-dlp
```

### للذكاء الاصطناعي:
```bash
pip install torch transformers openai-whisper mediapipe
```

---

## 🔄 **تحديث المكتبات**

```bash
# تحديث جميع المكتبات
pip install --upgrade -r requirements_essential.txt

# تحديث مكتبة واحدة
pip install --upgrade package_name

# فحص المكتبات القديمة
pip list --outdated
```

---

## 💡 **نصائح للتحسين**

### 1. استخدام البيئة الافتراضية:
```bash
python -m venv smart_content_env
smart_content_env\Scripts\activate  # Windows
pip install -r requirements_essential.txt
```

### 2. تسريع التثبيت:
```bash
pip install --upgrade pip
pip install --cache-dir ./pip_cache -r requirements_essential.txt
```

### 3. تثبيت متوازي:
```bash
pip install --upgrade pip
pip install -r requirements_essential.txt --parallel
```

---

## 📊 **إحصائيات المكتبات**

| الفئة | عدد المكتبات | الحجم التقريبي |
|-------|---------------|-----------------|
| أساسية | 20 | ~500MB |
| معالجة فيديو | 8 | ~1GB |
| ذكاء اصطناعي | 12 | ~3GB |
| وسائل تواصل | 10 | ~200MB |
| أدوات مساعدة | 25 | ~300MB |
| **المجموع** | **75+** | **~5GB** |

---

## 🆘 **الحصول على المساعدة**

### إذا واجهت مشاكل:
1. تأكد من تحديث pip: `pip install --upgrade pip`
2. جرب التثبيت في بيئة افتراضية جديدة
3. تحقق من متطلبات النظام
4. ابحث عن الخطأ في Google
5. اسأل في المجتمعات البرمجية

### مصادر مفيدة:
- [Python Package Index](https://pypi.org/)
- [PyTorch Installation](https://pytorch.org/get-started/locally/)
- [OpenCV Installation](https://docs.opencv.org/4.x/d5/de5/tutorial_py_setup_in_windows.html)

---

## ✅ **التحقق من نجاح التثبيت**

بعد التثبيت، شغل:
```bash
python test_final_exe.py
```

إذا ظهرت رسالة "✅ التطبيق جاهز للاستخدام" فكل شيء يعمل بشكل صحيح!
