#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحتوى الرئيسي - Content Manager
ينسق بين جميع منصات جلب المحتوى ويدير العمليات المشتركة
"""

import logging
import threading
import time
import asyncio
import weakref
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import json

from .snapchat_fetcher import SnapchatFetcher
from .tiktok_fetcher import TikTokFetcher
from .kick_fetcher import KickFetcher
from .base_fetcher import ContentItem
from ..core.logger import activity_logger

# محاولة استيراد المكتبات المحسنة
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

class ContentManager:
    """مدير المحتوى الرئيسي المحسن"""

    def __init__(self, config_manager, security_manager):
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.logger = logging.getLogger(__name__)

        # إنشاء جالبات المحتوى
        self.fetchers = {
            "snapchat": SnapchatFetcher(config_manager, security_manager),
            "tiktok": TikTokFetcher(config_manager, security_manager),
            "kick": KickFetcher(config_manager, security_manager)
        }

        # تحسينات الأداء
        self.thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ContentFetcher")
        self.content_cache = {}  # تخزين مؤقت للمحتوى
        self.cache_ttl = 300  # 5 دقائق
        self.rate_limiter = {}  # محدد المعدل لكل منصة

        # حالة المراقبة
        self.monitoring_active = False
        self.monitoring_thread = None
        self.stop_monitoring_event = threading.Event()

        # قائمة انتظار المحتوى الجديد (محسنة)
        self.content_queue = []
        self.queue_lock = threading.Lock()
        self.max_queue_size = 1000  # حد أقصى لحجم القائمة

        # معالجات الأحداث (مع weak references لتجنب تسريب الذاكرة)
        self.event_handlers = {
            "new_content": weakref.WeakSet(),
            "content_downloaded": weakref.WeakSet(),
            "monitoring_started": weakref.WeakSet(),
            "monitoring_stopped": [],
            "error": []
        }
        
        # إحصائيات شاملة
        self.global_stats = {
            "total_content_fetched": 0,
            "total_content_downloaded": 0,
            "total_errors": 0,
            "monitoring_start_time": None,
            "last_activity": None
        }
        
        # إعدادات المراقبة
        self.monitoring_settings = self._load_monitoring_settings()
    
    def _load_monitoring_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المراقبة"""
        try:
            return self.config_manager.get_setting("monitoring_settings", "global", {
                "check_interval": 300,  # 5 دقائق
                "auto_download": True,
                "content_types": ["stories", "lives", "clips"],
                "max_content_per_check": 50,
                "enabled_platforms": ["snapchat", "tiktok", "kick"]
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المراقبة: {str(e)}")
            return {
                "check_interval": 300,
                "auto_download": True,
                "content_types": ["stories", "lives", "clips"],
                "max_content_per_check": 50,
                "enabled_platforms": ["snapchat", "tiktok", "kick"]
            }
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """إضافة معالج حدث"""
        if event_type in self.event_handlers:
            self.event_handlers[event_type].append(handler)
    
    def remove_event_handler(self, event_type: str, handler: Callable):
        """إزالة معالج حدث"""
        if event_type in self.event_handlers and handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
    
    def _trigger_event(self, event_type: str, data: Any = None):
        """تشغيل معالجات الأحداث"""
        try:
            for handler in self.event_handlers.get(event_type, []):
                handler(data)
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل معالج الحدث {event_type}: {str(e)}")
    
    def start_monitoring(self, platforms: List[str] = None, 
                        content_types: List[str] = None,
                        check_interval: int = None) -> bool:
        """بدء مراقبة المحتوى"""
        try:
            if self.monitoring_active:
                self.logger.warning("المراقبة نشطة بالفعل")
                return False
            
            # تحديث الإعدادات
            if platforms:
                self.monitoring_settings["enabled_platforms"] = platforms
            if content_types:
                self.monitoring_settings["content_types"] = content_types
            if check_interval:
                self.monitoring_settings["check_interval"] = check_interval
            
            # التحقق من وجود منصات مفعلة
            enabled_platforms = [
                platform for platform in self.monitoring_settings["enabled_platforms"]
                if platform in self.fetchers and self.fetchers[platform].is_authenticated()
            ]
            
            if not enabled_platforms:
                self.logger.error("لا توجد منصات مصادق عليها للمراقبة")
                return False
            
            # بدء المراقبة
            self.monitoring_active = True
            self.stop_monitoring_event.clear()
            self.global_stats["monitoring_start_time"] = datetime.now()
            
            # بدء خيط المراقبة
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            # بدء مراقبة المنصات الفردية
            for platform in enabled_platforms:
                self.fetchers[platform].start_monitoring(
                    content_types=self.monitoring_settings["content_types"],
                    check_interval=self.monitoring_settings["check_interval"]
                )
            
            self.logger.info(f"تم بدء مراقبة المنصات: {enabled_platforms}")
            self._trigger_event("monitoring_started", enabled_platforms)
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء المراقبة: {str(e)}")
            self._trigger_event("error", str(e))
            return False
    
    def stop_monitoring(self):
        """إيقاف مراقبة المحتوى"""
        try:
            if not self.monitoring_active:
                return
            
            self.monitoring_active = False
            self.stop_monitoring_event.set()
            
            # إيقاف مراقبة المنصات الفردية
            for fetcher in self.fetchers.values():
                fetcher.stop_monitoring()
            
            # انتظار انتهاء خيط المراقبة
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            self.logger.info("تم إيقاف مراقبة المحتوى")
            self._trigger_event("monitoring_stopped")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المراقبة: {str(e)}")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active and not self.stop_monitoring_event.is_set():
            try:
                # التحقق من الأمان
                if not self.security_manager.is_safe_to_continue():
                    self.logger.warning("تم إيقاف المراقبة لأسباب أمنية")
                    break
                
                # فحص المحتوى الجديد من جميع المنصات
                new_content = self._check_all_platforms()
                
                if new_content:
                    # إضافة المحتوى الجديد لقائمة الانتظار
                    with self.queue_lock:
                        self.content_queue.extend(new_content)
                    
                    # تشغيل معالجات المحتوى الجديد
                    self._trigger_event("new_content", new_content)
                    
                    # تحديث الإحصائيات
                    self.global_stats["total_content_fetched"] += len(new_content)
                    self.global_stats["last_activity"] = datetime.now()
                    
                    self.logger.info(f"تم العثور على {len(new_content)} محتوى جديد")
                    
                    # تحميل تلقائي إذا كان مفعلاً
                    if self.monitoring_settings.get("auto_download", True):
                        self._auto_download_content(new_content)
                
                # انتظار قبل الفحص التالي
                wait_time = self.monitoring_settings.get("check_interval", 300)
                if self.stop_monitoring_event.wait(wait_time):
                    break
                    
            except Exception as e:
                self.logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                self.global_stats["total_errors"] += 1
                self._trigger_event("error", str(e))
                
                # انتظار قصير قبل المحاولة مرة أخرى
                if self.stop_monitoring_event.wait(30):
                    break
    
    def _check_all_platforms(self) -> List[ContentItem]:
        """فحص المحتوى الجديد من جميع المنصات"""
        all_new_content = []
        
        enabled_platforms = self.monitoring_settings.get("enabled_platforms", [])
        content_types = self.monitoring_settings.get("content_types", [])
        max_content = self.monitoring_settings.get("max_content_per_check", 50)
        
        for platform_name in enabled_platforms:
            if platform_name not in self.fetchers:
                continue
                
            fetcher = self.fetchers[platform_name]
            
            try:
                # فحص المحتوى الجديد
                new_content = fetcher.check_for_new_content(content_types)
                all_new_content.extend(new_content)
                
                # تسجيل النشاط
                if new_content:
                    activity_logger.log_content_fetched(platform_name, len(new_content))
                
            except Exception as e:
                self.logger.error(f"خطأ في فحص محتوى {platform_name}: {str(e)}")
                activity_logger.log_error(platform_name, str(e))
        
        # ترتيب وتحديد العدد الأقصى
        all_new_content.sort(key=lambda x: x.timestamp, reverse=True)
        return all_new_content[:max_content]
    
    def _auto_download_content(self, content_items: List[ContentItem]):
        """تحميل المحتوى تلقائياً"""
        def download_worker():
            for item in content_items:
                try:
                    if not self.monitoring_active:
                        break
                    
                    # تحديد المنصة والتحميل
                    fetcher = self.fetchers.get(item.platform)
                    if fetcher and fetcher.download_content(item):
                        self.global_stats["total_content_downloaded"] += 1
                        self._trigger_event("content_downloaded", item)
                    
                    # تأخير بسيط بين التحميلات
                    time.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"خطأ في التحميل التلقائي: {str(e)}")
        
        # تشغيل التحميل في خيط منفصل
        download_thread = threading.Thread(target=download_worker, daemon=True)
        download_thread.start()
    
    def get_content_queue(self) -> List[ContentItem]:
        """الحصول على قائمة انتظار المحتوى"""
        with self.queue_lock:
            return self.content_queue.copy()
    
    def clear_content_queue(self):
        """مسح قائمة انتظار المحتوى"""
        with self.queue_lock:
            self.content_queue.clear()
    
    def download_content(self, content_item: ContentItem) -> bool:
        """تحميل محتوى معين"""
        try:
            fetcher = self.fetchers.get(content_item.platform)
            if not fetcher:
                self.logger.error(f"لا يوجد جالب للمنصة: {content_item.platform}")
                return False
            
            success = fetcher.download_content(content_item)
            if success:
                self.global_stats["total_content_downloaded"] += 1
                self._trigger_event("content_downloaded", content_item)
            
            return success
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المحتوى: {str(e)}")
            return False
    
    def get_platform_stats(self, platform: str = None) -> Dict[str, Any]:
        """الحصول على إحصائيات المنصة"""
        if platform and platform in self.fetchers:
            return self.fetchers[platform].get_platform_stats()
        
        # إحصائيات جميع المنصات
        all_stats = {}
        for platform_name, fetcher in self.fetchers.items():
            all_stats[platform_name] = fetcher.get_platform_stats()
        
        return all_stats
    
    def get_global_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات الشاملة"""
        stats = self.global_stats.copy()
        stats.update({
            "monitoring_active": self.monitoring_active,
            "queue_size": len(self.content_queue),
            "enabled_platforms": self.monitoring_settings.get("enabled_platforms", []),
            "authenticated_platforms": [
                name for name, fetcher in self.fetchers.items()
                if fetcher.is_authenticated()
            ]
        })
        return stats
    
    def test_all_connections(self) -> Dict[str, bool]:
        """اختبار الاتصال مع جميع المنصات"""
        results = {}
        for platform_name, fetcher in self.fetchers.items():
            try:
                results[platform_name] = fetcher.test_connection()
            except Exception as e:
                self.logger.error(f"خطأ في اختبار {platform_name}: {str(e)}")
                results[platform_name] = False
        
        return results
    
    def add_monitored_user(self, platform: str, username: str) -> bool:
        """إضافة مستخدم للمراقبة في منصة معينة"""
        if platform in self.fetchers:
            return self.fetchers[platform].add_monitored_user(username)
        return False
    
    def remove_monitored_user(self, platform: str, username: str) -> bool:
        """إزالة مستخدم من المراقبة في منصة معينة"""
        if platform in self.fetchers:
            return self.fetchers[platform].remove_monitored_user(username)
        return False
    
    def get_monitored_users(self, platform: str = None) -> Dict[str, List[str]]:
        """الحصول على المستخدمين المراقبين"""
        if platform and platform in self.fetchers:
            return {platform: self.fetchers[platform].get_monitored_users()}
        
        # جميع المنصات
        all_users = {}
        for platform_name, fetcher in self.fetchers.items():
            all_users[platform_name] = fetcher.get_monitored_users()
        
        return all_users
    
    def cleanup_old_content(self, days: int = 30):
        """تنظيف المحتوى القديم من جميع المنصات"""
        for fetcher in self.fetchers.values():
            try:
                fetcher.cleanup_old_content(days)
            except Exception as e:
                self.logger.error(f"خطأ في تنظيف المحتوى: {str(e)}")
    
    def update_monitoring_settings(self, settings: Dict[str, Any]):
        """تحديث إعدادات المراقبة"""
        try:
            self.monitoring_settings.update(settings)
            self.config_manager.set_setting("monitoring_settings", "global", self.monitoring_settings)
            self.logger.info("تم تحديث إعدادات المراقبة")
        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات المراقبة: {str(e)}")
    
    def get_monitoring_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات المراقبة"""
        return self.monitoring_settings.copy()

    # ===== دوال التحسين الجديدة =====

    @lru_cache(maxsize=128)
    def get_cached_content(self, platform: str, user_id: str, content_type: str = "recent"):
        """الحصول على المحتوى من التخزين المؤقت"""
        cache_key = f"{platform}:{user_id}:{content_type}"

        if cache_key in self.content_cache:
            cached_data, timestamp = self.content_cache[cache_key]
            # فحص انتهاء صلاحية التخزين المؤقت
            if time.time() - timestamp < self.cache_ttl:
                return cached_data
            else:
                # إزالة البيانات المنتهية الصلاحية
                del self.content_cache[cache_key]

        return None

    def cache_content(self, platform: str, user_id: str, content_type: str, data: Any):
        """حفظ المحتوى في التخزين المؤقت"""
        cache_key = f"{platform}:{user_id}:{content_type}"
        self.content_cache[cache_key] = (data, time.time())

        # تنظيف التخزين المؤقت إذا أصبح كبيراً جداً
        if len(self.content_cache) > 500:
            self._cleanup_cache()

    def _cleanup_cache(self):
        """تنظيف التخزين المؤقت من البيانات القديمة"""
        current_time = time.time()
        expired_keys = []

        for key, (data, timestamp) in self.content_cache.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self.content_cache[key]

        self.logger.debug(f"تم تنظيف {len(expired_keys)} عنصر من التخزين المؤقت")

    def check_rate_limit(self, platform: str) -> bool:
        """فحص حدود المعدل للمنصة"""
        current_time = time.time()

        if platform not in self.rate_limiter:
            self.rate_limiter[platform] = {"requests": [], "limit": 60, "window": 60}  # 60 طلب في الدقيقة

        rate_data = self.rate_limiter[platform]

        # إزالة الطلبات القديمة
        rate_data["requests"] = [req_time for req_time in rate_data["requests"]
                                if current_time - req_time < rate_data["window"]]

        # فحص الحد الأقصى
        if len(rate_data["requests"]) >= rate_data["limit"]:
            return False

        # إضافة الطلب الحالي
        rate_data["requests"].append(current_time)
        return True

    async def fetch_content_async(self, platform: str, user_id: str, content_type: str = "recent"):
        """جلب المحتوى بشكل غير متزامن (إذا كان aiohttp متوفراً)"""
        if not AIOHTTP_AVAILABLE:
            # العودة للطريقة التقليدية
            return self.fetch_content_sync(platform, user_id, content_type)

        # فحص التخزين المؤقت أولاً
        cached_content = self.get_cached_content(platform, user_id, content_type)
        if cached_content:
            return cached_content

        # فحص حدود المعدل
        if not self.check_rate_limit(platform):
            self.logger.warning(f"تم تجاوز حد المعدل للمنصة: {platform}")
            return None

        try:
            if platform in self.fetchers:
                fetcher = self.fetchers[platform]
                # استخدام thread pool للعمليات المتزامنة
                loop = asyncio.get_event_loop()
                content = await loop.run_in_executor(
                    self.thread_pool,
                    fetcher.fetch_user_content,
                    user_id,
                    content_type
                )

                # حفظ في التخزين المؤقت
                if content:
                    self.cache_content(platform, user_id, content_type, content)

                return content

        except Exception as e:
            self.logger.error(f"خطأ في جلب المحتوى غير المتزامن: {e}")
            return None

    def fetch_content_sync(self, platform: str, user_id: str, content_type: str = "recent"):
        """جلب المحتوى بشكل متزامن مع تحسينات"""
        # فحص التخزين المؤقت أولاً
        cached_content = self.get_cached_content(platform, user_id, content_type)
        if cached_content:
            return cached_content

        # فحص حدود المعدل
        if not self.check_rate_limit(platform):
            self.logger.warning(f"تم تجاوز حد المعدل للمنصة: {platform}")
            return None

        try:
            if platform in self.fetchers:
                fetcher = self.fetchers[platform]
                content = fetcher.fetch_user_content(user_id, content_type)

                # حفظ في التخزين المؤقت
                if content:
                    self.cache_content(platform, user_id, content_type, content)

                return content

        except Exception as e:
            self.logger.error(f"خطأ في جلب المحتوى: {e}")
            return None

    def batch_fetch_content(self, requests: List[Dict[str, str]]) -> Dict[str, Any]:
        """جلب المحتوى بشكل دفعي لتحسين الأداء"""
        results = {}

        # تجميع الطلبات حسب المنصة
        platform_requests = {}
        for req in requests:
            platform = req.get("platform")
            if platform not in platform_requests:
                platform_requests[platform] = []
            platform_requests[platform].append(req)

        # معالجة كل منصة بشكل منفصل
        futures = []
        for platform, platform_reqs in platform_requests.items():
            future = self.thread_pool.submit(self._process_platform_requests, platform, platform_reqs)
            futures.append((platform, future))

        # جمع النتائج
        for platform, future in futures:
            try:
                platform_results = future.result(timeout=30)  # مهلة زمنية 30 ثانية
                results.update(platform_results)
            except Exception as e:
                self.logger.error(f"خطأ في معالجة طلبات {platform}: {e}")

        return results

    def _process_platform_requests(self, platform: str, requests: List[Dict[str, str]]) -> Dict[str, Any]:
        """معالجة طلبات منصة واحدة"""
        results = {}

        for req in requests:
            user_id = req.get("user_id")
            content_type = req.get("content_type", "recent")

            if user_id:
                content = self.fetch_content_sync(platform, user_id, content_type)
                key = f"{platform}:{user_id}:{content_type}"
                results[key] = content

        return results

    def optimize_queue_management(self):
        """تحسين إدارة قائمة انتظار المحتوى"""
        with self.queue_lock:
            # إزالة المحتوى المكرر
            unique_content = {}
            for item in self.content_queue:
                key = f"{item.platform}:{item.user_id}:{item.content_id}"
                if key not in unique_content or item.timestamp > unique_content[key].timestamp:
                    unique_content[key] = item

            # تحديث القائمة
            self.content_queue = list(unique_content.values())

            # ترتيب حسب الأولوية والوقت
            self.content_queue.sort(key=lambda x: (x.priority, x.timestamp), reverse=True)

            # قطع القائمة إذا تجاوزت الحد الأقصى
            if len(self.content_queue) > self.max_queue_size:
                removed_count = len(self.content_queue) - self.max_queue_size
                self.content_queue = self.content_queue[:self.max_queue_size]
                self.logger.warning(f"تم إزالة {removed_count} عنصر من قائمة الانتظار لتجاوز الحد الأقصى")

    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        stats = {
            "cache_size": len(self.content_cache),
            "queue_size": len(self.content_queue),
            "active_threads": self.thread_pool._threads,
            "rate_limiter_status": {},
            "fetcher_stats": {}
        }

        # إحصائيات محدد المعدل
        for platform, rate_data in self.rate_limiter.items():
            stats["rate_limiter_status"][platform] = {
                "current_requests": len(rate_data["requests"]),
                "limit": rate_data["limit"],
                "window": rate_data["window"]
            }

        # إحصائيات الجالبات
        for platform, fetcher in self.fetchers.items():
            if hasattr(fetcher, 'get_stats'):
                stats["fetcher_stats"][platform] = fetcher.get_stats()

        return stats

    def cleanup_resources(self):
        """تنظيف الموارد عند الإغلاق"""
        try:
            # إيقاف المراقبة
            if self.monitoring_active:
                self.stop_monitoring()

            # إغلاق thread pool
            self.thread_pool.shutdown(wait=True)

            # تنظيف التخزين المؤقت
            self.content_cache.clear()

            # تنظيف قائمة الانتظار
            with self.queue_lock:
                self.content_queue.clear()

            self.logger.info("تم تنظيف موارد مدير المحتوى")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {e}")
