#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أدوات التحسين - Test Optimization Tools
اختبار بسيط لأدوات تحسين الأداء
"""

import gc
import os
import time
from datetime import datetime

def test_basic_optimization():
    """اختبار التحسين الأساسي"""
    print("🚀 بدء اختبار أدوات التحسين...")
    
    # إنشاء مجلد السجلات
    os.makedirs('logs', exist_ok=True)
    
    # اختبار تنظيف الذاكرة
    print("🧠 اختبار تنظيف الذاكرة...")
    collected = gc.collect()
    print(f"   تم تنظيف {collected} كائن من الذاكرة")
    
    # اختبار إنشاء ملف سجل
    print("📝 اختبار إنشاء ملف السجل...")
    log_file = f"logs/test_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    try:
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("تم اختبار أدوات التحسين بنجاح\n")
            f.write(f"الوقت: {datetime.now()}\n")
            f.write(f"الكائنات المنظفة: {collected}\n")
        
        print(f"   تم إنشاء ملف السجل: {log_file}")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملف السجل: {e}")
    
    # اختبار فحص الملفات
    print("📁 اختبار فحص الملفات...")
    files_found = []
    
    for item in os.listdir('.'):
        if item.endswith('.py'):
            files_found.append(item)
    
    print(f"   تم العثور على {len(files_found)} ملف Python")
    
    # اختبار التوقيت
    print("⏱️ اختبار قياس الوقت...")
    start_time = time.time()
    time.sleep(0.1)  # محاكاة عملية
    end_time = time.time()
    duration = end_time - start_time
    print(f"   مدة العملية: {duration:.3f} ثانية")
    
    print("✅ تم إكمال اختبار أدوات التحسين بنجاح!")
    
    return {
        "status": "success",
        "objects_collected": collected,
        "files_found": len(files_found),
        "duration": duration,
        "log_file": log_file
    }

if __name__ == "__main__":
    result = test_basic_optimization()
    print(f"\n📊 النتيجة: {result}")
