#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تثبيت المكتبات المطلوبة لتطبيق منشئ المحتوى الذكي
"""

import subprocess
import sys
import time
from required_libraries import REQUIRED_LIBRARIES, get_essential_libraries, get_all_libraries

def run_pip_install(package, timeout=300):
    """تثبيت حزمة واحدة مع معالجة الأخطاء"""
    try:
        print(f"🔄 تثبيت {package}...")
        
        # تشغيل pip install
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package],
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            print(f"✅ تم تثبيت {package} بنجاح")
            return True
        else:
            print(f"❌ فشل تثبيت {package}")
            print(f"   الخطأ: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ انتهت مهلة تثبيت {package}")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت {package}: {e}")
        return False

def install_category(category_name, packages, skip_errors=True):
    """تثبيت مجموعة من الحزم"""
    print(f"\n📦 تثبيت مكتبات {category_name}")
    print("=" * 50)
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if run_pip_install(package):
            success_count += 1
        else:
            failed_packages.append(package)
            if not skip_errors:
                print(f"❌ توقف التثبيت بسبب فشل {package}")
                break
        
        # توقف قصير بين التثبيتات
        time.sleep(1)
    
    print(f"\n📊 نتائج {category_name}:")
    print(f"✅ نجح: {success_count}/{len(packages)}")
    if failed_packages:
        print(f"❌ فشل: {', '.join(failed_packages)}")
    
    return success_count, failed_packages

def install_essential_only():
    """تثبيت المكتبات الأساسية فقط"""
    print("🎯 تثبيت المكتبات الأساسية فقط")
    print("=" * 60)
    
    essential = get_essential_libraries()
    success, failed = install_category("الأساسية", essential)
    
    return success, failed

def install_all_libraries():
    """تثبيت جميع المكتبات"""
    print("🚀 تثبيت جميع المكتبات")
    print("=" * 60)
    
    total_success = 0
    total_failed = []
    
    for category, packages in REQUIRED_LIBRARIES.items():
        success, failed = install_category(category, packages)
        total_success += success
        total_failed.extend(failed)
    
    return total_success, total_failed

def install_by_category():
    """تثبيت حسب الفئة مع اختيار المستخدم"""
    print("📋 اختر الفئات للتثبيت:")
    print("=" * 40)
    
    categories = list(REQUIRED_LIBRARIES.keys())
    for i, category in enumerate(categories, 1):
        count = len(REQUIRED_LIBRARIES[category])
        print(f"{i}. {category.replace('_', ' ').title()} ({count} مكتبة)")
    
    print(f"{len(categories) + 1}. جميع الفئات")
    print("0. إلغاء")
    
    try:
        choice = int(input("\nاختر رقم الفئة: "))
        
        if choice == 0:
            print("تم الإلغاء")
            return 0, []
        elif choice == len(categories) + 1:
            return install_all_libraries()
        elif 1 <= choice <= len(categories):
            category = categories[choice - 1]
            packages = REQUIRED_LIBRARIES[category]
            return install_category(category, packages)
        else:
            print("❌ اختيار غير صحيح")
            return 0, []
            
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح")
        return 0, []

def check_installed_packages():
    """فحص المكتبات المثبتة"""
    print("🔍 فحص المكتبات المثبتة...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "list"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            installed = result.stdout.lower()
            all_libs = get_all_libraries()
            
            installed_libs = []
            missing_libs = []
            
            for lib in all_libs:
                if lib.lower() in installed:
                    installed_libs.append(lib)
                else:
                    missing_libs.append(lib)
            
            print(f"✅ مثبت: {len(installed_libs)}/{len(all_libs)}")
            print(f"❌ مفقود: {len(missing_libs)}")
            
            if missing_libs:
                print(f"\n📋 المكتبات المفقودة:")
                for lib in missing_libs[:10]:  # أول 10 فقط
                    print(f"  • {lib}")
                if len(missing_libs) > 10:
                    print(f"  ... و {len(missing_libs) - 10} أخرى")
            
            return installed_libs, missing_libs
        else:
            print("❌ فشل في فحص المكتبات المثبتة")
            return [], []
            
    except Exception as e:
        print(f"❌ خطأ في فحص المكتبات: {e}")
        return [], []

def create_requirements_file():
    """إنشاء ملف requirements.txt"""
    print("📄 إنشاء ملف requirements.txt...")
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write("# المكتبات المطلوبة لتطبيق منشئ المحتوى الذكي\n")
            f.write("# Smart Content Creator Required Libraries\n\n")
            
            for category, packages in REQUIRED_LIBRARIES.items():
                f.write(f"# {category.replace('_', ' ').title()}\n")
                for package in packages:
                    f.write(f"{package}\n")
                f.write("\n")
        
        print("✅ تم إنشاء requirements.txt")
        
        # إنشاء ملف للمكتبات الأساسية فقط
        with open("requirements_essential.txt", "w", encoding="utf-8") as f:
            f.write("# المكتبات الأساسية لتطبيق منشئ المحتوى الذكي\n")
            f.write("# Essential Libraries for Smart Content Creator\n\n")
            
            for package in get_essential_libraries():
                f.write(f"{package}\n")
        
        print("✅ تم إنشاء requirements_essential.txt")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملفات requirements: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎯 أداة تثبيت مكتبات منشئ المحتوى الذكي")
    print("Smart Content Creator Libraries Installer")
    print("=" * 60)
    
    while True:
        print("\n📋 الخيارات المتاحة:")
        print("1. تثبيت المكتبات الأساسية فقط (سريع)")
        print("2. تثبيت جميع المكتبات (شامل)")
        print("3. تثبيت حسب الفئة")
        print("4. فحص المكتبات المثبتة")
        print("5. إنشاء ملفات requirements")
        print("6. عرض قائمة المكتبات")
        print("0. خروج")
        
        try:
            choice = input("\nاختر رقم الخيار: ").strip()
            
            if choice == "0":
                print("👋 وداعاً!")
                break
            elif choice == "1":
                install_essential_only()
            elif choice == "2":
                install_all_libraries()
            elif choice == "3":
                install_by_category()
            elif choice == "4":
                check_installed_packages()
            elif choice == "5":
                create_requirements_file()
            elif choice == "6":
                subprocess.run([sys.executable, "required_libraries.py"])
            else:
                print("❌ اختيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n\n⚠️ تم إيقاف العملية بواسطة المستخدم")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
