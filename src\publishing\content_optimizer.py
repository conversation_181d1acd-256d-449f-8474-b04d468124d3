#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن المحتوى - Content Optimizer
يقوم بتحسين المحتوى للمنصات الاجتماعية
"""

import logging
import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import json
import os
from pathlib import Path
import moviepy.editor as mp

class OptimizationResult:
    """نتيجة التحسين"""

    def __init__(self):
        self.optimized_video_path = None
        self.optimized_thumbnail_path = None
        self.recommended_title = ""
        self.recommended_description = ""
        self.recommended_hashtags = []
        self.optimal_posting_time = None
        self.quality_score = 0.0
        self.engagement_prediction = 0.0
        self.optimization_applied = []
        self.warnings = []
        self.suggestions = []

        # تفاصيل التحسين
        self.video_optimizations = {
            "resolution_adjusted": False,
            "aspect_ratio_corrected": False,
            "duration_optimized": False,
            "audio_enhanced": False,
            "brightness_adjusted": False,
            "contrast_enhanced": False,
            "saturation_boosted": False,
            "stabilization_applied": False
        }

        self.content_optimizations = {
            "title_optimized": False,
            "description_enhanced": False,
            "hashtags_generated": False,
            "thumbnail_created": False,
            "posting_time_calculated": False
        }

    def to_dict(self) -> Dict[str, Any]:
        return {
            "optimized_video_path": self.optimized_video_path,
            "optimized_thumbnail_path": self.optimized_thumbnail_path,
            "recommended_title": self.recommended_title,
            "recommended_description": self.recommended_description,
            "recommended_hashtags": self.recommended_hashtags,
            "optimal_posting_time": self.optimal_posting_time.isoformat() if self.optimal_posting_time else None,
            "quality_score": self.quality_score,
            "engagement_prediction": self.engagement_prediction,
            "optimization_applied": self.optimization_applied,
            "warnings": self.warnings,
            "suggestions": self.suggestions,
            "video_optimizations": self.video_optimizations,
            "content_optimizations": self.content_optimizations
        }

class ContentOptimizer:
    """محسن المحتوى"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

        # تحميل إعدادات التحسين
        self.settings = self._load_optimization_settings()

        # معايير المنصات
        self.platform_specs = self._load_platform_specifications()

        # قواعد التحسين
        self.optimization_rules = self._load_optimization_rules()

        # إحصائيات
        self.stats = {
            "videos_optimized": 0,
            "average_quality_improvement": 0.0,
            "average_engagement_prediction": 0.0,
            "most_common_optimizations": {}
        }

    def _load_optimization_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات التحسين"""
        try:
            return self.config_manager.get_setting("publishing_settings", "content_optimizer", {
                "auto_optimize_video": True,
                "auto_optimize_audio": True,
                "auto_generate_thumbnail": True,
                "auto_enhance_colors": True,
                "auto_stabilize": False,
                "target_quality": "high",  # low, medium, high, ultra
                "max_file_size_mb": 500,
                "target_duration_range": [15, 60],  # ثواني
                "aspect_ratio_preference": "9:16",  # 16:9, 9:16, 1:1
                "resolution_preference": "1080p",  # 720p, 1080p, 4k
                "frame_rate_preference": 30,
                "audio_bitrate": 128,  # kbps
                "video_bitrate": 5000,  # kbps
                "enable_ai_enhancement": True,
                "preserve_original": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات التحسين: {str(e)}")
            return {
                "auto_optimize_video": True,
                "auto_optimize_audio": True,
                "target_quality": "high",
                "max_file_size_mb": 500
            }

    def _load_platform_specifications(self) -> Dict[str, Any]:
        """تحميل مواصفات المنصات"""
        return {
            "tiktok": {
                "max_duration": 180,  # ثانية
                "min_duration": 15,
                "recommended_duration": [15, 60],
                "max_file_size": 500 * 1024 * 1024,  # 500MB
                "supported_formats": ["mp4", "mov", "avi"],
                "recommended_resolution": [(1080, 1920), (720, 1280)],
                "aspect_ratios": ["9:16", "1:1"],
                "max_frame_rate": 60,
                "recommended_frame_rate": 30,
                "audio_requirements": {
                    "sample_rate": 44100,
                    "bitrate": 128,
                    "channels": 2
                },
                "title_max_length": 100,
                "description_max_length": 2200,
                "hashtags_max_count": 30
            },
            "instagram": {
                "max_duration": 90,
                "min_duration": 3,
                "recommended_duration": [15, 30],
                "max_file_size": 100 * 1024 * 1024,  # 100MB
                "supported_formats": ["mp4", "mov"],
                "recommended_resolution": [(1080, 1920), (1080, 1080)],
                "aspect_ratios": ["9:16", "1:1", "4:5"],
                "max_frame_rate": 30,
                "recommended_frame_rate": 30
            },
            "youtube_shorts": {
                "max_duration": 60,
                "min_duration": 15,
                "recommended_duration": [15, 60],
                "max_file_size": 2 * 1024 * 1024 * 1024,  # 2GB
                "supported_formats": ["mp4", "mov", "avi", "wmv"],
                "recommended_resolution": [(1080, 1920), (720, 1280)],
                "aspect_ratios": ["9:16"],
                "max_frame_rate": 60,
                "recommended_frame_rate": 30
            }
        }

    def _load_optimization_rules(self) -> Dict[str, Any]:
        """تحميل قواعد التحسين"""
        return {
            "video_quality": {
                "min_resolution": (720, 1280),
                "preferred_resolution": (1080, 1920),
                "min_bitrate": 2000,  # kbps
                "preferred_bitrate": 5000,
                "min_frame_rate": 24,
                "preferred_frame_rate": 30,
                "max_frame_rate": 60
            },
            "audio_quality": {
                "min_sample_rate": 22050,
                "preferred_sample_rate": 44100,
                "min_bitrate": 96,  # kbps
                "preferred_bitrate": 128,
                "channels": 2
            },
            "content_rules": {
                "hook_duration": 3,  # ثواني - مدة الخطاف
                "min_engagement_elements": 2,  # عدد عناصر التفاعل المطلوبة
                "optimal_text_overlay_duration": 2,  # ثواني
                "max_scene_duration": 5,  # ثواني
                "min_scene_changes": 3  # عدد تغييرات المشهد
            },
            "color_enhancement": {
                "brightness_boost": 10,  # نسبة مئوية
                "contrast_boost": 15,
                "saturation_boost": 20,
                "vibrance_boost": 10
            },
            "engagement_factors": {
                "face_presence": 1.5,  # معامل الوجوه
                "motion_intensity": 1.3,  # معامل الحركة
                "color_vibrancy": 1.2,  # معامل الألوان
                "audio_energy": 1.4,  # معامل طاقة الصوت
                "text_overlay": 1.1,  # معامل النص
                "scene_variety": 1.3  # معامل تنوع المشاهد
            }
        }

    def optimize_content(self, video_path: str, platform: str = "tiktok", ai_analysis: Dict[str, Any] = None) -> OptimizationResult:
        """تحسين المحتوى للمنصة المحددة"""
        try:
            self.logger.info(f"بدء تحسين المحتوى للمنصة: {platform}")

            result = OptimizationResult()

            # التحقق من وجود الملف
            if not os.path.exists(video_path):
                result.warnings.append(f"الملف غير موجود: {video_path}")
                return result

            # تحليل الفيديو الحالي
            video_analysis = self._analyze_video(video_path)
            if not video_analysis:
                result.warnings.append("فشل في تحليل الفيديو")
                return result

            # الحصول على مواصفات المنصة
            platform_spec = self.platform_specs.get(platform, self.platform_specs["tiktok"])

            # تحسين الفيديو
            optimized_video = self._optimize_video(video_path, platform_spec, video_analysis, result)
            if optimized_video:
                result.optimized_video_path = optimized_video
                result.optimization_applied.append("video_optimization")

            # إنشاء صورة مصغرة محسنة
            thumbnail = self._generate_optimized_thumbnail(result.optimized_video_path or video_path, ai_analysis, result)
            if thumbnail:
                result.optimized_thumbnail_path = thumbnail
                result.optimization_applied.append("thumbnail_generation")
                result.content_optimizations["thumbnail_created"] = True

            # تحسين المحتوى النصي
            self._optimize_text_content(ai_analysis, platform_spec, result)

            # حساب الوقت الأمثل للنشر
            optimal_time = self._calculate_optimal_posting_time(ai_analysis, platform)
            if optimal_time:
                result.optimal_posting_time = optimal_time
                result.content_optimizations["posting_time_calculated"] = True

            # حساب نقاط الجودة والتفاعل المتوقع
            result.quality_score = self._calculate_quality_score(video_analysis, platform_spec)
            result.engagement_prediction = self._predict_engagement(video_analysis, ai_analysis, platform)

            # إضافة اقتراحات
            self._generate_suggestions(video_analysis, ai_analysis, platform_spec, result)

            # تحديث الإحصائيات
            self.stats["videos_optimized"] += 1
            self._update_optimization_stats(result)

            self.logger.info(f"تم تحسين المحتوى بنجاح - نقاط الجودة: {result.quality_score:.1f}")
            return result

        except Exception as e:
            self.logger.error(f"خطأ في تحسين المحتوى: {str(e)}")
            result = OptimizationResult()
            result.warnings.append(f"خطأ في التحسين: {str(e)}")
            return result

    def _analyze_video(self, video_path: str) -> Optional[Dict[str, Any]]:
        """تحليل الفيديو"""
        try:
            # تحميل الفيديو
            video = mp.VideoFileClip(video_path)

            # معلومات أساسية
            analysis = {
                "duration": video.duration,
                "fps": video.fps,
                "size": video.size,
                "aspect_ratio": video.size[0] / video.size[1] if video.size[1] > 0 else 1,
                "has_audio": video.audio is not None,
                "file_size": os.path.getsize(video_path)
            }

            # تحليل الإطارات
            frame_analysis = self._analyze_frames(video)
            analysis.update(frame_analysis)

            # تحليل الصوت
            if video.audio:
                audio_analysis = self._analyze_audio(video.audio)
                analysis.update(audio_analysis)

            video.close()
            return analysis

        except Exception as e:
            self.logger.error(f"خطأ في تحليل الفيديو: {str(e)}")
            return None

    def _analyze_frames(self, video: mp.VideoFileClip) -> Dict[str, Any]:
        """تحليل إطارات الفيديو"""
        try:
            analysis = {
                "brightness_avg": 0.0,
                "contrast_avg": 0.0,
                "saturation_avg": 0.0,
                "motion_intensity": 0.0,
                "scene_changes": 0,
                "face_detection_score": 0.0,
                "color_vibrancy": 0.0
            }

            # أخذ عينات من الإطارات
            sample_times = np.linspace(0, video.duration - 0.1, min(10, int(video.duration)))

            brightness_values = []
            contrast_values = []
            saturation_values = []
            previous_frame = None
            motion_values = []

            for t in sample_times:
                frame = video.get_frame(t)

                # تحويل إلى BGR للمعالجة
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                # حساب السطوع
                gray = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2GRAY)
                brightness = np.mean(gray)
                brightness_values.append(brightness)

                # حساب التباين
                contrast = np.std(gray)
                contrast_values.append(contrast)

                # حساب التشبع
                hsv = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2HSV)
                saturation = np.mean(hsv[:, :, 1])
                saturation_values.append(saturation)

                # حساب الحركة
                if previous_frame is not None:
                    diff = cv2.absdiff(gray, previous_frame)
                    motion = np.mean(diff)
                    motion_values.append(motion)

                previous_frame = gray

            # حساب المتوسطات
            analysis["brightness_avg"] = np.mean(brightness_values)
            analysis["contrast_avg"] = np.mean(contrast_values)
            analysis["saturation_avg"] = np.mean(saturation_values)
            analysis["motion_intensity"] = np.mean(motion_values) if motion_values else 0

            # حساب حيوية الألوان
            analysis["color_vibrancy"] = (analysis["saturation_avg"] / 255.0) * 100

            return analysis

        except Exception as e:
            self.logger.error(f"خطأ في تحليل الإطارات: {str(e)}")
            return {}

    def _analyze_audio(self, audio: mp.AudioFileClip) -> Dict[str, Any]:
        """تحليل الصوت"""
        try:
            analysis = {
                "audio_duration": audio.duration,
                "sample_rate": audio.fps,
                "channels": audio.nchannels if hasattr(audio, 'nchannels') else 2,
                "audio_energy": 0.0,
                "volume_consistency": 0.0,
                "silence_ratio": 0.0
            }

            # أخذ عينة من الصوت
            sample_duration = min(10, audio.duration)
            audio_array = audio.subclip(0, sample_duration).to_soundarray()

            if len(audio_array) > 0:
                # حساب طاقة الصوت
                energy = np.mean(np.abs(audio_array))
                analysis["audio_energy"] = float(energy)

                # حساب ثبات مستوى الصوت
                rms_values = []
                chunk_size = int(audio.fps * 0.1)  # 0.1 ثانية

                for i in range(0, len(audio_array), chunk_size):
                    chunk = audio_array[i:i+chunk_size]
                    if len(chunk) > 0:
                        rms = np.sqrt(np.mean(chunk**2))
                        rms_values.append(rms)

                if rms_values:
                    analysis["volume_consistency"] = 1.0 - (np.std(rms_values) / (np.mean(rms_values) + 1e-6))

                # حساب نسبة الصمت
                silence_threshold = np.max(np.abs(audio_array)) * 0.01
                silence_samples = np.sum(np.abs(audio_array) < silence_threshold)
                analysis["silence_ratio"] = silence_samples / len(audio_array)

            return analysis

        except Exception as e:
            self.logger.error(f"خطأ في تحليل الصوت: {str(e)}")
            return {}

    def _optimize_video(self, video_path: str, platform_spec: Dict[str, Any],
                       video_analysis: Dict[str, Any], result: OptimizationResult) -> Optional[str]:
        """تحسين الفيديو"""
        try:
            video = mp.VideoFileClip(video_path)
            optimizations_applied = []

            # تحديد المسار المحسن
            output_dir = os.path.join(os.path.dirname(video_path), "optimized")
            os.makedirs(output_dir, exist_ok=True)

            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(output_dir, f"{base_name}_optimized.mp4")

            # تحسين الدقة ونسبة العرض إلى الارتفاع
            target_resolution = platform_spec["recommended_resolution"][0]
            if video.size != target_resolution:
                video = video.resize(target_resolution)
                optimizations_applied.append("resolution_adjustment")
                result.video_optimizations["resolution_adjusted"] = True

            # تحسين المدة
            max_duration = platform_spec["max_duration"]
            if video.duration > max_duration:
                video = video.subclip(0, max_duration)
                optimizations_applied.append("duration_trimming")
                result.video_optimizations["duration_optimized"] = True

            # تحسين معدل الإطارات
            target_fps = platform_spec["recommended_frame_rate"]
            if abs(video.fps - target_fps) > 1:
                video = video.set_fps(target_fps)
                optimizations_applied.append("fps_adjustment")

            # تحسين الألوان إذا كان مطلوباً
            if self.settings.get("auto_enhance_colors", True):
                video = self._enhance_colors(video, video_analysis, result)
                optimizations_applied.append("color_enhancement")

            # تحسين الصوت إذا كان موجوداً
            if video.audio and self.settings.get("auto_optimize_audio", True):
                video = self._enhance_audio(video, result)
                optimizations_applied.append("audio_enhancement")

            # تصدير الفيديو المحسن
            video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                bitrate=f"{self.settings.get('video_bitrate', 5000)}k",
                audio_bitrate=f"{self.settings.get('audio_bitrate', 128)}k",
                verbose=False,
                logger=None
            )

            video.close()

            result.optimization_applied.extend(optimizations_applied)
            self.logger.info(f"تم تحسين الفيديو: {len(optimizations_applied)} تحسين مطبق")

            return output_path

        except Exception as e:
            self.logger.error(f"خطأ في تحسين الفيديو: {str(e)}")
            return None

    def _enhance_colors(self, video: mp.VideoFileClip, video_analysis: Dict[str, Any],
                       result: OptimizationResult) -> mp.VideoFileClip:
        """تحسين الألوان"""
        try:
            rules = self.optimization_rules["color_enhancement"]

            # تحديد مقدار التحسين المطلوب
            brightness_boost = 0
            contrast_boost = 0
            saturation_boost = 0

            # تحسين السطوع
            if video_analysis.get("brightness_avg", 128) < 100:
                brightness_boost = rules["brightness_boost"]
                result.video_optimizations["brightness_adjusted"] = True

            # تحسين التباين
            if video_analysis.get("contrast_avg", 50) < 40:
                contrast_boost = rules["contrast_boost"]
                result.video_optimizations["contrast_enhanced"] = True

            # تحسين التشبع
            if video_analysis.get("color_vibrancy", 50) < 40:
                saturation_boost = rules["saturation_boost"]
                result.video_optimizations["saturation_boosted"] = True

            # تطبيق التحسينات
            if brightness_boost > 0 or contrast_boost > 0 or saturation_boost > 0:
                def color_enhance(get_frame, t):
                    frame = get_frame(t)

                    # تحويل إلى HSV للتحكم في التشبع
                    hsv = cv2.cvtColor(frame, cv2.COLOR_RGB2HSV).astype(np.float32)

                    # تحسين السطوع
                    if brightness_boost > 0:
                        hsv[:, :, 2] = np.clip(hsv[:, :, 2] * (1 + brightness_boost/100), 0, 255)

                    # تحسين التشبع
                    if saturation_boost > 0:
                        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * (1 + saturation_boost/100), 0, 255)

                    # تحويل إلى RGB
                    enhanced = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)

                    # تحسين التباين
                    if contrast_boost > 0:
                        enhanced = np.clip(enhanced * (1 + contrast_boost/100), 0, 255).astype(np.uint8)

                    return enhanced

                video = video.fl(color_enhance)

            return video

        except Exception as e:
            self.logger.error(f"خطأ في تحسين الألوان: {str(e)}")
            return video

    def _enhance_audio(self, video: mp.VideoFileClip, result: OptimizationResult) -> mp.VideoFileClip:
        """تحسين الصوت"""
        try:
            if not video.audio:
                return video

            audio = video.audio

            # تطبيع مستوى الصوت
            audio = audio.fx(mp.afx.audio_normalize)

            # تحسين جودة الصوت
            audio = audio.set_fps(self.optimization_rules["audio_quality"]["preferred_sample_rate"])

            # إرفاق الصوت المحسن بالفيديو
            video = video.set_audio(audio)

            result.video_optimizations["audio_enhanced"] = True

            return video

        except Exception as e:
            self.logger.error(f"خطأ في تحسين الصوت: {str(e)}")
            return video

    def _generate_optimized_thumbnail(self, video_path: str, ai_analysis: Dict[str, Any],
                                    result: OptimizationResult) -> Optional[str]:
        """إنشاء صورة مصغرة محسنة"""
        try:
            video = mp.VideoFileClip(video_path)

            # تحديد أفضل وقت للصورة المصغرة
            thumbnail_time = self._find_best_thumbnail_time(video, ai_analysis)

            # استخراج الإطار
            frame = video.get_frame(thumbnail_time)

            # تحسين الصورة
            enhanced_frame = self._enhance_thumbnail_image(frame)

            # حفظ الصورة المصغرة
            output_dir = os.path.join(os.path.dirname(video_path), "thumbnails")
            os.makedirs(output_dir, exist_ok=True)

            base_name = os.path.splitext(os.path.basename(video_path))[0]
            thumbnail_path = os.path.join(output_dir, f"{base_name}_thumbnail.jpg")

            cv2.imwrite(thumbnail_path, cv2.cvtColor(enhanced_frame, cv2.COLOR_RGB2BGR))

            video.close()

            self.logger.info(f"تم إنشاء صورة مصغرة محسنة: {thumbnail_path}")
            return thumbnail_path

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة: {str(e)}")
            return None

    def _find_best_thumbnail_time(self, video: mp.VideoFileClip, ai_analysis: Dict[str, Any]) -> float:
        """العثور على أفضل وقت للصورة المصغرة"""
        try:
            # إذا كان لدينا تحليل AI، استخدم أفضل اللحظات
            if ai_analysis and "best_moments" in ai_analysis:
                best_moments = ai_analysis["best_moments"]
                if best_moments:
                    # اختيار أفضل لحظة في الثلث الأول من الفيديو
                    first_third = video.duration / 3
                    for moment in best_moments:
                        if moment.get("timestamp", 0) <= first_third:
                            return moment["timestamp"]

            # إذا لم يكن لدينا تحليل، استخدم نقطة في الثلث الأول
            return min(video.duration * 0.2, 3.0)  # 20% من الفيديو أو 3 ثواني

        except Exception as e:
            self.logger.error(f"خطأ في تحديد وقت الصورة المصغرة: {str(e)}")
            return min(video.duration * 0.2, 3.0)

    def _enhance_thumbnail_image(self, frame: np.ndarray) -> np.ndarray:
        """تحسين صورة الصورة المصغرة"""
        try:
            # تحسين السطوع والتباين
            enhanced = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)

            # تحسين الحدة
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            enhanced = cv2.filter2D(enhanced, -1, kernel)

            # تحسين التشبع
            hsv = cv2.cvtColor(enhanced, cv2.COLOR_RGB2HSV)
            hsv[:, :, 1] = cv2.multiply(hsv[:, :, 1], 1.2)
            enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)

            return np.clip(enhanced, 0, 255).astype(np.uint8)

        except Exception as e:
            self.logger.error(f"خطأ في تحسين الصورة المصغرة: {str(e)}")
            return frame

    def _optimize_text_content(self, ai_analysis: Dict[str, Any], platform_spec: Dict[str, Any],
                             result: OptimizationResult):
        """تحسين المحتوى النصي"""
        try:
            # تحسين العنوان
            if ai_analysis and "transcription" in ai_analysis:
                transcription = ai_analysis["transcription"]
                result.recommended_title = self._generate_optimized_title(transcription, platform_spec)
                result.content_optimizations["title_optimized"] = True

            # تحسين الوصف
            if ai_analysis:
                result.recommended_description = self._generate_optimized_description(ai_analysis, platform_spec)
                result.content_optimizations["description_enhanced"] = True

            # إنشاء هاشتاغات
            if ai_analysis:
                result.recommended_hashtags = self._generate_optimized_hashtags(ai_analysis, platform_spec)
                result.content_optimizations["hashtags_generated"] = True

        except Exception as e:
            self.logger.error(f"خطأ في تحسين المحتوى النصي: {str(e)}")

    def _generate_optimized_title(self, transcription: Dict[str, Any], platform_spec: Dict[str, Any]) -> str:
        """إنشاء عنوان محسن"""
        try:
            max_length = platform_spec.get("title_max_length", 100)

            # استخراج الكلمات المفتاحية من النسخ النصي
            text = transcription.get("text", "")
            if not text:
                return "فيديو مثير ومضحك 😂"

            # تنظيف النص
            words = text.split()[:15]  # أول 15 كلمة

            # إنشاء عنوان جذاب
            title_templates = [
                "شاهد هذا المقطع المضحك! 😂",
                "لن تصدق ما حدث! 😱",
                "مقطع فيروسي مضحك 🔥",
                "هذا المقطع سيجعلك تضحك! 😄",
                "مقطع مثير ومضحك 🤣"
            ]

            # اختيار قالب عشوائي
            import random
            base_title = random.choice(title_templates)

            # إضافة كلمات مفتاحية إذا كان هناك مساحة
            if len(base_title) < max_length - 20 and words:
                key_words = " ".join(words[:3])
                title = f"{base_title} {key_words}"
            else:
                title = base_title

            # قطع العنوان إذا كان طويلاً
            if len(title) > max_length:
                title = title[:max_length-3] + "..."

            return title

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء العنوان: {str(e)}")
            return "فيديو مثير ومضحك 😂"

    def _generate_optimized_description(self, ai_analysis: Dict[str, Any], platform_spec: Dict[str, Any]) -> str:
        """إنشاء وصف محسن"""
        try:
            max_length = platform_spec.get("description_max_length", 2200)

            description_parts = []

            # إضافة وصف جذاب
            hooks = [
                "🔥 مقطع فيروسي لا يُفوت!",
                "😂 ستضحك حتى البكاء!",
                "🤣 أفضل مقطع ستشاهده اليوم!",
                "💯 محتوى حصري ومضحك!",
                "⚡ مقطع سيجعل يومك أفضل!"
            ]

            import random
            description_parts.append(random.choice(hooks))

            # إضافة وصف المحتوى إذا كان متاحاً
            if "transcription" in ai_analysis:
                text = ai_analysis["transcription"].get("text", "")
                if text:
                    # أخذ أول جملتين
                    sentences = text.split('.')[:2]
                    content_desc = '. '.join(sentences).strip()
                    if content_desc:
                        description_parts.append(f"\n\n📝 {content_desc}")

            # إضافة دعوة للعمل
            cta_options = [
                "\n\n👍 لا تنس الإعجاب والمتابعة للمزيد!",
                "\n\n❤️ أعجبك المقطع؟ شاركه مع أصدقائك!",
                "\n\n🔔 فعّل الجرس ليصلك كل جديد!",
                "\n\n💬 شاركنا رأيك في التعليقات!"
            ]

            description_parts.append(random.choice(cta_options))

            # إضافة هاشتاغات شائعة
            trending_hashtags = [
                "#مضحك #فيروسي #ترند #مقاطع_مضحكة #كوميديا",
                "#ضحك #مرح #تسلية #فيديو_مضحك #محتوى_عربي",
                "#مقطع_مضحك #ترفيه #كوميدي #مضحك_جداً #فيروسي"
            ]

            description_parts.append(f"\n\n{random.choice(trending_hashtags)}")

            # دمج الأجزاء
            description = "".join(description_parts)

            # قطع الوصف إذا كان طويلاً
            if len(description) > max_length:
                description = description[:max_length-3] + "..."

            return description

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الوصف: {str(e)}")
            return "🔥 مقطع فيروسي مضحك! 😂 #مضحك #فيروسي #ترند"

    def _generate_optimized_hashtags(self, ai_analysis: Dict[str, Any], platform_spec: Dict[str, Any]) -> List[str]:
        """إنشاء هاشتاغات محسنة"""
        try:
            max_hashtags = platform_spec.get("hashtags_max_count", 30)

            hashtags = []

            # هاشتاغات أساسية
            base_hashtags = [
                "#مضحك", "#فيروسي", "#ترند", "#كوميديا", "#ضحك",
                "#مرح", "#تسلية", "#ترفيه", "#مقاطع_مضحكة", "#محتوى_عربي"
            ]

            hashtags.extend(base_hashtags[:10])

            # هاشتاغات بناءً على المحتوى
            if "emotions" in ai_analysis:
                emotions = ai_analysis["emotions"]
                if "joy" in emotions or "happiness" in emotions:
                    hashtags.extend(["#سعادة", "#فرح", "#بهجة"])
                if "surprise" in emotions:
                    hashtags.extend(["#مفاجأة", "#صدمة"])

            # هاشتاغات بناءً على الكلمات المفتاحية
            if "keywords" in ai_analysis:
                keywords = ai_analysis["keywords"][:5]  # أول 5 كلمات
                for keyword in keywords:
                    if len(keyword) > 2:  # تجنب الكلمات القصيرة
                        hashtags.append(f"#{keyword}")

            # هاشتاغات شائعة إضافية
            trending_hashtags = [
                "#fyp", "#foryou", "#viral", "#funny", "#comedy",
                "#laugh", "#entertainment", "#fun", "#hilarious", "#lol"
            ]

            # إضافة هاشتاغات شائعة حتى الوصول للحد الأقصى
            for tag in trending_hashtags:
                if len(hashtags) >= max_hashtags:
                    break
                if tag not in hashtags:
                    hashtags.append(tag)

            return hashtags[:max_hashtags]

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الهاشتاغات: {str(e)}")
            return ["#مضحك", "#فيروسي", "#ترند", "#كوميديا", "#ضحك"]

    def _calculate_optimal_posting_time(self, ai_analysis: Dict[str, Any], platform: str) -> Optional[datetime]:
        """حساب الوقت الأمثل للنشر"""
        try:
            # الحصول على الأوقات المثلى للمنصة
            optimal_times = self.settings.get("optimal_posting_times", [])

            if not optimal_times:
                # أوقات افتراضية إذا لم تكن محددة
                optimal_times = [
                    {"day": "friday", "hours": [18, 19, 20]},
                    {"day": "saturday", "hours": [17, 18, 19]},
                    {"day": "sunday", "hours": [17, 18, 19]}
                ]

            # العثور على أقرب وقت مثالي
            now = datetime.now()
            best_time = None
            min_wait = float('inf')

            for day_config in optimal_times:
                day_name = day_config["day"]
                hours = day_config["hours"]

                # تحويل اسم اليوم إلى رقم
                day_mapping = {
                    "monday": 0, "tuesday": 1, "wednesday": 2, "thursday": 3,
                    "friday": 4, "saturday": 5, "sunday": 6
                }

                target_day = day_mapping.get(day_name, 4)  # افتراضي: الجمعة

                for hour in hours:
                    # حساب التاريخ والوقت المستهدف
                    days_ahead = target_day - now.weekday()
                    if days_ahead <= 0:  # اليوم المستهدف في الأسبوع القادم
                        days_ahead += 7

                    target_time = now + timedelta(days=days_ahead)
                    target_time = target_time.replace(hour=hour, minute=0, second=0, microsecond=0)

                    # التأكد من أن الوقت في المستقبل
                    if target_time > now:
                        wait_time = (target_time - now).total_seconds()
                        if wait_time < min_wait:
                            min_wait = wait_time
                            best_time = target_time

            # إذا لم نجد وقتاً مناسباً، استخدم وقتاً افتراضياً
            if not best_time:
                best_time = now + timedelta(hours=2)  # بعد ساعتين

            return best_time

        except Exception as e:
            self.logger.error(f"خطأ في حساب الوقت الأمثل: {str(e)}")
            return datetime.now() + timedelta(hours=2)

    def _calculate_quality_score(self, video_analysis: Dict[str, Any], platform_spec: Dict[str, Any]) -> float:
        """حساب نقاط الجودة"""
        try:
            score = 0.0
            max_score = 100.0

            # نقاط الدقة (20 نقطة)
            target_resolution = platform_spec["recommended_resolution"][0]
            video_size = video_analysis.get("size", (720, 1280))

            if video_size == target_resolution:
                score += 20
            elif min(video_size) >= min(target_resolution):
                score += 15
            else:
                score += 10

            # نقاط المدة (15 نقطة)
            duration = video_analysis.get("duration", 0)
            recommended_duration = platform_spec.get("recommended_duration", [15, 60])

            if recommended_duration[0] <= duration <= recommended_duration[1]:
                score += 15
            elif duration < recommended_duration[0]:
                score += 10
            else:
                score += 12

            # نقاط جودة الصوت (15 نقطة)
            if video_analysis.get("has_audio", False):
                audio_energy = video_analysis.get("audio_energy", 0)
                volume_consistency = video_analysis.get("volume_consistency", 0)

                if audio_energy > 0.1:
                    score += 8
                if volume_consistency > 0.7:
                    score += 7
            else:
                score += 5  # نقاط أقل للفيديوهات بدون صوت

            # نقاط جودة الصورة (20 نقطة)
            brightness = video_analysis.get("brightness_avg", 128)
            contrast = video_analysis.get("contrast_avg", 50)
            color_vibrancy = video_analysis.get("color_vibrancy", 50)

            # تقييم السطوع (80-180 مثالي)
            if 80 <= brightness <= 180:
                score += 7
            else:
                score += 4

            # تقييم التباين (> 30 جيد)
            if contrast > 30:
                score += 7
            else:
                score += 4

            # تقييم حيوية الألوان (> 40 جيد)
            if color_vibrancy > 40:
                score += 6
            else:
                score += 3

            # نقاط الحركة والديناميكية (15 نقطة)
            motion_intensity = video_analysis.get("motion_intensity", 0)
            scene_changes = video_analysis.get("scene_changes", 0)

            if motion_intensity > 10:
                score += 8
            elif motion_intensity > 5:
                score += 6
            else:
                score += 3

            if scene_changes > 2:
                score += 7
            else:
                score += 4

            # نقاط إضافية للمميزات الخاصة (15 نقطة)
            face_score = video_analysis.get("face_detection_score", 0)
            if face_score > 0.5:
                score += 8

            # نقاط للنسبة الذهبية للمدة
            if 20 <= duration <= 45:  # المدة المثلى للمحتوى الفيروسي
                score += 7

            return min(score, max_score)

        except Exception as e:
            self.logger.error(f"خطأ في حساب نقاط الجودة: {str(e)}")
            return 50.0

    def _predict_engagement(self, video_analysis: Dict[str, Any], ai_analysis: Dict[str, Any], platform: str) -> float:
        """توقع معدل التفاعل"""
        try:
            engagement_score = 0.0
            factors = self.optimization_rules["engagement_factors"]

            # عامل وجود الوجوه
            face_score = video_analysis.get("face_detection_score", 0)
            engagement_score += face_score * factors["face_presence"]

            # عامل شدة الحركة
            motion_intensity = video_analysis.get("motion_intensity", 0)
            normalized_motion = min(motion_intensity / 20, 1.0)  # تطبيع إلى 0-1
            engagement_score += normalized_motion * factors["motion_intensity"]

            # عامل حيوية الألوان
            color_vibrancy = video_analysis.get("color_vibrancy", 0)
            normalized_vibrancy = min(color_vibrancy / 100, 1.0)
            engagement_score += normalized_vibrancy * factors["color_vibrancy"]

            # عامل طاقة الصوت
            audio_energy = video_analysis.get("audio_energy", 0)
            normalized_audio = min(audio_energy / 0.5, 1.0)
            engagement_score += normalized_audio * factors["audio_energy"]

            # عامل تنوع المشاهد
            scene_changes = video_analysis.get("scene_changes", 0)
            normalized_scenes = min(scene_changes / 5, 1.0)
            engagement_score += normalized_scenes * factors["scene_variety"]

            # عوامل من تحليل الذكاء الاصطناعي
            if ai_analysis:
                # عامل المشاعر الإيجابية
                emotions = ai_analysis.get("emotions", {})
                positive_emotions = emotions.get("joy", 0) + emotions.get("excitement", 0)
                engagement_score += min(positive_emotions / 100, 1.0) * 1.5

                # عامل الكلمات المفتاحية الشائعة
                keywords = ai_analysis.get("keywords", [])
                viral_keywords = ["مضحك", "مفاجأة", "لا يصدق", "رائع", "مذهل"]
                keyword_score = sum(1 for kw in keywords if any(vk in kw for vk in viral_keywords))
                engagement_score += min(keyword_score / 5, 1.0) * 1.2

                # عامل التوقع الفيروسي
                viral_score = ai_analysis.get("viral_prediction", {}).get("score", 0)
                engagement_score += (viral_score / 100) * 2.0

            # تطبيع النتيجة إلى نسبة مئوية
            max_possible_score = sum(factors.values()) + 4.7  # العوامل الإضافية
            engagement_percentage = min((engagement_score / max_possible_score) * 100, 100)

            return engagement_percentage

        except Exception as e:
            self.logger.error(f"خطأ في توقع التفاعل: {str(e)}")
            return 50.0

    def _generate_suggestions(self, video_analysis: Dict[str, Any], ai_analysis: Dict[str, Any],
                            platform_spec: Dict[str, Any], result: OptimizationResult):
        """إنشاء اقتراحات للتحسين"""
        try:
            suggestions = []

            # اقتراحات الفيديو
            duration = video_analysis.get("duration", 0)
            recommended_duration = platform_spec.get("recommended_duration", [15, 60])

            if duration < recommended_duration[0]:
                suggestions.append(f"يُنصح بزيادة مدة الفيديو إلى {recommended_duration[0]} ثانية على الأقل")
            elif duration > recommended_duration[1]:
                suggestions.append(f"يُنصح بتقليل مدة الفيديو إلى {recommended_duration[1]} ثانية أو أقل")

            # اقتراحات الجودة
            brightness = video_analysis.get("brightness_avg", 128)
            if brightness < 80:
                suggestions.append("الفيديو مظلم قليلاً، يُنصح بزيادة السطوع")
            elif brightness > 200:
                suggestions.append("الفيديو مضيء جداً، يُنصح بتقليل السطوع")

            contrast = video_analysis.get("contrast_avg", 50)
            if contrast < 30:
                suggestions.append("يُنصح بزيادة التباين لجعل الصورة أكثر وضوحاً")

            color_vibrancy = video_analysis.get("color_vibrancy", 50)
            if color_vibrancy < 40:
                suggestions.append("يُنصح بزيادة تشبع الألوان لجعل الفيديو أكثر جاذبية")

            # اقتراحات الصوت
            if not video_analysis.get("has_audio", False):
                suggestions.append("إضافة موسيقى خلفية أو تعليق صوتي سيزيد من جاذبية الفيديو")
            else:
                audio_energy = video_analysis.get("audio_energy", 0)
                if audio_energy < 0.05:
                    suggestions.append("مستوى الصوت منخفض، يُنصح برفع مستوى الصوت")

                volume_consistency = video_analysis.get("volume_consistency", 0)
                if volume_consistency < 0.7:
                    suggestions.append("مستوى الصوت غير ثابت، يُنصح بتطبيع مستوى الصوت")

            # اقتراحات المحتوى
            motion_intensity = video_analysis.get("motion_intensity", 0)
            if motion_intensity < 5:
                suggestions.append("الفيديو ثابت نسبياً، إضافة المزيد من الحركة سيزيد من التفاعل")

            # اقتراحات بناءً على تحليل الذكاء الاصطناعي
            if ai_analysis:
                emotions = ai_analysis.get("emotions", {})
                if emotions.get("joy", 0) < 30:
                    suggestions.append("إضافة عناصر مرحة أو مضحكة سيزيد من جاذبية المحتوى")

                if len(ai_analysis.get("keywords", [])) < 3:
                    suggestions.append("إضافة كلمات مفتاحية أكثر في العنوان والوصف")

            # اقتراحات النشر
            suggestions.append("استخدم هاشتاغات شائعة ومتعلقة بالمحتوى")
            suggestions.append("انشر في الأوقات المثلى للحصول على أكبر تفاعل")
            suggestions.append("تفاعل مع التعليقات بسرعة لزيادة معدل التفاعل")

            result.suggestions = suggestions

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الاقتراحات: {str(e)}")

    def _update_optimization_stats(self, result: OptimizationResult):
        """تحديث إحصائيات التحسين"""
        try:
            # تحديث متوسط تحسين الجودة
            current_avg = self.stats.get("average_quality_improvement", 0)
            videos_count = self.stats.get("videos_optimized", 1)

            quality_improvement = result.quality_score - 50  # افتراض أن 50 هو الأساس
            new_avg = ((current_avg * (videos_count - 1)) + quality_improvement) / videos_count
            self.stats["average_quality_improvement"] = new_avg

            # تحديث متوسط توقع التفاعل
            current_engagement_avg = self.stats.get("average_engagement_prediction", 0)
            new_engagement_avg = ((current_engagement_avg * (videos_count - 1)) + result.engagement_prediction) / videos_count
            self.stats["average_engagement_prediction"] = new_engagement_avg

            # تحديث التحسينات الأكثر شيوعاً
            common_optimizations = self.stats.get("most_common_optimizations", {})
            for optimization in result.optimization_applied:
                common_optimizations[optimization] = common_optimizations.get(optimization, 0) + 1

            self.stats["most_common_optimizations"] = common_optimizations

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def get_optimization_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحسين"""
        return self.stats.copy()

    def batch_optimize(self, video_paths: List[str], platform: str = "tiktok") -> List[OptimizationResult]:
        """تحسين مجموعة من الفيديوهات"""
        try:
            results = []

            for video_path in video_paths:
                self.logger.info(f"تحسين الفيديو: {video_path}")
                result = self.optimize_content(video_path, platform)
                results.append(result)

            self.logger.info(f"تم تحسين {len(results)} فيديو")
            return results

        except Exception as e:
            self.logger.error(f"خطأ في التحسين المجمع: {str(e)}")
            return []