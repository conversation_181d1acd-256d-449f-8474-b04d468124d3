{"performance_metrics": {"encryption_speed": {"metric": "mb_per_second", "target": 100, "alert_threshold": 50}, "decryption_speed": {"metric": "mb_per_second", "target": 120, "alert_threshold": 60}, "authentication_time": {"metric": "milliseconds", "target": 100, "alert_threshold": 500}, "integrity_check_speed": {"metric": "files_per_second", "target": 50, "alert_threshold": 20}, "malware_scan_speed": {"metric": "files_per_second", "target": 100, "alert_threshold": 30}, "backup_speed": {"metric": "mb_per_second", "target": 50, "alert_threshold": 20}}, "resource_monitoring": {"cpu_usage": {"target_max": 70, "alert_threshold": 90}, "memory_usage": {"target_max": 80, "alert_threshold": 95}, "disk_io": {"target_max": 80, "alert_threshold": 95}, "network_io": {"target_max": 70, "alert_threshold": 90}}, "security_metrics": {"failed_authentications": {"alert_threshold": 10, "time_window_minutes": 5}, "integrity_violations": {"alert_threshold": 1, "time_window_minutes": 1}, "malware_detections": {"alert_threshold": 1, "time_window_minutes": 1}, "encryption_failures": {"alert_threshold": 5, "time_window_minutes": 10}}, "monitoring_settings": {"update_interval_seconds": 5, "log_retention_days": 30, "alert_cooldown_minutes": 15, "detailed_logging": true, "real_time_alerts": true}, "alerting": {"email_alerts": false, "log_alerts": true, "dashboard_alerts": true, "severity_levels": ["low", "medium", "high", "critical"]}, "reporting": {"daily_reports": true, "weekly_summaries": true, "performance_trends": true, "security_incidents": true}}