#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات نظام جلب المحتوى
Tests for Content Fetching System
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import json
import tempfile

# استيراد الوحدات المطلوب اختبارها
from src.content_fetcher.snapchat_fetcher import SnapchatFetcher
from src.content_fetcher.tiktok_fetcher import TikTokFetcher
from src.content_fetcher.kick_fetcher import KickFetcher
from src.content_fetcher.content_manager import ContentManager

class TestSnapchatFetcher:
    """اختبارات جالب محتوى Snapchat"""
    
    @pytest.fixture
    def snapchat_fetcher(self, temp_dir):
        """إنشاء كائن SnapchatFetcher للاختبار"""
        return SnapchatFetcher(str(temp_dir))
    
    def test_initialization(self, snapchat_fetcher):
        """اختبار تهيئة النظام"""
        assert snapchat_fetcher is not None
        assert hasattr(snapchat_fetcher, 'session')
        assert hasattr(snapchat_fetcher, 'config')
    
    @patch('requests.Session.get')
    def test_fetch_user_stories_success(self, mock_get, snapchat_fetcher):
        """اختبار جلب قصص المستخدم بنجاح"""
        # إعداد الاستجابة الوهمية
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "stories": [
                {
                    "id": "story_1",
                    "url": "https://example.com/story1.mp4",
                    "timestamp": "2024-01-01T12:00:00Z",
                    "duration": 15
                }
            ]
        }
        mock_get.return_value = mock_response
        
        # تشغيل الاختبار
        result = snapchat_fetcher.fetch_user_stories("test_user")
        
        # التحقق من النتائج
        assert result is not None
        assert len(result) > 0
        mock_get.assert_called_once()
    
    @patch('requests.Session.get')
    def test_fetch_user_stories_failure(self, mock_get, snapchat_fetcher):
        """اختبار فشل جلب قصص المستخدم"""
        # إعداد استجابة فاشلة
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response
        
        # تشغيل الاختبار
        result = snapchat_fetcher.fetch_user_stories("nonexistent_user")
        
        # التحقق من النتائج
        assert result == []
    
    def test_validate_content_url(self, snapchat_fetcher):
        """اختبار التحقق من صحة رابط المحتوى"""
        valid_url = "https://story.snapchat.com/s/test_story"
        invalid_url = "https://example.com/invalid"
        
        assert snapchat_fetcher.validate_content_url(valid_url) == True
        assert snapchat_fetcher.validate_content_url(invalid_url) == False

class TestTikTokFetcher:
    """اختبارات جالب محتوى TikTok"""
    
    @pytest.fixture
    def tiktok_fetcher(self, temp_dir):
        """إنشاء كائن TikTokFetcher للاختبار"""
        return TikTokFetcher(str(temp_dir))
    
    def test_initialization(self, tiktok_fetcher):
        """اختبار تهيئة النظام"""
        assert tiktok_fetcher is not None
        assert hasattr(tiktok_fetcher, 'session')
        assert hasattr(tiktok_fetcher, 'config')
    
    @patch('requests.Session.get')
    def test_fetch_live_stream_success(self, mock_get, tiktok_fetcher):
        """اختبار جلب البث المباشر بنجاح"""
        # إعداد الاستجابة الوهمية
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "live_streams": [
                {
                    "id": "live_1",
                    "stream_url": "https://example.com/live1.m3u8",
                    "title": "بث مباشر تجريبي",
                    "viewer_count": 1000
                }
            ]
        }
        mock_get.return_value = mock_response
        
        # تشغيل الاختبار
        result = tiktok_fetcher.fetch_live_streams("test_user")
        
        # التحقق من النتائج
        assert result is not None
        assert len(result) > 0
        mock_get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_download_video_async(self, tiktok_fetcher, temp_dir):
        """اختبار تحميل الفيديو بشكل غير متزامن"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # إعداد الاستجابة الوهمية
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.read = AsyncMock(return_value=b"fake_video_data")
            mock_get.return_value.__aenter__.return_value = mock_response
            
            # تشغيل الاختبار
            video_url = "https://example.com/test_video.mp4"
            result = await tiktok_fetcher.download_video_async(video_url, str(temp_dir))
            
            # التحقق من النتائج
            assert result is not None
            assert Path(result).exists()

class TestKickFetcher:
    """اختبارات جالب محتوى Kick"""
    
    @pytest.fixture
    def kick_fetcher(self, temp_dir):
        """إنشاء كائن KickFetcher للاختبار"""
        return KickFetcher(str(temp_dir))
    
    def test_initialization(self, kick_fetcher):
        """اختبار تهيئة النظام"""
        assert kick_fetcher is not None
        assert hasattr(kick_fetcher, 'session')
        assert hasattr(kick_fetcher, 'config')
    
    @patch('websocket.WebSocket.connect')
    def test_connect_to_stream(self, mock_connect, kick_fetcher):
        """اختبار الاتصال بالبث"""
        # إعداد الاتصال الوهمي
        mock_ws = Mock()
        mock_connect.return_value = mock_ws
        
        # تشغيل الاختبار
        result = kick_fetcher.connect_to_stream("test_channel")
        
        # التحقق من النتائج
        assert result == True
        mock_connect.assert_called_once()

class TestContentManager:
    """اختبارات مدير المحتوى"""
    
    @pytest.fixture
    def content_manager(self, temp_dir):
        """إنشاء كائن ContentManager للاختبار"""
        return ContentManager(str(temp_dir))
    
    def test_initialization(self, content_manager):
        """اختبار تهيئة مدير المحتوى"""
        assert content_manager is not None
        assert hasattr(content_manager, 'snapchat_fetcher')
        assert hasattr(content_manager, 'tiktok_fetcher')
        assert hasattr(content_manager, 'kick_fetcher')
    
    def test_add_content_source(self, content_manager):
        """اختبار إضافة مصدر محتوى"""
        source_config = {
            "platform": "snapchat",
            "username": "test_user",
            "fetch_interval": 300
        }
        
        result = content_manager.add_content_source(source_config)
        assert result == True
        assert len(content_manager.content_sources) > 0
    
    def test_remove_content_source(self, content_manager):
        """اختبار إزالة مصدر محتوى"""
        # إضافة مصدر أولاً
        source_config = {
            "platform": "tiktok",
            "username": "test_user_2",
            "fetch_interval": 600
        }
        content_manager.add_content_source(source_config)
        
        # إزالة المصدر
        result = content_manager.remove_content_source("test_user_2")
        assert result == True
    
    @patch('src.content_fetcher.snapchat_fetcher.SnapchatFetcher.fetch_user_stories')
    def test_fetch_all_content(self, mock_fetch, content_manager):
        """اختبار جلب جميع المحتوى"""
        # إعداد الاستجابة الوهمية
        mock_fetch.return_value = [
            {
                "id": "content_1",
                "url": "https://example.com/content1.mp4",
                "platform": "snapchat",
                "timestamp": "2024-01-01T12:00:00Z"
            }
        ]
        
        # إضافة مصدر محتوى
        content_manager.add_content_source({
            "platform": "snapchat",
            "username": "test_user",
            "fetch_interval": 300
        })
        
        # تشغيل الاختبار
        result = content_manager.fetch_all_content()
        
        # التحقق من النتائج
        assert result is not None
        assert len(result) > 0
        mock_fetch.assert_called_once()
    
    def test_get_content_statistics(self, content_manager):
        """اختبار الحصول على إحصائيات المحتوى"""
        stats = content_manager.get_content_statistics()
        
        assert isinstance(stats, dict)
        assert "total_sources" in stats
        assert "total_content" in stats
        assert "platforms" in stats

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
