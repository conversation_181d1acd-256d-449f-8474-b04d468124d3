#!/usr/bin/env python3
"""
Basic Performance Optimizer
Simple optimization tool without external dependencies
"""

import os
import sys
import gc
import time
import json
import threading
import tracemalloc
from pathlib import Path
from datetime import datetime

class BasicOptimizer:
    def __init__(self):
        self.start_time = time.time()
        tracemalloc.start()
        
    def run_optimization(self):
        print("Starting basic optimization...")
        
        try:
            # Step 1: Analyze current state
            print("Step 1: Analyzing current state...")
            initial_state = self.analyze_state()
            
            # Step 2: Memory optimization
            print("Step 2: Optimizing memory...")
            memory_result = self.optimize_memory()
            
            # Step 3: Cleanup temp files
            print("Step 3: Cleaning up temp files...")
            cleanup_result = self.cleanup_files()
            
            # Step 4: Final analysis
            print("Step 4: Final analysis...")
            final_state = self.analyze_state()
            
            # Step 5: Generate report
            print("Step 5: Generating report...")
            report = self.generate_report(initial_state, memory_result, cleanup_result, final_state)
            
            print("Optimization completed successfully!")
            return report
            
        except Exception as e:
            print(f"Error during optimization: {e}")
            return {"error": str(e)}
    
    def analyze_state(self):
        try:
            current, peak = tracemalloc.get_traced_memory()
            thread_count = threading.active_count()
            temp_files = self.count_temp_files()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "memory_current_mb": current / 1024 / 1024,
                "memory_peak_mb": peak / 1024 / 1024,
                "thread_count": thread_count,
                "temp_files": temp_files,
                "gc_counts": gc.get_count()
            }
        except Exception as e:
            return {"error": str(e)}
    
    def optimize_memory(self):
        try:
            before_current, before_peak = tracemalloc.get_traced_memory()
            
            # Run garbage collection
            collected = []
            for generation in range(3):
                collected.append(gc.collect(generation))
            
            # Optimize GC thresholds
            gc.set_threshold(700, 10, 10)
            
            after_current, after_peak = tracemalloc.get_traced_memory()
            
            return {
                "before_mb": before_current / 1024 / 1024,
                "after_mb": after_current / 1024 / 1024,
                "saved_mb": (before_current - after_current) / 1024 / 1024,
                "collected_objects": sum(collected)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def cleanup_files(self):
        try:
            cleaned_files = 0
            cleaned_size = 0
            
            # Clean temp directories
            temp_dirs = ["temp", "tmp", "__pycache__", ".pytest_cache"]
            
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                # Delete old files (older than 1 day)
                                if time.time() - os.path.getmtime(file_path) > 86400:
                                    os.remove(file_path)
                                    cleaned_files += 1
                                    cleaned_size += file_size
                            except:
                                continue
            
            # Clean .pyc files
            for root, dirs, files in os.walk("."):
                for file in files:
                    if file.endswith('.pyc'):
                        try:
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_files += 1
                            cleaned_size += file_size
                        except:
                            continue
            
            return {
                "cleaned_files": cleaned_files,
                "cleaned_size_mb": cleaned_size / 1024 / 1024
            }
        except Exception as e:
            return {"error": str(e)}
    
    def count_temp_files(self):
        count = 0
        temp_dirs = ["temp", "tmp", "__pycache__", ".pytest_cache"]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    count += len(files)
        
        return count
    
    def generate_report(self, initial, memory_opt, cleanup, final):
        execution_time = time.time() - self.start_time
        
        memory_improvement = 0
        if "memory_current_mb" in initial and "memory_current_mb" in final:
            memory_improvement = initial["memory_current_mb"] - final["memory_current_mb"]
        
        report = {
            "status": "completed",
            "execution_time_seconds": execution_time,
            "memory_improvement_mb": memory_improvement,
            "garbage_collected": memory_opt.get("collected_objects", 0),
            "files_cleaned": cleanup.get("cleaned_files", 0),
            "space_freed_mb": cleanup.get("cleaned_size_mb", 0),
            "initial_state": initial,
            "final_state": final
        }
        
        # Save report
        report_file = f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"Report saved to: {report_file}")
        except Exception as e:
            print(f"Error saving report: {e}")
        
        return report

def main():
    print("Starting Basic Performance Optimizer...")
    
    optimizer = BasicOptimizer()
    results = optimizer.run_optimization()
    
    if "error" not in results:
        print("\nOptimization completed successfully!")
        print(f"Memory improvement: {results.get('memory_improvement_mb', 0):.2f} MB")
        print(f"Files cleaned: {results.get('files_cleaned', 0)}")
        print(f"Execution time: {results.get('execution_time_seconds', 0):.2f} seconds")
    else:
        print(f"Optimization failed: {results['error']}")

if __name__ == "__main__":
    main()
