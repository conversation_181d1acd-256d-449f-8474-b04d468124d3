#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد التطبيق للتجميع والتوزيع
Application Setup for Building and Distribution
"""

import sys
import os
from pathlib import Path
from setuptools import setup, find_packages

# معلومات التطبيق
APP_NAME = "Smart Content Creator"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "تطبيق ذكي لجمع ومعالجة ونشر المحتوى تلقائياً من منصات التواصل الاجتماعي"
APP_AUTHOR = "Augment Agent"
APP_EMAIL = "<EMAIL>"

# قراءة متطلبات المشروع
def read_requirements():
    """قراءة ملف المتطلبات"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        with open(requirements_file, 'r', encoding='utf-8') as f:
            requirements = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # إزالة التعليقات من نهاية السطر
                    if '#' in line:
                        line = line.split('#')[0].strip()
                    if line:
                        requirements.append(line)
            return requirements
    return []

# قراءة الوصف الطويل
def read_long_description():
    """قراءة الوصف الطويل من README"""
    readme_file = Path(__file__).parent / "README.md"
    if readme_file.exists():
        with open(readme_file, 'r', encoding='utf-8') as f:
            return f.read()
    return APP_DESCRIPTION

# العثور على جميع ملفات البيانات
def find_data_files():
    """العثور على ملفات البيانات المطلوبة"""
    data_files = []
    
    # ملفات الإعدادات
    config_files = []
    config_dir = Path("config")
    if config_dir.exists():
        for config_file in config_dir.rglob("*.json"):
            config_files.append(str(config_file))
        if config_files:
            data_files.append(("config", config_files))
    
    # ملفات الموارد
    resources_files = []
    resources_dir = Path("resources")
    if resources_dir.exists():
        for resource_file in resources_dir.rglob("*"):
            if resource_file.is_file():
                resources_files.append(str(resource_file))
        if resources_files:
            data_files.append(("resources", resources_files))
    
    # ملفات الأيقونات والصور
    assets_files = []
    assets_dir = Path("assets")
    if assets_dir.exists():
        for asset_file in assets_dir.rglob("*"):
            if asset_file.is_file():
                assets_files.append(str(asset_file))
        if assets_files:
            data_files.append(("assets", assets_files))
    
    return data_files

# إعداد التطبيق
setup(
    name=APP_NAME.replace(" ", "-").lower(),
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    author=APP_AUTHOR,
    author_email=APP_EMAIL,
    url="https://github.com/augmentcode/smart-content-creator",
    
    # الحزم والوحدات
    packages=find_packages(),
    include_package_data=True,
    
    # ملفات البيانات
    data_files=find_data_files(),
    
    # المتطلبات
    install_requires=read_requirements(),
    
    # متطلبات إضافية
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-qt>=4.0.0',
            'pytest-asyncio>=0.21.0',
            'black>=23.0.0',
            'flake8>=6.0.0',
            'mypy>=1.0.0'
        ],
        'build': [
            'pyinstaller>=6.0.0',
            'auto-py-to-exe>=2.0.0'
        ]
    },
    
    # نقطة الدخول
    entry_points={
        'console_scripts': [
            'smart-content-creator=main:main',
        ],
        'gui_scripts': [
            'smart-content-creator-gui=main:main',
        ]
    },
    
    # معلومات التصنيف
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Operating System :: POSIX :: Linux",
        "Operating System :: MacOS",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Video",
        "Topic :: Internet :: WWW/HTTP",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "Environment :: X11 Applications :: Qt",
    ],
    
    # متطلبات Python
    python_requires=">=3.8",
    
    # كلمات مفتاحية
    keywords="content creation, social media, automation, video editing, AI analysis",
    
    # معلومات المشروع
    project_urls={
        "Bug Reports": "https://github.com/augmentcode/smart-content-creator/issues",
        "Source": "https://github.com/augmentcode/smart-content-creator",
        "Documentation": "https://docs.augmentcode.com/smart-content-creator",
    },
    
    # إعدادات إضافية
    zip_safe=False,
    platforms=["Windows", "Linux", "macOS"],
)

# معلومات إضافية للطباعة
if __name__ == "__main__":
    print(f"🚀 إعداد {APP_NAME} v{APP_VERSION}")
    print(f"📦 الحزم المكتشفة: {len(find_packages())}")
    print(f"📋 المتطلبات: {len(read_requirements())}")
    print(f"📁 ملفات البيانات: {len(find_data_files())}")
    print("✅ تم إعداد التطبيق بنجاح!")
