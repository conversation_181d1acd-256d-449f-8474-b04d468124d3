#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير القوالب - Template Manager
يدير قوالب المونتاج المعدة مسبقاً
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

class EditingTemplate:
    """قالب مونتاج"""
    
    def __init__(self, name: str, template_type: str = "general"):
        self.name = name
        self.template_type = template_type  # general, viral, podcast, tutorial, etc.
        self.description = ""
        self.duration_range = (10, 60)  # نطاق المدة بالثواني
        self.resolution = (1080, 1920)  # دقة الفيديو
        self.fps = 30
        
        # إعدادات المونتاج
        self.clip_settings = {
            "min_clip_duration": 2.0,
            "max_clip_duration": 8.0,
            "transition_duration": 0.5,
            "auto_cut_on_beat": True,
            "prefer_action_scenes": True
        }
        
        # إعدادات الصوت
        self.audio_settings = {
            "background_music": True,
            "music_volume": 0.3,
            "voice_enhancement": True,
            "auto_ducking": True,  # خفض الموسيقى عند الكلام
            "fade_in_duration": 1.0,
            "fade_out_duration": 1.0
        }
        
        # إعدادات المؤثرات
        self.effects_settings = {
            "auto_color_correction": True,
            "brightness_boost": 0.1,
            "contrast_boost": 0.1,
            "saturation_boost": 0.2,
            "add_glow": False,
            "add_vintage": False,
            "zoom_effects": True,
            "shake_effects": False
        }
        
        # إعدادات الترجمة
        self.subtitle_settings = {
            "auto_generate": True,
            "font_size": 24,
            "font_color": "white",
            "background_color": "black",
            "position": "bottom",
            "animation": "fade",
            "max_chars_per_line": 40
        }
        
        # إعدادات التصدير
        self.export_settings = {
            "quality": "high",
            "bitrate": "5000k",
            "audio_bitrate": "192k",
            "format": "mp4",
            "optimize_for_social": True
        }
        
        # معلومات إضافية
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.usage_count = 0
        self.rating = 0.0
        self.tags = []
        self.preview_image = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "template_type": self.template_type,
            "description": self.description,
            "duration_range": self.duration_range,
            "resolution": self.resolution,
            "fps": self.fps,
            "clip_settings": self.clip_settings,
            "audio_settings": self.audio_settings,
            "effects_settings": self.effects_settings,
            "subtitle_settings": self.subtitle_settings,
            "export_settings": self.export_settings,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "usage_count": self.usage_count,
            "rating": self.rating,
            "tags": self.tags,
            "preview_image": self.preview_image
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EditingTemplate':
        template = cls(data["name"], data.get("template_type", "general"))
        template.description = data.get("description", "")
        template.duration_range = tuple(data.get("duration_range", (10, 60)))
        template.resolution = tuple(data.get("resolution", (1080, 1920)))
        template.fps = data.get("fps", 30)
        template.clip_settings = data.get("clip_settings", {})
        template.audio_settings = data.get("audio_settings", {})
        template.effects_settings = data.get("effects_settings", {})
        template.subtitle_settings = data.get("subtitle_settings", {})
        template.export_settings = data.get("export_settings", {})
        template.usage_count = data.get("usage_count", 0)
        template.rating = data.get("rating", 0.0)
        template.tags = data.get("tags", [])
        template.preview_image = data.get("preview_image")
        
        if "created_at" in data:
            template.created_at = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data:
            template.updated_at = datetime.fromisoformat(data["updated_at"])
        
        return template

class TemplateManager:
    """مدير القوالب"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # مجلد القوالب
        self.templates_dir = Path("templates")
        self.templates_dir.mkdir(exist_ok=True)
        
        # قاموس القوالب
        self.templates = {}
        
        # تحميل القوالب
        self._load_templates()
        
        # إنشاء القوالب الافتراضية إذا لم تكن موجودة
        if not self.templates:
            self._create_default_templates()
        
        # إحصائيات
        self.stats = {
            "templates_created": 0,
            "templates_used": 0,
            "most_popular_template": None
        }
    
    def _load_templates(self):
        """تحميل القوالب من الملفات"""
        try:
            templates_file = self.templates_dir / "templates.json"
            
            if templates_file.exists():
                with open(templates_file, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                
                for template_data in templates_data.values():
                    template = EditingTemplate.from_dict(template_data)
                    self.templates[template.name] = template
                
                self.logger.info(f"تم تحميل {len(self.templates)} قالب")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل القوالب: {str(e)}")
    
    def _save_templates(self):
        """حفظ القوالب في الملفات"""
        try:
            templates_file = self.templates_dir / "templates.json"
            
            templates_data = {}
            for name, template in self.templates.items():
                templates_data[name] = template.to_dict()
            
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info("تم حفظ القوالب")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ القوالب: {str(e)}")
    
    def _create_default_templates(self):
        """إنشاء القوالب الافتراضية"""
        try:
            # قالب المحتوى الفيروسي
            viral_template = EditingTemplate("محتوى فيروسي", "viral")
            viral_template.description = "قالب مخصص للمحتوى الفيروسي السريع والجذاب"
            viral_template.duration_range = (15, 30)
            viral_template.clip_settings.update({
                "min_clip_duration": 1.0,
                "max_clip_duration": 3.0,
                "transition_duration": 0.2,
                "auto_cut_on_beat": True,
                "prefer_action_scenes": True
            })
            viral_template.effects_settings.update({
                "zoom_effects": True,
                "add_glow": True,
                "saturation_boost": 0.3
            })
            viral_template.tags = ["فيروسي", "سريع", "جذاب", "تيك توك"]
            
            # قالب البودكاست
            podcast_template = EditingTemplate("بودكاست", "podcast")
            podcast_template.description = "قالب مخصص لمقاطع البودكاست والمحادثات"
            podcast_template.duration_range = (30, 120)
            podcast_template.clip_settings.update({
                "min_clip_duration": 5.0,
                "max_clip_duration": 15.0,
                "transition_duration": 1.0,
                "auto_cut_on_beat": False,
                "prefer_action_scenes": False
            })
            podcast_template.audio_settings.update({
                "voice_enhancement": True,
                "background_music": False,
                "auto_ducking": False
            })
            podcast_template.effects_settings.update({
                "auto_color_correction": True,
                "zoom_effects": False,
                "add_vintage": True
            })
            podcast_template.tags = ["بودكاست", "محادثة", "تعليمي"]
            
            # قالب التعليمي
            tutorial_template = EditingTemplate("تعليمي", "tutorial")
            tutorial_template.description = "قالب مخصص للمحتوى التعليمي والشروحات"
            tutorial_template.duration_range = (60, 300)
            tutorial_template.clip_settings.update({
                "min_clip_duration": 3.0,
                "max_clip_duration": 10.0,
                "transition_duration": 0.5,
                "auto_cut_on_beat": False,
                "prefer_action_scenes": False
            })
            tutorial_template.subtitle_settings.update({
                "auto_generate": True,
                "font_size": 28,
                "animation": "typewriter"
            })
            tutorial_template.tags = ["تعليمي", "شرح", "تدريب"]
            
            # قالب الألعاب
            gaming_template = EditingTemplate("ألعاب", "gaming")
            gaming_template.description = "قالب مخصص لمقاطع الألعاب والتحديات"
            gaming_template.duration_range = (30, 90)
            gaming_template.clip_settings.update({
                "min_clip_duration": 2.0,
                "max_clip_duration": 6.0,
                "transition_duration": 0.3,
                "auto_cut_on_beat": True,
                "prefer_action_scenes": True
            })
            gaming_template.effects_settings.update({
                "add_glow": True,
                "shake_effects": True,
                "zoom_effects": True
            })
            gaming_template.tags = ["ألعاب", "تحدي", "إثارة"]
            
            # قالب الطبخ
            cooking_template = EditingTemplate("طبخ", "cooking")
            cooking_template.description = "قالب مخصص لمقاطع الطبخ والوصفات"
            cooking_template.duration_range = (45, 120)
            cooking_template.clip_settings.update({
                "min_clip_duration": 3.0,
                "max_clip_duration": 8.0,
                "transition_duration": 0.8,
                "auto_cut_on_beat": False,
                "prefer_action_scenes": False
            })
            cooking_template.effects_settings.update({
                "saturation_boost": 0.4,
                "brightness_boost": 0.2,
                "add_vintage": True
            })
            cooking_template.tags = ["طبخ", "وصفات", "طعام"]
            
            # إضافة القوالب
            self.templates["محتوى فيروسي"] = viral_template
            self.templates["بودكاست"] = podcast_template
            self.templates["تعليمي"] = tutorial_template
            self.templates["ألعاب"] = gaming_template
            self.templates["طبخ"] = cooking_template
            
            # حفظ القوالب
            self._save_templates()
            
            self.logger.info("تم إنشاء القوالب الافتراضية")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء القوالب الافتراضية: {str(e)}")
    
    def get_template(self, name: str) -> Optional[EditingTemplate]:
        """الحصول على قالب بالاسم"""
        return self.templates.get(name)
    
    def get_templates_by_type(self, template_type: str) -> List[EditingTemplate]:
        """الحصول على القوالب حسب النوع"""
        return [template for template in self.templates.values() 
                if template.template_type == template_type]
    
    def get_all_templates(self) -> Dict[str, EditingTemplate]:
        """الحصول على جميع القوالب"""
        return self.templates.copy()
    
    def create_template(self, template: EditingTemplate) -> bool:
        """إنشاء قالب جديد"""
        try:
            if template.name in self.templates:
                self.logger.warning(f"القالب {template.name} موجود بالفعل")
                return False
            
            self.templates[template.name] = template
            self._save_templates()
            
            self.stats["templates_created"] += 1
            self.logger.info(f"تم إنشاء القالب: {template.name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء القالب: {str(e)}")
            return False
    
    def update_template(self, template: EditingTemplate) -> bool:
        """تحديث قالب موجود"""
        try:
            if template.name not in self.templates:
                self.logger.warning(f"القالب {template.name} غير موجود")
                return False
            
            template.updated_at = datetime.now()
            self.templates[template.name] = template
            self._save_templates()
            
            self.logger.info(f"تم تحديث القالب: {template.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث القالب: {str(e)}")
            return False
    
    def delete_template(self, name: str) -> bool:
        """حذف قالب"""
        try:
            if name not in self.templates:
                self.logger.warning(f"القالب {name} غير موجود")
                return False
            
            del self.templates[name]
            self._save_templates()
            
            self.logger.info(f"تم حذف القالب: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف القالب: {str(e)}")
            return False
    
    def use_template(self, name: str) -> Optional[EditingTemplate]:
        """استخدام قالب (يزيد عداد الاستخدام)"""
        try:
            template = self.templates.get(name)
            if template:
                template.usage_count += 1
                template.updated_at = datetime.now()
                self._save_templates()
                
                self.stats["templates_used"] += 1
                
                # تحديث القالب الأكثر شعبية
                most_popular = max(self.templates.values(), key=lambda t: t.usage_count)
                self.stats["most_popular_template"] = most_popular.name
                
                self.logger.info(f"تم استخدام القالب: {name}")
                
            return template
            
        except Exception as e:
            self.logger.error(f"خطأ في استخدام القالب: {str(e)}")
            return None
    
    def search_templates(self, query: str) -> List[EditingTemplate]:
        """البحث في القوالب"""
        try:
            query = query.lower()
            results = []
            
            for template in self.templates.values():
                # البحث في الاسم والوصف والعلامات
                if (query in template.name.lower() or 
                    query in template.description.lower() or
                    any(query in tag.lower() for tag in template.tags)):
                    results.append(template)
            
            return results
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث: {str(e)}")
            return []
    
    def get_popular_templates(self, limit: int = 5) -> List[EditingTemplate]:
        """الحصول على القوالب الأكثر شعبية"""
        try:
            sorted_templates = sorted(
                self.templates.values(), 
                key=lambda t: t.usage_count, 
                reverse=True
            )
            return sorted_templates[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على القوالب الشعبية: {str(e)}")
            return []
    
    def get_template_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات القوالب"""
        try:
            template_types = {}
            total_usage = 0
            
            for template in self.templates.values():
                template_type = template.template_type
                if template_type not in template_types:
                    template_types[template_type] = 0
                template_types[template_type] += 1
                total_usage += template.usage_count
            
            return {
                "total_templates": len(self.templates),
                "templates_by_type": template_types,
                "total_usage": total_usage,
                "templates_created": self.stats["templates_created"],
                "templates_used": self.stats["templates_used"],
                "most_popular_template": self.stats["most_popular_template"],
                "average_usage": total_usage / max(1, len(self.templates))
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}
    
    def export_template(self, name: str, export_path: str) -> bool:
        """تصدير قالب إلى ملف"""
        try:
            template = self.templates.get(name)
            if not template:
                self.logger.error(f"القالب {name} غير موجود")
                return False
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"تم تصدير القالب {name} إلى {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير القالب: {str(e)}")
            return False
    
    def import_template(self, import_path: str) -> bool:
        """استيراد قالب من ملف"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            template = EditingTemplate.from_dict(template_data)
            
            # التحقق من عدم وجود قالب بنفس الاسم
            if template.name in self.templates:
                template.name = f"{template.name}_مستورد"
            
            self.templates[template.name] = template
            self._save_templates()
            
            self.logger.info(f"تم استيراد القالب {template.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في استيراد القالب: {str(e)}")
            return False
