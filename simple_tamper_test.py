#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام الحماية من التلاعب
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("🔍 اختبار استيراد نظام الحماية من التلاعب...")

try:
    print("1. استيراد المكتبات الأساسية...")
    import os
    import json
    import hashlib
    import hmac
    import time
    import logging
    from datetime import datetime, timedelta
    from enum import Enum
    from dataclasses import dataclass
    print("✅ تم استيراد المكتبات الأساسية")
    
    print("2. استيراد مكتبات التشفير...")
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.primitives.serialization import load_pem_private_key, load_pem_public_key
    from cryptography.hazmat.backends import default_backend
    from cryptography.exceptions import InvalidSignature
    print("✅ تم استيراد مكتبات التشفير")
    
    print("3. استيراد نظام الحماية من التلاعب...")
    from security.tamper_protection import (
        TamperProtectionSystem, 
        ProtectionLevel, 
        IntegrityStatus,
        FileIntegrity,
        TamperAlert
    )
    print("✅ تم استيراد نظام الحماية من التلاعب")
    
    print("4. اختبار إنشاء كائن النظام...")
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        tamper_system = TamperProtectionSystem(base_dir=temp_dir)
        print("✅ تم إنشاء كائن نظام الحماية من التلاعب")
        
        print("5. اختبار الحصول على حالة النظام...")
        status = tamper_system.get_system_status()
        print(f"✅ حالة النظام: {status}")
        
        print("6. اختبار الحصول على الإحصائيات...")
        stats = tamper_system.get_protection_statistics()
        print(f"✅ الإحصائيات: {stats}")
    
    print("\n🎉 جميع الاختبارات البسيطة نجحت!")
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
