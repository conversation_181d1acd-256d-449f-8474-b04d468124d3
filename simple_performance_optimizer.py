#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن الأداء المبسط - Simple Performance Optimizer
نسخة مبسطة من محسن الأداء تعمل بدون مكتبات خارجية
"""

import gc
import os
import sys
import time
import json
import threading
import tracemalloc
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

class SimplePerformanceOptimizer:
    """محسن الأداء المبسط"""
    
    def __init__(self):
        self.start_time = None
        self.optimization_results = {}
        
        # إنشاء مجلد السجلات
        os.makedirs('logs', exist_ok=True)
        
        # بدء مراقبة الذاكرة
        tracemalloc.start()
        
        print("🚀 تم تهيئة محسن الأداء المبسط")
    
    def run_optimization(self) -> Dict[str, Any]:
        """تشغيل تحسين الأداء"""
        self.start_time = datetime.now()
        print(f"⏰ بدء التحسين في: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            "start_time": self.start_time.isoformat(),
            "optimizations": {},
            "summary": {},
            "recommendations": []
        }
        
        try:
            # 1. تحسين الذاكرة
            print("🧠 تحسين الذاكرة...")
            memory_results = self._optimize_memory()
            results["optimizations"]["memory"] = memory_results
            
            # 2. تنظيف الملفات المؤقتة
            print("🗑️ تنظيف الملفات المؤقتة...")
            cleanup_results = self._cleanup_temp_files()
            results["optimizations"]["cleanup"] = cleanup_results
            
            # 3. تحسين إعدادات Python
            print("🐍 تحسين إعدادات Python...")
            python_results = self._optimize_python_settings()
            results["optimizations"]["python"] = python_results
            
            # 4. فحص الموارد
            print("📊 فحص استخدام الموارد...")
            resource_results = self._check_resources()
            results["optimizations"]["resources"] = resource_results
            
            # 5. توليد التوصيات
            print("💡 توليد التوصيات...")
            recommendations = self._generate_recommendations()
            results["recommendations"] = recommendations
            
            # إنهاء التحسين
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            results["end_time"] = end_time.isoformat()
            results["duration"] = str(duration)
            results["status"] = "success"
            
            # توليد الملخص
            results["summary"] = self._generate_summary(results)
            
            print(f"✅ تم إكمال التحسين في: {duration}")
            
        except Exception as e:
            print(f"❌ خطأ في التحسين: {e}")
            results["status"] = "error"
            results["error"] = str(e)
            results["end_time"] = datetime.now().isoformat()
        
        return results
    
    def _optimize_memory(self) -> Dict[str, Any]:
        """تحسين الذاكرة"""
        # قياس الذاكرة قبل التحسين
        current, peak = tracemalloc.get_traced_memory()
        initial_memory = current / 1024 / 1024  # MB
        
        # تنظيف الذاكرة
        collected_objects = 0
        for i in range(3):  # تنظيف متعدد المراحل
            collected = gc.collect()
            collected_objects += collected
            time.sleep(0.1)
        
        # قياس الذاكرة بعد التحسين
        current_after, peak_after = tracemalloc.get_traced_memory()
        final_memory = current_after / 1024 / 1024  # MB
        memory_saved = initial_memory - final_memory
        
        return {
            "initial_memory_mb": round(initial_memory, 2),
            "final_memory_mb": round(final_memory, 2),
            "memory_saved_mb": round(memory_saved, 2),
            "objects_collected": collected_objects,
            "peak_memory_mb": round(peak / 1024 / 1024, 2),
            "improvement_percentage": round((memory_saved / initial_memory) * 100, 2) if initial_memory > 0 else 0
        }
    
    def _cleanup_temp_files(self) -> Dict[str, Any]:
        """تنظيف الملفات المؤقتة"""
        temp_dirs = ['temp', 'tmp', '__pycache__', '.pytest_cache']
        files_deleted = 0
        size_freed = 0
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                files_deleted += 1
                                size_freed += file_size
                            except Exception:
                                pass
                except Exception:
                    pass
        
        # تنظيف ملفات .pyc
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    try:
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        files_deleted += 1
                        size_freed += file_size
                    except Exception:
                        pass
        
        return {
            "files_deleted": files_deleted,
            "size_freed_mb": round(size_freed / 1024 / 1024, 2),
            "temp_dirs_checked": temp_dirs
        }
    
    def _optimize_python_settings(self) -> Dict[str, Any]:
        """تحسين إعدادات Python"""
        # تحسين garbage collection
        gc.set_threshold(700, 10, 10)  # تحسين عتبات التنظيف
        
        # تمكين التحسينات
        optimizations = {
            "gc_threshold_optimized": True,
            "gc_enabled": gc.isenabled(),
            "gc_counts": gc.get_count(),
            "gc_stats": gc.get_stats() if hasattr(gc, 'get_stats') else "not_available"
        }
        
        return optimizations
    
    def _check_resources(self) -> Dict[str, Any]:
        """فحص استخدام الموارد"""
        resources = {}
        
        try:
            # فحص الذاكرة
            current, peak = tracemalloc.get_traced_memory()
            resources["memory"] = {
                "current_mb": round(current / 1024 / 1024, 2),
                "peak_mb": round(peak / 1024 / 1024, 2)
            }
        except Exception:
            resources["memory"] = {"error": "unable_to_measure"}
        
        try:
            # فحص الخيوط
            active_threads = threading.active_count()
            resources["threads"] = {
                "active_count": active_threads
            }
        except Exception:
            resources["threads"] = {"error": "unable_to_measure"}
        
        try:
            # فحص الملفات المفتوحة (تقريبي)
            import resource
            max_files = resource.getrlimit(resource.RLIMIT_NOFILE)[0]
            resources["files"] = {
                "max_open_files": max_files
            }
        except Exception:
            resources["files"] = {"error": "unable_to_measure"}
        
        return resources
    
    def _generate_recommendations(self) -> List[str]:
        """توليد توصيات التحسين"""
        recommendations = [
            "🔄 طبق garbage collection دوري للحفاظ على الذاكرة",
            "📁 نظف الملفات المؤقتة بانتظام",
            "⚡ استخدم التخزين المؤقت للعمليات المكلفة",
            "🧵 راقب عدد الخيوط النشطة",
            "💾 استخدم generators للبيانات الكبيرة",
            "🔍 راقب استخدام الذاكرة باستمرار",
            "🚀 استخدم المعالجة غير المتزامنة عند الإمكان",
            "📊 قس الأداء قبل وبعد التحسينات",
            "🛡️ طبق التحسينات تدريجياً",
            "📝 احتفظ بسجلات الأداء للمراجعة"
        ]
        
        return recommendations
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """توليد ملخص النتائج"""
        summary = {
            "total_optimizations": len(results.get("optimizations", {})),
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "total_recommendations": len(results.get("recommendations", [])),
            "overall_status": results.get("status", "unknown")
        }
        
        # حساب التحسينات الناجحة
        for opt_name, opt_data in results.get("optimizations", {}).items():
            if isinstance(opt_data, dict) and "error" not in opt_data:
                summary["successful_optimizations"] += 1
            else:
                summary["failed_optimizations"] += 1
        
        # حساب التحسن الإجمالي
        memory_opt = results.get("optimizations", {}).get("memory", {})
        if "memory_saved_mb" in memory_opt:
            summary["memory_improvement_mb"] = memory_opt["memory_saved_mb"]
        
        cleanup_opt = results.get("optimizations", {}).get("cleanup", {})
        if "size_freed_mb" in cleanup_opt:
            summary["disk_space_freed_mb"] = cleanup_opt["size_freed_mb"]
        
        return summary
    
    def save_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """حفظ النتائج"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/simple_optimization_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ النتائج في: {filename}")
            return filename
        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {e}")
            return ""
    
    def print_summary(self, results: Dict[str, Any]):
        """طباعة ملخص النتائج"""
        print("\n" + "=" * 60)
        print("📋 ملخص تحسين الأداء")
        print("=" * 60)
        
        summary = results.get("summary", {})
        
        print(f"الحالة: {results.get('status', 'unknown')}")
        print(f"المدة: {results.get('duration', 'unknown')}")
        print(f"التحسينات المطبقة: {summary.get('total_optimizations', 0)}")
        print(f"التحسينات الناجحة: {summary.get('successful_optimizations', 0)}")
        
        if "memory_improvement_mb" in summary:
            print(f"توفير الذاكرة: {summary['memory_improvement_mb']} MB")
        
        if "disk_space_freed_mb" in summary:
            print(f"مساحة القرص المحررة: {summary['disk_space_freed_mb']} MB")
        
        print(f"عدد التوصيات: {summary.get('total_recommendations', 0)}")
        
        print("\n💡 أهم التوصيات:")
        recommendations = results.get("recommendations", [])
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"  {i}. {rec}")
        
        print("=" * 60)

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في محسن الأداء المبسط")
    print("=" * 60)
    
    # إنشاء المحسن
    optimizer = SimplePerformanceOptimizer()
    
    # تشغيل التحسين
    results = optimizer.run_optimization()
    
    # حفظ النتائج
    output_file = optimizer.save_results(results)
    
    # طباعة الملخص
    optimizer.print_summary(results)
    
    if results.get("status") == "success":
        print("\n✅ تم إكمال تحسين الأداء بنجاح!")
    else:
        print("\n❌ حدث خطأ أثناء تحسين الأداء")
        if "error" in results:
            print(f"الخطأ: {results['error']}")
    
    return results

if __name__ == "__main__":
    main()
