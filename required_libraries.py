#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قائمة شاملة بالمكتبات المطلوبة لتطبيق منشئ المحتوى الذكي
"""

# المكتبات الأساسية المطلوبة للتطبيق الكامل
REQUIRED_LIBRARIES = {
    
    # 🎥 معالجة الفيديو والصوت
    "video_audio": [
        "moviepy",           # تحرير الفيديو الأساسي
        "opencv-python",     # معالجة الصور والفيديو
        "ffmpeg-python",     # تحويل وضغط الفيديو
        "pydub",            # معالجة الصوت
        "librosa",          # تحليل الصوت المتقدم
        "soundfile",        # قراءة وكتابة ملفات الصوت
        "imageio",          # قراءة وكتابة الصور والفيديو
        "imageio-ffmpeg",   # دعم FFmpeg لـ imageio
    ],
    
    # 🤖 الذكاء الاصطناعي والتعلم الآلي
    "ai_ml": [
        "torch",            # PyTorch للذكاء الاصطناعي
        "torchvision",      # رؤية حاسوبية
        "torchaudio",       # معالجة الصوت بالذكاء الاصطناعي
        "transformers",     # نماذج اللغة المتقدمة
        "whisper",          # تحويل الكلام إلى نص
        "openai",           # واجهة OpenAI API
        "tensorflow",       # TensorFlow (بديل لـ PyTorch)
        "scikit-learn",     # خوارزميات التعلم الآلي
        "nltk",             # معالجة اللغة الطبيعية
        "spacy",            # معالجة اللغة المتقدمة
        "face-recognition", # التعرف على الوجوه
        "mediapipe",        # معالجة الوسائط المتعددة
    ],
    
    # 🌐 التفاعل مع وسائل التواصل الاجتماعي
    "social_media": [
        "selenium",         # أتمتة المتصفح
        "playwright",       # أتمتة متصفح حديثة
        "instaloader",      # تحميل من Instagram
        "tiktok-api",       # واجهة TikTok (غير رسمية)
        "tweepy",           # واجهة Twitter
        "facebook-sdk",     # واجهة Facebook
        "youtube-dl",       # تحميل من YouTube
        "yt-dlp",           # نسخة محدثة من youtube-dl
        "requests",         # طلبات HTTP
        "aiohttp",          # طلبات HTTP غير متزامنة
        "httpx",            # عميل HTTP حديث
    ],
    
    # 📊 تحليل البيانات والإحصائيات
    "data_analysis": [
        "pandas",           # تحليل البيانات
        "numpy",            # العمليات الرياضية
        "matplotlib",       # الرسوم البيانية
        "seaborn",          # رسوم بيانية متقدمة
        "plotly",           # رسوم تفاعلية
        "scipy",            # حسابات علمية
        "statsmodels",      # نماذج إحصائية
    ],
    
    # 🗄️ قواعد البيانات والتخزين
    "database": [
        "sqlalchemy",       # ORM لقواعد البيانات
        "sqlite3",          # قاعدة بيانات محلية (مدمجة)
        "pymongo",          # MongoDB
        "redis",            # Redis للتخزين المؤقت
        "psycopg2",         # PostgreSQL
        "mysql-connector-python", # MySQL
    ],
    
    # 🔐 الأمان والتشفير
    "security": [
        "cryptography",     # تشفير متقدم
        "bcrypt",           # تشفير كلمات المرور
        "pycryptodome",     # مكتبة تشفير شاملة
        "keyring",          # إدارة المفاتيح
        "python-jose",      # JWT tokens
        "passlib",          # تشفير كلمات المرور
    ],
    
    # 🌍 الشبكة والاتصالات
    "networking": [
        "websockets",       # WebSocket connections
        "socketio",         # Socket.IO
        "paramiko",         # SSH connections
        "ftplib",           # FTP (مدمجة)
        "smtplib",          # SMTP (مدمجة)
        "imaplib",          # IMAP (مدمجة)
    ],
    
    # 📱 واجهة المستخدم
    "gui": [
        "PyQt6",            # واجهة رسومية أساسية
        "PyQt6-tools",      # أدوات PyQt6
        "qtawesome",        # أيقونات Font Awesome
        "qdarkstyle",       # ثيم داكن
        "qasync",           # دعم async في PyQt
    ],
    
    # 🛠️ أدوات التطوير والاختبار
    "development": [
        "pytest",          # اختبارات
        "pytest-qt",       # اختبار PyQt
        "pytest-asyncio",  # اختبار async
        "black",           # تنسيق الكود
        "flake8",          # فحص جودة الكود
        "mypy",            # فحص الأنواع
        "coverage",        # تغطية الاختبارات
    ],
    
    # 📦 أدوات البناء والتوزيع
    "build_tools": [
        "pyinstaller",     # بناء ملفات تنفيذية
        "cx-freeze",       # بديل لـ PyInstaller
        "nuitka",          # مترجم Python
        "setuptools",      # بناء الحزم
        "wheel",           # تنسيق الحزم
        "twine",           # رفع الحزم
    ],
    
    # 🔧 أدوات مساعدة
    "utilities": [
        "python-dotenv",   # متغيرات البيئة
        "configparser",    # ملفات التكوين (مدمجة)
        "logging",         # السجلات (مدمجة)
        "schedule",        # جدولة المهام
        "apscheduler",     # جدولة متقدمة
        "tqdm",            # شريط التقدم
        "rich",            # إخراج ملون في Terminal
        "click",           # واجهة سطر الأوامر
        "typer",           # واجهة سطر أوامر حديثة
        "psutil",          # معلومات النظام
        "watchdog",        # مراقبة الملفات
        "pillow",          # معالجة الصور
        "qrcode",          # إنشاء QR codes
        "barcode",         # إنشاء باركود
        "python-magic",    # تحديد نوع الملفات
        "chardet",         # كشف ترميز النص
        "python-dateutil", # معالجة التواريخ
        "pytz",            # المناطق الزمنية
        "babel",           # الترجمة والتوطين
    ],
    
    # 🌐 تطوير الويب (للوحة التحكم)
    "web": [
        "fastapi",         # إطار عمل ويب سريع
        "uvicorn",         # خادم ASGI
        "jinja2",          # محرك القوالب
        "starlette",       # إطار عمل ويب أساسي
        "websockets",      # دعم WebSocket
    ],
    
    # 📄 معالجة الملفات والوثائق
    "file_processing": [
        "openpyxl",        # ملفات Excel
        "python-docx",     # ملفات Word
        "pypdf2",          # ملفات PDF
        "reportlab",       # إنشاء PDF
        "xlsxwriter",      # كتابة Excel
        "csv",             # ملفات CSV (مدمجة)
        "json",            # ملفات JSON (مدمجة)
        "yaml",            # ملفات YAML
        "toml",            # ملفات TOML
        "xml.etree.ElementTree", # XML (مدمجة)
    ]
}

def get_all_libraries():
    """الحصول على قائمة بجميع المكتبات"""
    all_libs = []
    for category, libs in REQUIRED_LIBRARIES.items():
        all_libs.extend(libs)
    return sorted(set(all_libs))

def get_essential_libraries():
    """المكتبات الأساسية للبدء"""
    essential = [
        # واجهة المستخدم
        "PyQt6", "PyQt6-tools", "qtawesome",
        
        # معالجة الفيديو الأساسية
        "moviepy", "opencv-python", "pillow",
        
        # الشبكة والتفاعل
        "requests", "selenium", "aiohttp",
        
        # تحليل البيانات الأساسي
        "pandas", "numpy",
        
        # الأمان
        "cryptography", "bcrypt",
        
        # أدوات مساعدة
        "python-dotenv", "tqdm", "schedule",
        
        # البناء
        "pyinstaller"
    ]
    return essential

def get_ai_libraries():
    """مكتبات الذكاء الاصطناعي"""
    return REQUIRED_LIBRARIES["ai_ml"]

def get_social_media_libraries():
    """مكتبات وسائل التواصل الاجتماعي"""
    return REQUIRED_LIBRARIES["social_media"]

if __name__ == "__main__":
    print("📦 المكتبات المطلوبة لتطبيق منشئ المحتوى الذكي")
    print("=" * 60)
    
    for category, libs in REQUIRED_LIBRARIES.items():
        print(f"\n🔹 {category.replace('_', ' ').title()}:")
        for lib in libs:
            print(f"  • {lib}")
    
    print(f"\n📊 إجمالي المكتبات: {len(get_all_libraries())}")
    print(f"🎯 المكتبات الأساسية: {len(get_essential_libraries())}")
