#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مستخرج المقاطع - Clip Extractor
يستخرج أفضل المقاطع من الفيديو بناءً على التحليل الذكي
"""

import logging
import os
import json
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import moviepy.editor as mp
import numpy as np

class ExtractedClip:
    """مقطع مستخرج مع معلوماته"""
    
    def __init__(self, start_time: float, end_time: float, 
                 source_file: str, reason: str = ""):
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.source_file = source_file
        self.reason = reason
        self.score = 0.0
        self.audio_score = 0.0
        self.video_score = 0.0
        self.combined_score = 0.0
        self.keywords = []
        self.emotions = []
        self.visual_features = {}
        self.audio_features = {}
        self.output_path = ""
        self.thumbnail_path = ""
        self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "source_file": self.source_file,
            "reason": self.reason,
            "score": self.score,
            "audio_score": self.audio_score,
            "video_score": self.video_score,
            "combined_score": self.combined_score,
            "keywords": self.keywords,
            "emotions": self.emotions,
            "visual_features": self.visual_features,
            "audio_features": self.audio_features,
            "output_path": self.output_path,
            "thumbnail_path": self.thumbnail_path,
            "created_at": self.created_at.isoformat()
        }

class ClipExtractor:
    """مستخرج المقاطع الذكي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات الاستخراج
        self.settings = self._load_extraction_settings()
        
        # مجلدات الحفظ
        self.clips_dir = Path("data/extracted_clips")
        self.thumbnails_dir = Path("data/thumbnails")
        self.clips_dir.mkdir(parents=True, exist_ok=True)
        self.thumbnails_dir.mkdir(parents=True, exist_ok=True)
        
        # إحصائيات
        self.stats = {
            "total_clips_extracted": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "total_duration_extracted": 0.0
        }
    
    def _load_extraction_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات الاستخراج"""
        try:
            return self.config_manager.get_setting("ai_settings", "clip_extraction", {
                "min_clip_duration": 5.0,  # ثواني
                "max_clip_duration": 60.0,  # ثواني
                "preferred_duration": 15.0,  # ثواني
                "max_clips_per_video": 5,
                "quality_threshold": 0.6,
                "overlap_threshold": 2.0,  # ثواني تداخل مسموح
                "audio_weight": 0.4,
                "video_weight": 0.6,
                "create_thumbnails": True,
                "output_format": "mp4",
                "output_quality": "medium",  # low, medium, high
                "fade_duration": 0.5,  # ثواني
                "add_padding": True,
                "padding_duration": 1.0  # ثواني
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الاستخراج: {str(e)}")
            return {
                "min_clip_duration": 5.0,
                "max_clip_duration": 60.0,
                "preferred_duration": 15.0,
                "max_clips_per_video": 5,
                "quality_threshold": 0.6,
                "overlap_threshold": 2.0,
                "audio_weight": 0.4,
                "video_weight": 0.6,
                "create_thumbnails": True,
                "output_format": "mp4",
                "output_quality": "medium",
                "fade_duration": 0.5,
                "add_padding": True,
                "padding_duration": 1.0
            }
    
    def extract_clips(self, file_path: str, 
                     audio_analysis: Dict[str, Any] = None,
                     video_analysis: Dict[str, Any] = None) -> List[ExtractedClip]:
        """استخراج المقاطع من الفيديو"""
        try:
            self.logger.info(f"بدء استخراج المقاطع من: {file_path}")
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")
            
            # تحديد المقاطع المرشحة
            candidate_clips = self._identify_candidate_clips(
                file_path, audio_analysis, video_analysis
            )
            
            # تصفية وترتيب المقاطع
            filtered_clips = self._filter_and_rank_clips(candidate_clips)
            
            # استخراج المقاطع الفعلية
            extracted_clips = []
            for clip in filtered_clips:
                try:
                    extracted_clip = self._extract_single_clip(clip, file_path)
                    if extracted_clip:
                        extracted_clips.append(extracted_clip)
                        self.stats["successful_extractions"] += 1
                        self.stats["total_duration_extracted"] += extracted_clip.duration
                except Exception as e:
                    self.logger.error(f"خطأ في استخراج المقطع: {str(e)}")
                    self.stats["failed_extractions"] += 1
            
            self.stats["total_clips_extracted"] += len(extracted_clips)
            
            self.logger.info(f"تم استخراج {len(extracted_clips)} مقطع بنجاح")
            return extracted_clips
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج المقاطع: {str(e)}")
            return []
    
    def _identify_candidate_clips(self, file_path: str,
                                 audio_analysis: Dict[str, Any] = None,
                                 video_analysis: Dict[str, Any] = None) -> List[ExtractedClip]:
        """تحديد المقاطع المرشحة للاستخراج"""
        candidates = []
        
        try:
            # من تحليل الصوت
            if audio_analysis and "interesting_moments" in audio_analysis:
                audio_candidates = self._get_audio_candidates(
                    audio_analysis["interesting_moments"], file_path
                )
                candidates.extend(audio_candidates)
            
            # من تحليل الفيديو
            if video_analysis and "interesting_moments" in video_analysis:
                video_candidates = self._get_video_candidates(
                    video_analysis["interesting_moments"], file_path
                )
                candidates.extend(video_candidates)
            
            # إذا لم توجد تحليلات، استخدم طريقة بسيطة
            if not candidates:
                candidates = self._get_simple_candidates(file_path)
            
            return candidates
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد المقاطع المرشحة: {str(e)}")
            return []
    
    def _get_audio_candidates(self, audio_moments: List[Dict[str, Any]], 
                             file_path: str) -> List[ExtractedClip]:
        """الحصول على مقاطع مرشحة من تحليل الصوت"""
        candidates = []
        
        try:
            for moment in audio_moments:
                start_time = moment.get("start_time", 0.0)
                end_time = moment.get("end_time", 0.0)
                
                # تعديل المدة لتناسب الإعدادات
                duration = end_time - start_time
                preferred_duration = self.settings.get("preferred_duration", 15.0)
                
                if duration < self.settings.get("min_clip_duration", 5.0):
                    # توسيع المقطع
                    extension = (self.settings.get("min_clip_duration", 5.0) - duration) / 2
                    start_time = max(0, start_time - extension)
                    end_time = end_time + extension
                elif duration > self.settings.get("max_clip_duration", 60.0):
                    # تقليص المقطع
                    end_time = start_time + self.settings.get("max_clip_duration", 60.0)
                
                clip = ExtractedClip(start_time, end_time, file_path, "لحظة صوتية مثيرة")
                clip.audio_score = moment.get("score", 0.0)
                clip.keywords = moment.get("keywords", [])
                clip.emotions = moment.get("emotions", [])
                clip.audio_features = {
                    "speech_rate": moment.get("speech_rate", 0.0),
                    "energy_level": moment.get("energy_level", 0.0),
                    "emotion_score": moment.get("emotion_score", 0.0)
                }
                
                candidates.append(clip)
            
            return candidates
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مقاطع الصوت: {str(e)}")
            return []
    
    def _get_video_candidates(self, video_moments: List[Dict[str, Any]], 
                             file_path: str) -> List[ExtractedClip]:
        """الحصول على مقاطع مرشحة من تحليل الفيديو"""
        candidates = []
        
        try:
            for moment in video_moments:
                start_time = moment.get("start_time", 0.0)
                end_time = moment.get("end_time", 0.0)
                
                # تعديل المدة
                duration = end_time - start_time
                if duration < self.settings.get("min_clip_duration", 5.0):
                    extension = (self.settings.get("min_clip_duration", 5.0) - duration) / 2
                    start_time = max(0, start_time - extension)
                    end_time = end_time + extension
                elif duration > self.settings.get("max_clip_duration", 60.0):
                    end_time = start_time + self.settings.get("max_clip_duration", 60.0)
                
                clip = ExtractedClip(start_time, end_time, file_path, moment.get("reason", "لحظة بصرية مثيرة"))
                clip.video_score = moment.get("score", 0.0)
                clip.visual_features = {
                    "avg_motion": moment.get("avg_motion", 0.0),
                    "face_appearances": moment.get("face_appearances", 0),
                    "scene_changes": moment.get("scene_changes", 0),
                    "is_action_packed": moment.get("is_action_packed", False)
                }
                
                candidates.append(clip)
            
            return candidates
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مقاطع الفيديو: {str(e)}")
            return []
    
    def _get_simple_candidates(self, file_path: str) -> List[ExtractedClip]:
        """الحصول على مقاطع بطريقة بسيطة (بدون تحليل متقدم)"""
        candidates = []
        
        try:
            # الحصول على مدة الفيديو
            with mp.VideoFileClip(file_path) as video:
                duration = video.duration
            
            # تقسيم الفيديو إلى مقاطع متساوية
            segment_duration = self.settings.get("preferred_duration", 15.0)
            num_segments = int(duration / segment_duration)
            
            for i in range(min(num_segments, self.settings.get("max_clips_per_video", 5))):
                start_time = i * segment_duration
                end_time = min(start_time + segment_duration, duration)
                
                if end_time - start_time >= self.settings.get("min_clip_duration", 5.0):
                    clip = ExtractedClip(start_time, end_time, file_path, "مقطع تلقائي")
                    clip.score = 0.5  # نقاط متوسطة
                    candidates.append(clip)
            
            return candidates
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مقاطع بسيطة: {str(e)}")
            return []
    
    def _filter_and_rank_clips(self, candidates: List[ExtractedClip]) -> List[ExtractedClip]:
        """تصفية وترتيب المقاطع المرشحة"""
        try:
            # حساب النقاط المجمعة
            for clip in candidates:
                clip.combined_score = (
                    clip.audio_score * self.settings.get("audio_weight", 0.4) +
                    clip.video_score * self.settings.get("video_weight", 0.6)
                )
                if clip.combined_score == 0:
                    clip.combined_score = clip.score
            
            # تصفية المقاطع ذات الجودة المنخفضة
            quality_threshold = self.settings.get("quality_threshold", 0.6)
            filtered = [clip for clip in candidates if clip.combined_score >= quality_threshold]
            
            # إزالة التداخل
            filtered = self._remove_overlapping_clips(filtered)
            
            # ترتيب حسب النقاط
            filtered.sort(key=lambda x: x.combined_score, reverse=True)
            
            # تحديد العدد الأقصى
            max_clips = self.settings.get("max_clips_per_video", 5)
            return filtered[:max_clips]
            
        except Exception as e:
            self.logger.error(f"خطأ في تصفية المقاطع: {str(e)}")
            return candidates
    
    def _remove_overlapping_clips(self, clips: List[ExtractedClip]) -> List[ExtractedClip]:
        """إزالة المقاطع المتداخلة"""
        try:
            if not clips:
                return clips
            
            # ترتيب حسب وقت البداية
            clips.sort(key=lambda x: x.start_time)
            
            filtered = [clips[0]]
            overlap_threshold = self.settings.get("overlap_threshold", 2.0)
            
            for clip in clips[1:]:
                last_clip = filtered[-1]
                
                # حساب التداخل
                overlap_start = max(clip.start_time, last_clip.start_time)
                overlap_end = min(clip.end_time, last_clip.end_time)
                overlap_duration = max(0, overlap_end - overlap_start)
                
                # إذا كان التداخل قليل، أضف المقطع
                if overlap_duration <= overlap_threshold:
                    filtered.append(clip)
                else:
                    # اختر المقطع الأفضل
                    if clip.combined_score > last_clip.combined_score:
                        filtered[-1] = clip
            
            return filtered
            
        except Exception as e:
            self.logger.error(f"خطأ في إزالة التداخل: {str(e)}")
            return clips
    
    def _extract_single_clip(self, clip: ExtractedClip, source_file: str) -> Optional[ExtractedClip]:
        """استخراج مقطع واحد"""
        try:
            # إنشاء اسم ملف الإخراج
            timestamp = int(datetime.now().timestamp())
            output_filename = f"clip_{timestamp}_{int(clip.start_time)}_{int(clip.end_time)}.{self.settings.get('output_format', 'mp4')}"
            output_path = self.clips_dir / output_filename
            
            # تحميل الفيديو
            with mp.VideoFileClip(source_file) as video:
                # قص المقطع
                start_time = clip.start_time
                end_time = clip.end_time
                
                # إضافة حشو إذا كان مطلوباً
                if self.settings.get("add_padding", True):
                    padding = self.settings.get("padding_duration", 1.0)
                    start_time = max(0, start_time - padding)
                    end_time = min(video.duration, end_time + padding)
                
                # قص المقطع
                clip_video = video.subclip(start_time, end_time)
                
                # إضافة تأثيرات الانتقال
                if self.settings.get("fade_duration", 0.5) > 0:
                    fade_duration = min(self.settings.get("fade_duration", 0.5), clip_video.duration / 4)
                    clip_video = clip_video.fadein(fade_duration).fadeout(fade_duration)
                
                # تحديد جودة الإخراج
                quality_settings = self._get_quality_settings()
                
                # حفظ المقطع
                clip_video.write_videofile(
                    str(output_path),
                    **quality_settings,
                    verbose=False,
                    logger=None
                )
                
                # إنشاء صورة مصغرة
                thumbnail_path = ""
                if self.settings.get("create_thumbnails", True):
                    thumbnail_path = self._create_thumbnail(clip_video, clip)
                
                # تحديث معلومات المقطع
                clip.output_path = str(output_path)
                clip.thumbnail_path = thumbnail_path
                clip.start_time = start_time
                clip.end_time = end_time
                clip.duration = end_time - start_time
                
                return clip
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج المقطع: {str(e)}")
            return None
    
    def _get_quality_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات الجودة"""
        quality = self.settings.get("output_quality", "medium")
        
        if quality == "high":
            return {
                "codec": "libx264",
                "bitrate": "5000k",
                "audio_bitrate": "192k"
            }
        elif quality == "low":
            return {
                "codec": "libx264",
                "bitrate": "1000k",
                "audio_bitrate": "128k"
            }
        else:  # medium
            return {
                "codec": "libx264",
                "bitrate": "2500k",
                "audio_bitrate": "160k"
            }
    
    def _create_thumbnail(self, video_clip, clip: ExtractedClip) -> str:
        """إنشاء صورة مصغرة للمقطع"""
        try:
            # اختيار إطار من منتصف المقطع
            thumbnail_time = video_clip.duration / 2
            
            # إنشاء اسم ملف الصورة المصغرة
            timestamp = int(datetime.now().timestamp())
            thumbnail_filename = f"thumb_{timestamp}_{int(clip.start_time)}.jpg"
            thumbnail_path = self.thumbnails_dir / thumbnail_filename
            
            # حفظ الصورة المصغرة
            video_clip.save_frame(str(thumbnail_path), t=thumbnail_time)
            
            return str(thumbnail_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الصورة المصغرة: {str(e)}")
            return ""
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخراج"""
        return self.stats.copy()
    
    def update_extraction_settings(self, settings: Dict[str, Any]):
        """تحديث إعدادات الاستخراج"""
        try:
            self.settings.update(settings)
            self.config_manager.set_setting("ai_settings", "clip_extraction", self.settings)
            self.logger.info("تم تحديث إعدادات الاستخراج")
        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات الاستخراج: {str(e)}")
    
    def cleanup_old_clips(self, days_old: int = 7):
        """تنظيف المقاطع القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            # تنظيف المقاطع
            for clip_file in self.clips_dir.glob("*.mp4"):
                if datetime.fromtimestamp(clip_file.stat().st_mtime) < cutoff_date:
                    clip_file.unlink()
            
            # تنظيف الصور المصغرة
            for thumb_file in self.thumbnails_dir.glob("*.jpg"):
                if datetime.fromtimestamp(thumb_file.stat().st_mtime) < cutoff_date:
                    thumb_file.unlink()
            
            self.logger.info(f"تم تنظيف المقاطع الأقدم من {days_old} أيام")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المقاطع القديمة: {str(e)}")
