{"performance_metrics": {"track_rendering_time": true, "track_memory_usage": true, "track_cpu_usage": true, "track_gpu_usage": false, "track_disk_io": true, "track_cache_efficiency": true}, "monitoring_intervals": {"real_time_interval": 1.0, "summary_interval": 60.0, "report_interval": 300.0}, "alerts": {"high_memory_threshold": 0.85, "high_cpu_threshold": 0.9, "low_disk_space_threshold": 0.95, "slow_rendering_threshold": 30.0}, "logging": {"log_level": "INFO", "log_file": "logs/editing_performance.log", "max_log_size_mb": 100, "backup_count": 5}}