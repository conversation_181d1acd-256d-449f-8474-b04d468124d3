#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محلل الصوت - Audio Analyzer
يحلل الصوت باستخدام Whisper ويحدد اللحظات المهمة والمضحكة
"""

import logging
import os
import json
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
import librosa
import whisper
from pathlib import Path
import re

class AudioSegment:
    """قطعة صوتية مع معلوماتها"""
    
    def __init__(self, start_time: float, end_time: float, 
                 text: str = "", confidence: float = 0.0):
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.text = text
        self.confidence = confidence
        self.emotion_score = 0.0
        self.energy_level = 0.0
        self.speech_rate = 0.0
        self.keywords = []
        self.is_interesting = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "text": self.text,
            "confidence": self.confidence,
            "emotion_score": self.emotion_score,
            "energy_level": self.energy_level,
            "speech_rate": self.speech_rate,
            "keywords": self.keywords,
            "is_interesting": self.is_interesting
        }

class AudioAnalyzer:
    """محلل الصوت الذكي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات التحليل
        self.settings = self._load_audio_settings()
        
        # تحميل نموذج Whisper
        self.whisper_model = None
        self._load_whisper_model()
        
        # كلمات مفتاحية للمحتوى المثير
        self.interesting_keywords = self._load_interesting_keywords()
        
        # كلمات مفتاحية للمشاعر
        self.emotion_keywords = self._load_emotion_keywords()
    
    def _load_audio_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات تحليل الصوت"""
        try:
            return self.config_manager.get_setting("ai_settings", "audio", {
                "whisper_model": "base",  # tiny, base, small, medium, large
                "language": "ar",  # العربية
                "min_segment_duration": 2.0,  # ثواني
                "max_segment_duration": 30.0,  # ثواني
                "energy_threshold": 0.3,
                "speech_rate_threshold": 150,  # كلمة في الدقيقة
                "confidence_threshold": 0.7,
                "enable_emotion_detection": True,
                "enable_keyword_detection": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الصوت: {str(e)}")
            return {
                "whisper_model": "base",
                "language": "ar",
                "min_segment_duration": 2.0,
                "max_segment_duration": 30.0,
                "energy_threshold": 0.3,
                "speech_rate_threshold": 150,
                "confidence_threshold": 0.7,
                "enable_emotion_detection": True,
                "enable_keyword_detection": True
            }
    
    def _load_whisper_model(self):
        """تحميل نموذج Whisper"""
        try:
            model_name = self.settings.get("whisper_model", "base")
            self.logger.info(f"تحميل نموذج Whisper: {model_name}")
            self.whisper_model = whisper.load_model(model_name)
            self.logger.info("تم تحميل نموذج Whisper بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في تحميل نموذج Whisper: {str(e)}")
            # محاولة تحميل النموذج الأساسي
            try:
                self.whisper_model = whisper.load_model("tiny")
                self.logger.info("تم تحميل النموذج الأساسي")
            except:
                self.whisper_model = None
                self.logger.error("فشل في تحميل أي نموذج Whisper")
    
    def _load_interesting_keywords(self) -> List[str]:
        """تحميل الكلمات المفتاحية المثيرة للاهتمام"""
        return [
            # كلمات الضحك والمرح
            "ضحك", "مضحك", "مرح", "كوميدي", "طريف", "مسلي",
            "هههه", "ههههه", "لول", "😂", "🤣",
            
            # كلمات الإثارة والتشويق
            "مذهل", "رائع", "لا يصدق", "مدهش", "عجيب", "غريب",
            "صدمة", "مفاجأة", "سر", "خفي", "مثير",
            
            # كلمات التفاعل
            "شوف", "انظر", "تعال", "هنا", "الآن", "فجأة",
            "لحظة", "انتبه", "اسمع", "قول", "تخيل",
            
            # كلمات الألعاب والترفيه
            "لعبة", "تحدي", "مسابقة", "فوز", "خسارة", "نجح",
            "فشل", "محاولة", "جرب", "اختبار",
            
            # كلمات عاطفية قوية
            "حب", "كره", "خوف", "فرح", "حزن", "غضب",
            "سعادة", "دهشة", "قلق", "راحة"
        ]
    
    def _load_emotion_keywords(self) -> Dict[str, List[str]]:
        """تحميل كلمات المشاعر"""
        return {
            "joy": ["فرح", "سعادة", "مرح", "ضحك", "بهجة", "سرور"],
            "anger": ["غضب", "زعل", "عصبية", "انفعال", "ثورة"],
            "sadness": ["حزن", "أسى", "كآبة", "ألم", "وجع"],
            "fear": ["خوف", "رعب", "فزع", "قلق", "توتر"],
            "surprise": ["دهشة", "مفاجأة", "صدمة", "تعجب"],
            "disgust": ["اشمئزاز", "كره", "نفور", "قرف"]
        }
    
    def analyze_audio(self, file_path: str) -> Dict[str, Any]:
        """تحليل الصوت الرئيسي"""
        try:
            self.logger.info(f"بدء تحليل الصوت: {file_path}")
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"الملف غير موجود: {file_path}")
            
            # استخراج الصوت إذا كان فيديو
            audio_path = self._extract_audio_if_needed(file_path)
            
            # تحليل الصوت باستخدام librosa
            audio_features = self._analyze_audio_features(audio_path)
            
            # تحليل النص باستخدام Whisper
            transcription_result = self._transcribe_audio(audio_path)
            
            # تحليل المقاطع
            segments = self._analyze_segments(transcription_result, audio_features)
            
            # تحديد اللحظات المهمة
            interesting_moments = self._find_interesting_moments(segments)
            
            # حساب التقييم الشامل
            quality_score = self._calculate_audio_quality_score(
                audio_features, segments, interesting_moments
            )
            
            # تنظيف الملفات المؤقتة
            if audio_path != file_path:
                self._cleanup_temp_file(audio_path)
            
            result = {
                "file_path": file_path,
                "duration": audio_features.get("duration", 0.0),
                "sample_rate": audio_features.get("sample_rate", 0),
                "transcription": transcription_result,
                "audio_features": audio_features,
                "segments": [seg.to_dict() for seg in segments],
                "interesting_moments": interesting_moments,
                "quality_score": quality_score,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info("انتهى تحليل الصوت بنجاح")
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الصوت: {str(e)}")
            return {
                "error": str(e),
                "file_path": file_path,
                "quality_score": 0.0,
                "analysis_timestamp": datetime.now().isoformat()
            }
    
    def _extract_audio_if_needed(self, file_path: str) -> str:
        """استخراج الصوت من الفيديو إذا لزم الأمر"""
        try:
            file_extension = Path(file_path).suffix.lower()
            
            # إذا كان ملف صوتي، استخدمه مباشرة
            if file_extension in ['.mp3', '.wav', '.aac', '.ogg', '.m4a']:
                return file_path
            
            # إذا كان فيديو، استخرج الصوت
            if file_extension in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
                import moviepy.editor as mp
                
                # إنشاء ملف مؤقت للصوت
                temp_audio = tempfile.NamedTemporaryFile(
                    suffix='.wav', delete=False
                )
                temp_audio.close()
                
                # استخراج الصوت
                video = mp.VideoFileClip(file_path)
                audio = video.audio
                audio.write_audiofile(temp_audio.name, verbose=False, logger=None)
                
                # تنظيف الموارد
                audio.close()
                video.close()
                
                return temp_audio.name
            
            raise ValueError(f"نوع ملف غير مدعوم: {file_extension}")
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الصوت: {str(e)}")
            raise
    
    def _analyze_audio_features(self, audio_path: str) -> Dict[str, Any]:
        """تحليل خصائص الصوت باستخدام librosa"""
        try:
            # تحميل الصوت
            y, sr = librosa.load(audio_path, sr=None)
            duration = len(y) / sr
            
            # حساب الطاقة
            energy = np.sum(y ** 2) / len(y)
            
            # حساب الطيف الترددي
            stft = librosa.stft(y)
            magnitude = np.abs(stft)
            
            # حساب الخصائص الطيفية
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]
            zero_crossing_rate = librosa.feature.zero_crossing_rate(y)[0]
            
            # حساب MFCC
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            
            # حساب الإيقاع
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            
            # تحديد المناطق الصامتة والنشطة
            intervals = librosa.effects.split(y, top_db=20)
            
            return {
                "duration": duration,
                "sample_rate": sr,
                "energy": float(energy),
                "tempo": float(tempo),
                "spectral_centroid_mean": float(np.mean(spectral_centroids)),
                "spectral_rolloff_mean": float(np.mean(spectral_rolloff)),
                "zero_crossing_rate_mean": float(np.mean(zero_crossing_rate)),
                "mfcc_mean": [float(x) for x in np.mean(mfccs, axis=1)],
                "active_intervals": len(intervals),
                "silence_ratio": 1.0 - (len(intervals) * np.mean([end - start for start, end in intervals]) / len(y))
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل خصائص الصوت: {str(e)}")
            return {"error": str(e), "duration": 0.0, "sample_rate": 0}
    
    def _transcribe_audio(self, audio_path: str) -> Dict[str, Any]:
        """تحويل الصوت إلى نص باستخدام Whisper"""
        try:
            if not self.whisper_model:
                raise ValueError("نموذج Whisper غير متاح")
            
            # تحويل الصوت إلى نص
            result = self.whisper_model.transcribe(
                audio_path,
                language=self.settings.get("language", "ar"),
                word_timestamps=True
            )
            
            return {
                "text": result.get("text", ""),
                "language": result.get("language", ""),
                "segments": result.get("segments", []),
                "words": result.get("words", []) if "words" in result else []
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحويل الصوت إلى نص: {str(e)}")
            return {
                "text": "",
                "language": "",
                "segments": [],
                "words": [],
                "error": str(e)
            }
    
    def _analyze_segments(self, transcription: Dict[str, Any], 
                         audio_features: Dict[str, Any]) -> List[AudioSegment]:
        """تحليل مقاطع الصوت"""
        segments = []
        
        try:
            whisper_segments = transcription.get("segments", [])
            
            for seg_data in whisper_segments:
                segment = AudioSegment(
                    start_time=seg_data.get("start", 0.0),
                    end_time=seg_data.get("end", 0.0),
                    text=seg_data.get("text", "").strip(),
                    confidence=seg_data.get("avg_logprob", 0.0)
                )
                
                # تحليل المشاعر والكلمات المفتاحية
                if self.settings.get("enable_emotion_detection", True):
                    segment.emotion_score = self._analyze_emotion(segment.text)
                
                if self.settings.get("enable_keyword_detection", True):
                    segment.keywords = self._extract_keywords(segment.text)
                    segment.is_interesting = self._is_segment_interesting(segment.text)
                
                # حساب معدل الكلام
                word_count = len(segment.text.split())
                if segment.duration > 0:
                    segment.speech_rate = (word_count / segment.duration) * 60  # كلمة/دقيقة
                
                # تقدير مستوى الطاقة (تقريبي)
                segment.energy_level = min(1.0, audio_features.get("energy", 0.0) * 10)
                
                segments.append(segment)
            
            return segments
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المقاطع: {str(e)}")
            return []
    
    def _analyze_emotion(self, text: str) -> float:
        """تحليل المشاعر في النص"""
        try:
            text_lower = text.lower()
            emotion_scores = {}
            
            # حساب نقاط المشاعر
            for emotion, keywords in self.emotion_keywords.items():
                score = 0
                for keyword in keywords:
                    score += text_lower.count(keyword)
                emotion_scores[emotion] = score
            
            # تحديد المشاعر الإيجابية مقابل السلبية
            positive_emotions = emotion_scores.get("joy", 0) + emotion_scores.get("surprise", 0)
            negative_emotions = (emotion_scores.get("anger", 0) + 
                               emotion_scores.get("sadness", 0) + 
                               emotion_scores.get("fear", 0) + 
                               emotion_scores.get("disgust", 0))
            
            total_emotions = positive_emotions + negative_emotions
            if total_emotions == 0:
                return 0.5  # محايد
            
            # نقاط أعلى للمشاعر الإيجابية
            return min(1.0, (positive_emotions * 1.5 + negative_emotions * 0.5) / total_emotions)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المشاعر: {str(e)}")
            return 0.5
    
    def _extract_keywords(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية"""
        try:
            text_lower = text.lower()
            found_keywords = []
            
            for keyword in self.interesting_keywords:
                if keyword in text_lower:
                    found_keywords.append(keyword)
            
            return found_keywords
            
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الكلمات المفتاحية: {str(e)}")
            return []
    
    def _is_segment_interesting(self, text: str) -> bool:
        """تحديد ما إذا كان المقطع مثيراً للاهتمام"""
        try:
            text_lower = text.lower()
            
            # البحث عن كلمات مثيرة للاهتمام
            interesting_count = sum(1 for keyword in self.interesting_keywords 
                                  if keyword in text_lower)
            
            # البحث عن علامات الإثارة
            excitement_patterns = [
                r'[!]{2,}',  # علامات تعجب متعددة
                r'[؟]{2,}',  # علامات استفهام متعددة
                r'ه{3,}',    # ضحك
                r'و{3,}',    # تعجب
                r'ا{3,}'     # استطالة
            ]
            
            excitement_score = sum(1 for pattern in excitement_patterns 
                                 if re.search(pattern, text))
            
            # تحديد العتبة
            return interesting_count >= 2 or excitement_score >= 1
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد الإثارة: {str(e)}")
            return False
    
    def _find_interesting_moments(self, segments: List[AudioSegment]) -> List[Dict[str, Any]]:
        """تحديد اللحظات المهمة"""
        interesting_moments = []
        
        try:
            for segment in segments:
                # حساب نقاط الإثارة
                interest_score = 0.0
                
                # نقاط للكلمات المفتاحية
                interest_score += len(segment.keywords) * 0.2
                
                # نقاط للمشاعر الإيجابية
                interest_score += segment.emotion_score * 0.3
                
                # نقاط لمعدل الكلام السريع (إثارة)
                if segment.speech_rate > self.settings.get("speech_rate_threshold", 150):
                    interest_score += 0.2
                
                # نقاط للطاقة العالية
                if segment.energy_level > self.settings.get("energy_threshold", 0.3):
                    interest_score += 0.2
                
                # نقاط للثقة العالية في التحويل
                if segment.confidence > self.settings.get("confidence_threshold", 0.7):
                    interest_score += 0.1
                
                # إضافة اللحظة إذا كانت مثيرة
                if interest_score > 0.5 or segment.is_interesting:
                    interesting_moments.append({
                        "start_time": segment.start_time,
                        "end_time": segment.end_time,
                        "duration": segment.duration,
                        "text": segment.text,
                        "score": min(1.0, interest_score),
                        "keywords": segment.keywords,
                        "emotion_score": segment.emotion_score,
                        "speech_rate": segment.speech_rate,
                        "energy_level": segment.energy_level
                    })
            
            # ترتيب حسب النقاط
            interesting_moments.sort(key=lambda x: x["score"], reverse=True)
            
            return interesting_moments
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد اللحظات المهمة: {str(e)}")
            return []
    
    def _calculate_audio_quality_score(self, audio_features: Dict[str, Any],
                                     segments: List[AudioSegment],
                                     interesting_moments: List[Dict[str, Any]]) -> float:
        """حساب تقييم جودة الصوت"""
        try:
            score = 0.0
            
            # تقييم الخصائص التقنية (30%)
            technical_score = 0.0
            
            # جودة الصوت
            if audio_features.get("energy", 0) > 0.01:
                technical_score += 0.3
            
            # وضوح الصوت (نسبة الصمت)
            silence_ratio = audio_features.get("silence_ratio", 1.0)
            if silence_ratio < 0.5:  # صوت نشط
                technical_score += 0.4
            
            # استقرار الطيف الترددي
            if audio_features.get("spectral_centroid_mean", 0) > 1000:
                technical_score += 0.3
            
            score += technical_score * 0.3
            
            # تقييم المحتوى (50%)
            content_score = 0.0
            
            # عدد المقاطع المثيرة
            if interesting_moments:
                content_score += min(0.5, len(interesting_moments) * 0.1)
            
            # متوسط نقاط المشاعر
            if segments:
                avg_emotion = sum(seg.emotion_score for seg in segments) / len(segments)
                content_score += avg_emotion * 0.3
            
            # تنوع الكلمات المفتاحية
            all_keywords = set()
            for seg in segments:
                all_keywords.update(seg.keywords)
            content_score += min(0.2, len(all_keywords) * 0.02)
            
            score += content_score * 0.5
            
            # تقييم التفاعل (20%)
            interaction_score = 0.0
            
            # معدل الكلام المتنوع
            speech_rates = [seg.speech_rate for seg in segments if seg.speech_rate > 0]
            if speech_rates:
                rate_variance = np.var(speech_rates)
                interaction_score += min(0.5, rate_variance / 1000)
            
            # وجود لحظات عالية الطاقة
            high_energy_segments = [seg for seg in segments if seg.energy_level > 0.7]
            interaction_score += min(0.5, len(high_energy_segments) * 0.1)
            
            score += interaction_score * 0.2
            
            return min(1.0, max(0.0, score))
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب تقييم الصوت: {str(e)}")
            return 0.0
    
    def _cleanup_temp_file(self, file_path: str):
        """تنظيف الملفات المؤقتة"""
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الملف المؤقت: {str(e)}")
