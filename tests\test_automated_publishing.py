#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات نظام النشر التلقائي
Tests for Automated Publishing System
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import requests

# استيراد الوحدات المطلوب اختبارها
try:
    from src.automated_publishing.tiktok_publisher import TikTokPublisher
    from src.automated_publishing.platform_manager import PlatformManager
    from src.automated_publishing.content_scheduler import ContentScheduler
    from src.automated_publishing.upload_manager import UploadManager
except ImportError:
    # في حالة عدم وجود الوحدات، سنقوم بإنشاء كائنات وهمية للاختبار
    TikTokPublisher = Mock
    PlatformManager = Mock
    ContentScheduler = Mock
    UploadManager = Mock

class TestTikTokPublisher:
    """اختبارات ناشر TikTok"""
    
    @pytest.fixture
    def tiktok_publisher(self, temp_dir):
        """إنشاء كائن TikTokPublisher للاختبار"""
        if TikTokPublisher == Mock:
            return Mock()
        return TikTokPublisher(str(temp_dir))
    
    @pytest.fixture
    def sample_video_file(self, temp_dir):
        """إنشاء ملف فيديو تجريبي"""
        video_path = temp_dir / "test_video.mp4"
        video_path.write_bytes(b"fake_video_data")
        return str(video_path)
    
    def test_initialization(self, tiktok_publisher):
        """اختبار تهيئة ناشر TikTok"""
        assert tiktok_publisher is not None
        if hasattr(tiktok_publisher, 'api_client'):
            assert hasattr(tiktok_publisher, 'api_client')
            assert hasattr(tiktok_publisher, 'config')
    
    @patch('requests.post')
    def test_authenticate_success(self, mock_post, tiktok_publisher):
        """اختبار المصادقة الناجحة"""
        # إعداد الاستجابة الوهمية
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_token_123",
            "expires_in": 3600,
            "token_type": "Bearer"
        }
        mock_post.return_value = mock_response
        
        if hasattr(tiktok_publisher, 'authenticate'):
            result = tiktok_publisher.authenticate("test_client_id", "test_client_secret")
            assert result == True
            mock_post.assert_called_once()
    
    @patch('requests.post')
    def test_authenticate_failure(self, mock_post, tiktok_publisher):
        """اختبار فشل المصادقة"""
        # إعداد استجابة فاشلة
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {"error": "invalid_credentials"}
        mock_post.return_value = mock_response
        
        if hasattr(tiktok_publisher, 'authenticate'):
            result = tiktok_publisher.authenticate("invalid_id", "invalid_secret")
            assert result == False
    
    @patch('requests.post')
    def test_upload_video_success(self, mock_post, tiktok_publisher, sample_video_file):
        """اختبار رفع الفيديو بنجاح"""
        # إعداد الاستجابة الوهمية
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "video_id": "video_123",
            "status": "uploaded",
            "url": "https://tiktok.com/video/123"
        }
        mock_post.return_value = mock_response
        
        video_metadata = {
            "title": "فيديو تجريبي",
            "description": "وصف الفيديو",
            "tags": ["تجربة", "اختبار"],
            "privacy": "public"
        }
        
        if hasattr(tiktok_publisher, 'upload_video'):
            result = tiktok_publisher.upload_video(sample_video_file, video_metadata)
            assert result is not None
            assert "video_id" in result
            mock_post.assert_called_once()
    
    def test_validate_video_metadata(self, tiktok_publisher):
        """اختبار التحقق من صحة بيانات الفيديو"""
        valid_metadata = {
            "title": "عنوان صحيح",
            "description": "وصف صحيح",
            "tags": ["تاج1", "تاج2"],
            "privacy": "public"
        }
        
        invalid_metadata = {
            "title": "",  # عنوان فارغ
            "description": "وصف",
            "privacy": "invalid_privacy"  # خصوصية غير صحيحة
        }
        
        if hasattr(tiktok_publisher, 'validate_video_metadata'):
            assert tiktok_publisher.validate_video_metadata(valid_metadata) == True
            assert tiktok_publisher.validate_video_metadata(invalid_metadata) == False
    
    @patch('requests.get')
    def test_get_upload_status(self, mock_get, tiktok_publisher):
        """اختبار الحصول على حالة الرفع"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "video_id": "video_123",
            "status": "processing",
            "progress": 75
        }
        mock_get.return_value = mock_response
        
        if hasattr(tiktok_publisher, 'get_upload_status'):
            result = tiktok_publisher.get_upload_status("video_123")
            assert result is not None
            assert result["status"] == "processing"

class TestPlatformManager:
    """اختبارات مدير المنصات"""
    
    @pytest.fixture
    def platform_manager(self, temp_dir):
        """إنشاء كائن PlatformManager للاختبار"""
        if PlatformManager == Mock:
            return Mock()
        return PlatformManager(str(temp_dir))
    
    def test_initialization(self, platform_manager):
        """اختبار تهيئة مدير المنصات"""
        assert platform_manager is not None
        if hasattr(platform_manager, 'platforms'):
            assert hasattr(platform_manager, 'platforms')
    
    def test_add_platform(self, platform_manager):
        """اختبار إضافة منصة جديدة"""
        platform_config = {
            "name": "tiktok",
            "api_key": "test_api_key",
            "secret": "test_secret",
            "enabled": True
        }
        
        if hasattr(platform_manager, 'add_platform'):
            result = platform_manager.add_platform(platform_config)
            assert result == True
    
    def test_remove_platform(self, platform_manager):
        """اختبار إزالة منصة"""
        if hasattr(platform_manager, 'remove_platform'):
            result = platform_manager.remove_platform("tiktok")
            assert result == True
    
    def test_get_platform_status(self, platform_manager):
        """اختبار الحصول على حالة المنصة"""
        if hasattr(platform_manager, 'get_platform_status'):
            status = platform_manager.get_platform_status("tiktok")
            assert status is not None
            if isinstance(status, dict):
                assert "name" in status
                assert "status" in status
    
    def test_publish_to_multiple_platforms(self, platform_manager, sample_video_file):
        """اختبار النشر على منصات متعددة"""
        content_data = {
            "video_path": sample_video_file,
            "title": "فيديو متعدد المنصات",
            "description": "وصف الفيديو",
            "tags": ["متعدد", "منصات"]
        }
        
        platforms = ["tiktok", "instagram", "youtube"]
        
        if hasattr(platform_manager, 'publish_to_multiple_platforms'):
            with patch.object(platform_manager, '_publish_to_platform') as mock_publish:
                mock_publish.return_value = {"success": True, "video_id": "123"}
                
                results = platform_manager.publish_to_multiple_platforms(content_data, platforms)
                assert results is not None
                assert len(results) == len(platforms)

class TestContentScheduler:
    """اختبارات مجدول المحتوى"""
    
    @pytest.fixture
    def content_scheduler(self, temp_dir):
        """إنشاء كائن ContentScheduler للاختبار"""
        if ContentScheduler == Mock:
            return Mock()
        return ContentScheduler(str(temp_dir))
    
    def test_initialization(self, content_scheduler):
        """اختبار تهيئة مجدول المحتوى"""
        assert content_scheduler is not None
        if hasattr(content_scheduler, 'scheduled_content'):
            assert hasattr(content_scheduler, 'scheduled_content')
    
    def test_schedule_content(self, content_scheduler):
        """اختبار جدولة المحتوى"""
        content_item = {
            "video_path": "test_video.mp4",
            "title": "فيديو مجدول",
            "description": "وصف الفيديو المجدول",
            "platforms": ["tiktok"],
            "scheduled_time": "2024-12-31T23:59:59Z"
        }
        
        if hasattr(content_scheduler, 'schedule_content'):
            result = content_scheduler.schedule_content(content_item)
            assert result is not None
            if isinstance(result, dict):
                assert "schedule_id" in result
    
    def test_cancel_scheduled_content(self, content_scheduler):
        """اختبار إلغاء المحتوى المجدول"""
        if hasattr(content_scheduler, 'cancel_scheduled_content'):
            result = content_scheduler.cancel_scheduled_content("schedule_123")
            assert result == True
    
    def test_get_scheduled_content(self, content_scheduler):
        """اختبار الحصول على المحتوى المجدول"""
        if hasattr(content_scheduler, 'get_scheduled_content'):
            scheduled_items = content_scheduler.get_scheduled_content()
            assert scheduled_items is not None
            assert isinstance(scheduled_items, list)
    
    def test_process_due_content(self, content_scheduler):
        """اختبار معالجة المحتوى المستحق للنشر"""
        if hasattr(content_scheduler, 'process_due_content'):
            with patch('datetime.datetime') as mock_datetime:
                mock_datetime.now.return_value.isoformat.return_value = "2024-12-31T23:59:59Z"
                
                results = content_scheduler.process_due_content()
                assert results is not None
                assert isinstance(results, list)

class TestUploadManager:
    """اختبارات مدير الرفع"""
    
    @pytest.fixture
    def upload_manager(self, temp_dir):
        """إنشاء كائن UploadManager للاختبار"""
        if UploadManager == Mock:
            return Mock()
        return UploadManager(str(temp_dir))
    
    def test_initialization(self, upload_manager):
        """اختبار تهيئة مدير الرفع"""
        assert upload_manager is not None
        if hasattr(upload_manager, 'upload_queue'):
            assert hasattr(upload_manager, 'upload_queue')
    
    def test_add_to_upload_queue(self, upload_manager, sample_video_file):
        """اختبار إضافة ملف لقائمة الرفع"""
        upload_item = {
            "file_path": sample_video_file,
            "platform": "tiktok",
            "metadata": {
                "title": "فيديو للرفع",
                "description": "وصف الفيديو"
            },
            "priority": 1
        }
        
        if hasattr(upload_manager, 'add_to_upload_queue'):
            result = upload_manager.add_to_upload_queue(upload_item)
            assert result is not None
            if isinstance(result, dict):
                assert "queue_id" in result
    
    def test_process_upload_queue(self, upload_manager):
        """اختبار معالجة قائمة الرفع"""
        if hasattr(upload_manager, 'process_upload_queue'):
            with patch.object(upload_manager, '_upload_file') as mock_upload:
                mock_upload.return_value = {"success": True, "video_id": "123"}
                
                results = upload_manager.process_upload_queue()
                assert results is not None
                assert isinstance(results, list)
    
    def test_get_upload_progress(self, upload_manager):
        """اختبار الحصول على تقدم الرفع"""
        if hasattr(upload_manager, 'get_upload_progress'):
            progress = upload_manager.get_upload_progress("upload_123")
            assert progress is not None
            if isinstance(progress, dict):
                assert "status" in progress
                assert "progress_percentage" in progress
    
    def test_retry_failed_upload(self, upload_manager):
        """اختبار إعادة محاولة الرفع الفاشل"""
        if hasattr(upload_manager, 'retry_failed_upload'):
            result = upload_manager.retry_failed_upload("upload_123")
            assert result == True

@pytest.mark.integration
class TestPublishingSystemIntegration:
    """اختبارات التكامل بين أنظمة النشر"""
    
    @pytest.fixture
    def publishing_systems(self, temp_dir):
        """إنشاء جميع أنظمة النشر"""
        return {
            "tiktok_publisher": TikTokPublisher(str(temp_dir)) if TikTokPublisher != Mock else Mock(),
            "platform_manager": PlatformManager(str(temp_dir)) if PlatformManager != Mock else Mock(),
            "content_scheduler": ContentScheduler(str(temp_dir)) if ContentScheduler != Mock else Mock(),
            "upload_manager": UploadManager(str(temp_dir)) if UploadManager != Mock else Mock()
        }
    
    def test_complete_publishing_workflow(self, publishing_systems, sample_video_file):
        """اختبار سير عمل النشر الكامل"""
        platform_manager = publishing_systems["platform_manager"]
        content_scheduler = publishing_systems["content_scheduler"]
        upload_manager = publishing_systems["upload_manager"]
        
        # محاكاة سير عمل النشر الكامل
        content_data = {
            "video_path": sample_video_file,
            "title": "فيديو للنشر الكامل",
            "description": "وصف شامل للفيديو",
            "tags": ["اختبار", "تكامل"],
            "platforms": ["tiktok"],
            "scheduled_time": "2024-12-31T23:59:59Z"
        }
        
        # 1. جدولة المحتوى
        if hasattr(content_scheduler, 'schedule_content'):
            schedule_result = content_scheduler.schedule_content(content_data)
            assert schedule_result is not None
        
        # 2. إضافة للرفع
        if hasattr(upload_manager, 'add_to_upload_queue'):
            upload_item = {
                "file_path": sample_video_file,
                "platform": "tiktok",
                "metadata": content_data,
                "priority": 1
            }
            queue_result = upload_manager.add_to_upload_queue(upload_item)
            assert queue_result is not None
        
        # 3. النشر على المنصات
        if hasattr(platform_manager, 'publish_to_multiple_platforms'):
            with patch.object(platform_manager, '_publish_to_platform') as mock_publish:
                mock_publish.return_value = {"success": True, "video_id": "123"}
                
                publish_results = platform_manager.publish_to_multiple_platforms(
                    content_data, ["tiktok"]
                )
                assert publish_results is not None

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
