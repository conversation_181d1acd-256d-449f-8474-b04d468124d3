#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء المتقدم - Advanced Performance Monitor
يراقب أداء النظام والعمليات في الوقت الفعلي
"""

import psutil
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import json
import statistics
from collections import deque, defaultdict

@dataclass
class PerformanceMetric:
    """مقياس الأداء"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    open_files: int
    response_time_ms: float = 0.0
    operation_type: str = ""
    success: bool = True
    error_message: str = ""

@dataclass
class SystemAlert:
    """تنبيه النظام"""
    alert_id: str
    timestamp: datetime
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    category: str  # PERFORMANCE, MEMORY, DISK, NETWORK, ERROR
    message: str
    metric_value: float
    threshold: float
    resolved: bool = False
    resolved_at: Optional[datetime] = None

class PerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # إعدادات المراقبة
        self.monitoring_settings = self._load_monitoring_settings()
        
        # بيانات المراقبة
        self.metrics_history = deque(maxlen=self.monitoring_settings.get("max_history_size", 1000))
        self.operation_metrics = defaultdict(list)
        self.alerts = []
        self.alert_handlers = []
        
        # إحصائيات الأداء
        self.performance_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "average_response_time": 0.0,
            "peak_cpu_usage": 0.0,
            "peak_memory_usage": 0.0,
            "total_alerts": 0,
            "critical_alerts": 0,
            "monitoring_start_time": datetime.now(),
            "last_updated": datetime.now()
        }
        
        # عتبات التنبيه
        self.alert_thresholds = self.monitoring_settings.get("alert_thresholds", {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_usage_percent": 90.0,
            "response_time_ms": 5000.0,
            "error_rate_percent": 10.0
        })
        
        # حالة المراقبة
        self.monitoring_active = False
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # معلومات النظام الأساسية
        self.process = psutil.Process()
        self.system_info = self._get_system_info()
        
        # ملفات البيانات
        self.data_dir = Path.home() / ".smart_content_app" / "monitoring"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.metrics_file = self.data_dir / "performance_metrics.json"
        self.alerts_file = self.data_dir / "alerts.json"
        self.stats_file = self.data_dir / "performance_stats.json"
        
        # تحميل البيانات المحفوظة
        self._load_saved_data()
        
        self.logger.info("تم تهيئة مراقب الأداء المتقدم")
    
    def _load_monitoring_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المراقبة"""
        try:
            return self.config_manager.get_setting("monitoring", "performance", {
                "monitoring_interval": 5,  # ثواني
                "max_history_size": 1000,
                "enable_real_time_alerts": True,
                "save_metrics_interval": 300,  # 5 دقائق
                "cleanup_old_data_days": 30,
                "alert_thresholds": {
                    "cpu_percent": 80.0,
                    "memory_percent": 85.0,
                    "disk_usage_percent": 90.0,
                    "response_time_ms": 5000.0,
                    "error_rate_percent": 10.0
                }
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المراقبة: {str(e)}")
            return {}
    
    def _get_system_info(self) -> Dict[str, Any]:
        """الحصول على معلومات النظام الأساسية"""
        try:
            return {
                "cpu_count": psutil.cpu_count(),
                "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {},
                "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                "disk_total_gb": psutil.disk_usage('/').total / (1024**3),
                "platform": psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else "unknown",
                "boot_time": datetime.fromtimestamp(psutil.boot_time()),
                "python_version": f"{psutil.version_info}",
                "process_id": self.process.pid
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات النظام: {str(e)}")
            return {}
    
    def _load_saved_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل الإحصائيات
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    # تحديث الإحصائيات مع الحفاظ على البيانات الحالية
                    for key, value in saved_stats.items():
                        if key in self.performance_stats:
                            self.performance_stats[key] = value
            
            # تحميل التنبيهات الحديثة
            if self.alerts_file.exists():
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts_data = json.load(f)
                    # تحميل التنبيهات من آخر 24 ساعة فقط
                    cutoff_time = datetime.now() - timedelta(hours=24)
                    for alert_data in alerts_data:
                        alert_time = datetime.fromisoformat(alert_data["timestamp"])
                        if alert_time > cutoff_time:
                            alert = SystemAlert(**alert_data)
                            self.alerts.append(alert)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")
    
    def start_monitoring(self):
        """بدء المراقبة"""
        try:
            if self.monitoring_active:
                self.logger.warning("المراقبة نشطة بالفعل")
                return
            
            self.monitoring_active = True
            self.stop_event.clear()
            
            # بدء خيط المراقبة
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="PerformanceMonitor"
            )
            self.monitoring_thread.start()
            
            self.logger.info("تم بدء مراقبة الأداء")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء المراقبة: {str(e)}")
            self.monitoring_active = False
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        try:
            if not self.monitoring_active:
                return
            
            self.monitoring_active = False
            self.stop_event.set()
            
            # انتظار انتهاء خيط المراقبة
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5)
            
            # حفظ البيانات النهائية
            self._save_all_data()
            
            self.logger.info("تم إيقاف مراقبة الأداء")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المراقبة: {str(e)}")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        try:
            interval = self.monitoring_settings.get("monitoring_interval", 5)
            save_interval = self.monitoring_settings.get("save_metrics_interval", 300)
            last_save_time = time.time()
            
            while not self.stop_event.wait(interval):
                try:
                    # جمع مقاييس الأداء
                    metric = self._collect_performance_metric()
                    self.metrics_history.append(metric)
                    
                    # فحص التنبيهات
                    if self.monitoring_settings.get("enable_real_time_alerts", True):
                        self._check_alerts(metric)
                    
                    # تحديث الإحصائيات
                    self._update_performance_stats(metric)
                    
                    # حفظ البيانات دورياً
                    current_time = time.time()
                    if current_time - last_save_time >= save_interval:
                        self._save_all_data()
                        last_save_time = current_time
                    
                except Exception as e:
                    self.logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"خطأ في حلقة المراقبة الرئيسية: {str(e)}")
        finally:
            self.monitoring_active = False
    
    def _collect_performance_metric(self) -> PerformanceMetric:
        """جمع مقياس أداء حالي"""
        try:
            # معلومات المعالج والذاكرة
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # معلومات القرص
            disk_io = self.process.io_counters()
            disk_read_mb = disk_io.read_bytes / (1024**2)
            disk_write_mb = disk_io.write_bytes / (1024**2)
            
            # معلومات الشبكة (للنظام بالكامل)
            net_io = psutil.net_io_counters()
            net_sent_mb = net_io.bytes_sent / (1024**2)
            net_recv_mb = net_io.bytes_recv / (1024**2)
            
            # معلومات الخيوط والملفات
            active_threads = self.process.num_threads()
            try:
                open_files = len(self.process.open_files())
            except:
                open_files = 0
            
            return PerformanceMetric(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_info.rss / (1024**2),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=net_sent_mb,
                network_recv_mb=net_recv_mb,
                active_threads=active_threads,
                open_files=open_files
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في جمع مقاييس الأداء: {str(e)}")
            return PerformanceMetric(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                active_threads=0,
                open_files=0
            )

    def _check_alerts(self, metric: PerformanceMetric):
        """فحص التنبيهات بناءً على المقاييس الحالية"""
        try:
            alerts_to_create = []

            # فحص استخدام المعالج
            if metric.cpu_percent > self.alert_thresholds.get("cpu_percent", 80.0):
                alerts_to_create.append({
                    "severity": "HIGH" if metric.cpu_percent > 90 else "MEDIUM",
                    "category": "PERFORMANCE",
                    "message": f"استخدام المعالج عالي: {metric.cpu_percent:.1f}%",
                    "metric_value": metric.cpu_percent,
                    "threshold": self.alert_thresholds.get("cpu_percent", 80.0)
                })

            # فحص استخدام الذاكرة
            if metric.memory_percent > self.alert_thresholds.get("memory_percent", 85.0):
                alerts_to_create.append({
                    "severity": "HIGH" if metric.memory_percent > 95 else "MEDIUM",
                    "category": "MEMORY",
                    "message": f"استخدام الذاكرة عالي: {metric.memory_percent:.1f}%",
                    "metric_value": metric.memory_percent,
                    "threshold": self.alert_thresholds.get("memory_percent", 85.0)
                })

            # فحص وقت الاستجابة
            if metric.response_time_ms > self.alert_thresholds.get("response_time_ms", 5000.0):
                alerts_to_create.append({
                    "severity": "MEDIUM",
                    "category": "PERFORMANCE",
                    "message": f"وقت استجابة بطيء: {metric.response_time_ms:.0f}ms",
                    "metric_value": metric.response_time_ms,
                    "threshold": self.alert_thresholds.get("response_time_ms", 5000.0)
                })

            # فحص عدد الملفات المفتوحة
            if metric.open_files > 100:
                alerts_to_create.append({
                    "severity": "LOW",
                    "category": "PERFORMANCE",
                    "message": f"عدد ملفات مفتوحة كثير: {metric.open_files}",
                    "metric_value": metric.open_files,
                    "threshold": 100
                })

            # إنشاء التنبيهات
            for alert_data in alerts_to_create:
                self._create_alert(**alert_data)

        except Exception as e:
            self.logger.error(f"خطأ في فحص التنبيهات: {str(e)}")

    def _create_alert(self, severity: str, category: str, message: str,
                     metric_value: float, threshold: float):
        """إنشاء تنبيه جديد"""
        try:
            # تجنب التنبيهات المكررة
            recent_alerts = [a for a in self.alerts
                           if a.category == category and
                           a.message == message and
                           not a.resolved and
                           (datetime.now() - a.timestamp).seconds < 300]  # آخر 5 دقائق

            if recent_alerts:
                return

            # إنشاء التنبيه
            alert = SystemAlert(
                alert_id=f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.alerts)}",
                timestamp=datetime.now(),
                severity=severity,
                category=category,
                message=message,
                metric_value=metric_value,
                threshold=threshold
            )

            self.alerts.append(alert)
            self.performance_stats["total_alerts"] += 1

            if severity == "CRITICAL":
                self.performance_stats["critical_alerts"] += 1

            # تشغيل معالجات التنبيه
            self._trigger_alert_handlers(alert)

            self.logger.warning(f"تنبيه جديد [{severity}]: {message}")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التنبيه: {str(e)}")

    def _trigger_alert_handlers(self, alert: SystemAlert):
        """تشغيل معالجات التنبيه"""
        try:
            for handler in self.alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    self.logger.error(f"خطأ في معالج التنبيه: {str(e)}")
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل معالجات التنبيه: {str(e)}")

    def _update_performance_stats(self, metric: PerformanceMetric):
        """تحديث إحصائيات الأداء"""
        try:
            # تحديث الذروات
            self.performance_stats["peak_cpu_usage"] = max(
                self.performance_stats["peak_cpu_usage"], metric.cpu_percent
            )
            self.performance_stats["peak_memory_usage"] = max(
                self.performance_stats["peak_memory_usage"], metric.memory_percent
            )

            # تحديث متوسط وقت الاستجابة
            if metric.response_time_ms > 0:
                current_avg = self.performance_stats["average_response_time"]
                total_ops = self.performance_stats["total_operations"]

                if total_ops > 0:
                    self.performance_stats["average_response_time"] = (
                        (current_avg * total_ops + metric.response_time_ms) / (total_ops + 1)
                    )
                else:
                    self.performance_stats["average_response_time"] = metric.response_time_ms

                self.performance_stats["total_operations"] += 1

                if metric.success:
                    self.performance_stats["successful_operations"] += 1
                else:
                    self.performance_stats["failed_operations"] += 1

            self.performance_stats["last_updated"] = datetime.now()

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إحصائيات الأداء: {str(e)}")

    def record_operation(self, operation_type: str, start_time: float,
                        success: bool = True, error_message: str = ""):
        """تسجيل عملية مع قياس الأداء"""
        try:
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000

            # إنشاء مقياس للعملية
            metric = PerformanceMetric(
                timestamp=datetime.now(),
                cpu_percent=self.process.cpu_percent(),
                memory_percent=self.process.memory_percent(),
                memory_used_mb=self.process.memory_info().rss / (1024**2),
                disk_io_read_mb=0.0,  # سيتم تحديثها في المراقبة العامة
                disk_io_write_mb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                active_threads=self.process.num_threads(),
                open_files=0,
                response_time_ms=response_time_ms,
                operation_type=operation_type,
                success=success,
                error_message=error_message
            )

            # حفظ في تاريخ العمليات
            self.operation_metrics[operation_type].append(metric)

            # الاحتفاظ بآخر 100 عملية لكل نوع
            if len(self.operation_metrics[operation_type]) > 100:
                self.operation_metrics[operation_type] = self.operation_metrics[operation_type][-100:]

            # تحديث الإحصائيات
            self._update_performance_stats(metric)

            # فحص التنبيهات
            if self.monitoring_settings.get("enable_real_time_alerts", True):
                self._check_alerts(metric)

            self.logger.debug(f"تم تسجيل عملية {operation_type}: {response_time_ms:.2f}ms")

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل العملية: {str(e)}")

    def add_alert_handler(self, handler: Callable[[SystemAlert], None]):
        """إضافة معالج تنبيه"""
        try:
            if handler not in self.alert_handlers:
                self.alert_handlers.append(handler)
                self.logger.info("تم إضافة معالج تنبيه جديد")
        except Exception as e:
            self.logger.error(f"خطأ في إضافة معالج التنبيه: {str(e)}")

    def remove_alert_handler(self, handler: Callable[[SystemAlert], None]):
        """إزالة معالج تنبيه"""
        try:
            if handler in self.alert_handlers:
                self.alert_handlers.remove(handler)
                self.logger.info("تم إزالة معالج التنبيه")
        except Exception as e:
            self.logger.error(f"خطأ في إزالة معالج التنبيه: {str(e)}")

    def resolve_alert(self, alert_id: str):
        """حل تنبيه"""
        try:
            for alert in self.alerts:
                if alert.alert_id == alert_id and not alert.resolved:
                    alert.resolved = True
                    alert.resolved_at = datetime.now()
                    self.logger.info(f"تم حل التنبيه: {alert_id}")
                    return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في حل التنبيه: {str(e)}")
            return False

    def get_current_metrics(self) -> Optional[PerformanceMetric]:
        """الحصول على المقاييس الحالية"""
        try:
            if self.metrics_history:
                return self.metrics_history[-1]
            return None
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المقاييس الحالية: {str(e)}")
            return None

    def get_metrics_history(self, hours: int = 1) -> List[PerformanceMetric]:
        """الحصول على تاريخ المقاييس"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            return [m for m in self.metrics_history if m.timestamp > cutoff_time]
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على تاريخ المقاييس: {str(e)}")
            return []

    def get_operation_stats(self, operation_type: str) -> Dict[str, Any]:
        """الحصول على إحصائيات عملية معينة"""
        try:
            if operation_type not in self.operation_metrics:
                return {}

            metrics = self.operation_metrics[operation_type]
            if not metrics:
                return {}

            response_times = [m.response_time_ms for m in metrics if m.response_time_ms > 0]
            success_count = sum(1 for m in metrics if m.success)

            stats = {
                "total_operations": len(metrics),
                "successful_operations": success_count,
                "failed_operations": len(metrics) - success_count,
                "success_rate": (success_count / len(metrics)) * 100 if metrics else 0,
                "average_response_time": statistics.mean(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "median_response_time": statistics.median(response_times) if response_times else 0
            }

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات العملية: {str(e)}")
            return {}

    def get_active_alerts(self, severity: Optional[str] = None) -> List[SystemAlert]:
        """الحصول على التنبيهات النشطة"""
        try:
            active_alerts = [a for a in self.alerts if not a.resolved]

            if severity:
                active_alerts = [a for a in active_alerts if a.severity == severity]

            return sorted(active_alerts, key=lambda x: x.timestamp, reverse=True)

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التنبيهات النشطة: {str(e)}")
            return []

    def get_performance_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الأداء"""
        try:
            current_metric = self.get_current_metrics()
            recent_metrics = self.get_metrics_history(hours=1)

            summary = {
                "current_status": {
                    "cpu_percent": current_metric.cpu_percent if current_metric else 0,
                    "memory_percent": current_metric.memory_percent if current_metric else 0,
                    "memory_used_mb": current_metric.memory_used_mb if current_metric else 0,
                    "active_threads": current_metric.active_threads if current_metric else 0,
                    "open_files": current_metric.open_files if current_metric else 0
                },
                "hourly_averages": {},
                "performance_stats": self.performance_stats.copy(),
                "active_alerts_count": len(self.get_active_alerts()),
                "critical_alerts_count": len(self.get_active_alerts("CRITICAL")),
                "system_info": self.system_info,
                "monitoring_active": self.monitoring_active
            }

            # حساب المتوسطات للساعة الماضية
            if recent_metrics:
                summary["hourly_averages"] = {
                    "cpu_percent": statistics.mean([m.cpu_percent for m in recent_metrics]),
                    "memory_percent": statistics.mean([m.memory_percent for m in recent_metrics]),
                    "memory_used_mb": statistics.mean([m.memory_used_mb for m in recent_metrics]),
                    "response_time_ms": statistics.mean([m.response_time_ms for m in recent_metrics if m.response_time_ms > 0]) or 0
                }

            return summary

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على ملخص الأداء: {str(e)}")
            return {}

    def _save_all_data(self):
        """حفظ جميع البيانات"""
        try:
            # حفظ الإحصائيات
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                # تحويل datetime إلى string للحفظ
                stats_to_save = self.performance_stats.copy()
                for key, value in stats_to_save.items():
                    if isinstance(value, datetime):
                        stats_to_save[key] = value.isoformat()

                json.dump(stats_to_save, f, ensure_ascii=False, indent=2)

            # حفظ التنبيهات
            alerts_data = []
            for alert in self.alerts:
                alert_dict = asdict(alert)
                # تحويل datetime إلى string
                alert_dict["timestamp"] = alert.timestamp.isoformat()
                if alert.resolved_at:
                    alert_dict["resolved_at"] = alert.resolved_at.isoformat()
                alerts_data.append(alert_dict)

            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts_data, f, ensure_ascii=False, indent=2)

            # حفظ المقاييس الحديثة (آخر 100 مقياس)
            recent_metrics = list(self.metrics_history)[-100:]
            metrics_data = []
            for metric in recent_metrics:
                metric_dict = asdict(metric)
                metric_dict["timestamp"] = metric.timestamp.isoformat()
                metrics_data.append(metric_dict)

            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ البيانات: {str(e)}")

    def cleanup_old_data(self, days: int = None):
        """تنظيف البيانات القديمة"""
        try:
            if days is None:
                days = self.monitoring_settings.get("cleanup_old_data_days", 30)

            cutoff_time = datetime.now() - timedelta(days=days)

            # تنظيف التنبيهات القديمة
            self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]

            # تنظيف مقاييس العمليات القديمة
            for operation_type in self.operation_metrics:
                self.operation_metrics[operation_type] = [
                    m for m in self.operation_metrics[operation_type]
                    if m.timestamp > cutoff_time
                ]

            self.logger.info(f"تم تنظيف البيانات الأقدم من {days} يوم")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")

    def update_alert_thresholds(self, new_thresholds: Dict[str, float]):
        """تحديث عتبات التنبيه"""
        try:
            self.alert_thresholds.update(new_thresholds)

            # حفظ في الإعدادات
            settings = self.monitoring_settings.copy()
            settings["alert_thresholds"] = self.alert_thresholds
            self.config_manager.set_setting("monitoring", "performance", settings)

            self.logger.info("تم تحديث عتبات التنبيه")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث عتبات التنبيه: {str(e)}")

    def export_performance_report(self, file_path: str, hours: int = 24):
        """تصدير تقرير الأداء"""
        try:
            report_data = {
                "report_generated": datetime.now().isoformat(),
                "time_period_hours": hours,
                "performance_summary": self.get_performance_summary(),
                "metrics_history": [
                    asdict(m) for m in self.get_metrics_history(hours)
                ],
                "operation_stats": {
                    op_type: self.get_operation_stats(op_type)
                    for op_type in self.operation_metrics.keys()
                },
                "alerts": [
                    asdict(a) for a in self.alerts
                    if a.timestamp > datetime.now() - timedelta(hours=hours)
                ]
            }

            # تحويل datetime objects إلى strings
            def convert_datetime(obj):
                if isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                elif isinstance(obj, datetime):
                    return obj.isoformat()
                else:
                    return obj

            report_data = convert_datetime(report_data)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير تقرير الأداء إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير تقرير الأداء: {str(e)}")

    def __del__(self):
        """تنظيف الموارد"""
        try:
            self.stop_monitoring()
        except:
            pass
