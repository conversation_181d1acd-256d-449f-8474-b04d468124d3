#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالج الصوت - Audio Processor
يقوم بمعالجة وتحسين الصوت في الفيديوهات
"""

import logging
import os
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import librosa
import soundfile as sf
from scipy import signal
import moviepy.editor as mp

class AudioTrack:
    """مسار صوتي"""
    
    def __init__(self, file_path: str, track_type: str = "background"):
        self.file_path = file_path
        self.track_type = track_type  # background, voice, effect
        self.start_time = 0.0
        self.end_time = 0.0
        self.duration = 0.0
        self.volume = 1.0
        self.fade_in = 0.0
        self.fade_out = 0.0
        self.loop = False
        self.effects = []
        self.sample_rate = 44100
        self.channels = 2
        self.audio_data = None
        
        # تحميل معلومات الملف
        self._load_audio_info()
    
    def _load_audio_info(self):
        """تحميل معلومات الملف الصوتي"""
        try:
            if os.path.exists(self.file_path):
                # استخدام librosa لتحليل الملف
                self.audio_data, self.sample_rate = librosa.load(self.file_path, sr=None)
                self.duration = len(self.audio_data) / self.sample_rate
                self.channels = 1 if len(self.audio_data.shape) == 1 else self.audio_data.shape[0]
                
        except Exception as e:
            logging.error(f"خطأ في تحميل معلومات الصوت: {str(e)}")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "file_path": self.file_path,
            "track_type": self.track_type,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "volume": self.volume,
            "fade_in": self.fade_in,
            "fade_out": self.fade_out,
            "loop": self.loop,
            "sample_rate": self.sample_rate,
            "channels": self.channels
        }

class AudioProcessor:
    """معالج الصوت"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات معالجة الصوت
        self.settings = self._load_audio_settings()
        
        # إحصائيات
        self.stats = {
            "files_processed": 0,
            "noise_reductions": 0,
            "normalizations": 0,
            "effects_applied": 0
        }
    
    def _load_audio_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات معالجة الصوت"""
        try:
            return self.config_manager.get_setting("editing_settings", "audio_processor", {
                "auto_normalize": True,
                "auto_noise_reduction": True,
                "target_loudness": -23.0,  # LUFS
                "max_peak": -1.0,  # dB
                "noise_reduction_strength": 0.5,
                "compressor_threshold": -20.0,
                "compressor_ratio": 4.0,
                "eq_enabled": True,
                "eq_presets": {
                    "voice": {"low": -2, "mid": 2, "high": 1},
                    "music": {"low": 1, "mid": 0, "high": 2},
                    "podcast": {"low": -1, "mid": 3, "high": 0}
                },
                "reverb_enabled": False,
                "reverb_room_size": 0.3,
                "reverb_damping": 0.5,
                "sample_rate": 44100,
                "bit_depth": 16
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الصوت: {str(e)}")
            return {
                "auto_normalize": True,
                "auto_noise_reduction": True,
                "target_loudness": -23.0,
                "max_peak": -1.0,
                "noise_reduction_strength": 0.5,
                "compressor_threshold": -20.0,
                "compressor_ratio": 4.0,
                "eq_enabled": True,
                "eq_presets": {
                    "voice": {"low": -2, "mid": 2, "high": 1},
                    "music": {"low": 1, "mid": 0, "high": 2},
                    "podcast": {"low": -1, "mid": 3, "high": 0}
                },
                "reverb_enabled": False,
                "reverb_room_size": 0.3,
                "reverb_damping": 0.5,
                "sample_rate": 44100,
                "bit_depth": 16
            }
    
    def process_audio_track(self, track: AudioTrack, 
                           processing_options: Dict[str, Any] = None) -> AudioTrack:
        """معالجة مسار صوتي"""
        try:
            self.logger.info(f"بدء معالجة المسار الصوتي: {track.file_path}")
            
            if track.audio_data is None:
                self.logger.error("لا توجد بيانات صوتية للمعالجة")
                return track
            
            audio_data = track.audio_data.copy()
            
            # تطبيق المعالجات حسب الخيارات
            options = processing_options or {}
            
            # تقليل الضوضاء
            if options.get("noise_reduction", self.settings.get("auto_noise_reduction", True)):
                audio_data = self._reduce_noise(audio_data, track.sample_rate)
                self.stats["noise_reductions"] += 1
            
            # تطبيع الصوت
            if options.get("normalize", self.settings.get("auto_normalize", True)):
                audio_data = self._normalize_audio(audio_data)
                self.stats["normalizations"] += 1
            
            # ضغط الصوت
            if options.get("compress", False):
                audio_data = self._compress_audio(audio_data, track.sample_rate)
            
            # معادل الصوت
            if options.get("equalize", self.settings.get("eq_enabled", True)):
                eq_preset = options.get("eq_preset", "voice")
                audio_data = self._apply_equalizer(audio_data, track.sample_rate, eq_preset)
            
            # تطبيق المؤثرات
            for effect in track.effects:
                audio_data = self._apply_audio_effect(audio_data, track.sample_rate, effect)
                self.stats["effects_applied"] += 1
            
            # تحديث بيانات المسار
            track.audio_data = audio_data
            
            self.stats["files_processed"] += 1
            self.logger.info(f"انتهت معالجة المسار الصوتي")
            
            return track
            
        except Exception as e:
            self.logger.error(f"خطأ في معالجة المسار الصوتي: {str(e)}")
            return track
    
    def _reduce_noise(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """تقليل الضوضاء"""
        try:
            # تطبيق مرشح تمرير منخفض بسيط لتقليل الضوضاء عالية التردد
            nyquist = sample_rate // 2
            cutoff = min(8000, nyquist - 1)  # قطع الترددات فوق 8kHz
            
            # تصميم مرشح باترورث
            b, a = signal.butter(4, cutoff / nyquist, btype='low')
            
            # تطبيق المرشح
            filtered_audio = signal.filtfilt(b, a, audio_data)
            
            # تطبيق تقليل الضوضاء الطيفي البسيط
            strength = self.settings.get("noise_reduction_strength", 0.5)
            
            # حساب الطيف
            stft = librosa.stft(filtered_audio)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # تقدير الضوضاء من الإطارات الأولى
            noise_profile = np.mean(magnitude[:, :10], axis=1, keepdims=True)
            
            # تطبيق تقليل الضوضاء
            noise_reduced_magnitude = magnitude - (noise_profile * strength)
            noise_reduced_magnitude = np.maximum(noise_reduced_magnitude, magnitude * 0.1)
            
            # إعادة بناء الإشارة
            noise_reduced_stft = noise_reduced_magnitude * np.exp(1j * phase)
            result = librosa.istft(noise_reduced_stft)
            
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تقليل الضوضاء: {str(e)}")
            return audio_data
    
    def _normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """تطبيع الصوت"""
        try:
            # تطبيع القمة
            max_peak = self.settings.get("max_peak", -1.0)
            peak_db = 20 * np.log10(np.max(np.abs(audio_data)) + 1e-10)
            
            if peak_db > max_peak:
                gain_db = max_peak - peak_db
                gain_linear = 10 ** (gain_db / 20)
                audio_data = audio_data * gain_linear
            
            # تطبيع RMS بسيط
            rms = np.sqrt(np.mean(audio_data ** 2))
            target_rms = 0.1  # قيمة مستهدفة
            
            if rms > 0:
                gain = target_rms / rms
                audio_data = audio_data * min(gain, 2.0)  # حد أقصى للتضخيم
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيع الصوت: {str(e)}")
            return audio_data
    
    def _compress_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """ضغط الصوت (Compressor)"""
        try:
            threshold_db = self.settings.get("compressor_threshold", -20.0)
            ratio = self.settings.get("compressor_ratio", 4.0)
            
            # تحويل العتبة إلى قيمة خطية
            threshold_linear = 10 ** (threshold_db / 20)
            
            # تطبيق الضغط
            compressed_audio = audio_data.copy()
            
            # العثور على النقاط التي تتجاوز العتبة
            above_threshold = np.abs(audio_data) > threshold_linear
            
            # تطبيق الضغط على النقاط التي تتجاوز العتبة
            excess = np.abs(audio_data[above_threshold]) - threshold_linear
            compressed_excess = excess / ratio
            
            # إعادة بناء الإشارة
            compressed_audio[above_threshold] = (
                np.sign(audio_data[above_threshold]) * 
                (threshold_linear + compressed_excess)
            )
            
            return compressed_audio
            
        except Exception as e:
            self.logger.error(f"خطأ في ضغط الصوت: {str(e)}")
            return audio_data
    
    def _apply_equalizer(self, audio_data: np.ndarray, sample_rate: int, 
                        preset: str = "voice") -> np.ndarray:
        """تطبيق معادل الصوت"""
        try:
            eq_settings = self.settings.get("eq_presets", {}).get(preset, {})
            
            if not eq_settings:
                return audio_data
            
            # تطبيق معادل ثلاثي النطاقات بسيط
            low_gain = eq_settings.get("low", 0)
            mid_gain = eq_settings.get("mid", 0)
            high_gain = eq_settings.get("high", 0)
            
            # تصميم مرشحات للنطاقات المختلفة
            nyquist = sample_rate // 2
            
            # نطاق منخفض (20-250 Hz)
            if low_gain != 0:
                low_freq = min(250, nyquist - 1)
                b_low, a_low = signal.butter(2, low_freq / nyquist, btype='low')
                low_band = signal.filtfilt(b_low, a_low, audio_data)
                audio_data += low_band * (10 ** (low_gain / 20) - 1)
            
            # نطاق متوسط (250-4000 Hz)
            if mid_gain != 0:
                mid_low = min(250, nyquist - 1)
                mid_high = min(4000, nyquist - 1)
                b_mid, a_mid = signal.butter(2, [mid_low / nyquist, mid_high / nyquist], btype='band')
                mid_band = signal.filtfilt(b_mid, a_mid, audio_data)
                audio_data += mid_band * (10 ** (mid_gain / 20) - 1)
            
            # نطاق عالي (4000+ Hz)
            if high_gain != 0:
                high_freq = min(4000, nyquist - 1)
                b_high, a_high = signal.butter(2, high_freq / nyquist, btype='high')
                high_band = signal.filtfilt(b_high, a_high, audio_data)
                audio_data += high_band * (10 ** (high_gain / 20) - 1)
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق معادل الصوت: {str(e)}")
            return audio_data
    
    def _apply_audio_effect(self, audio_data: np.ndarray, sample_rate: int, 
                           effect: Dict[str, Any]) -> np.ndarray:
        """تطبيق مؤثر صوتي"""
        try:
            effect_type = effect.get("type", "")
            
            if effect_type == "reverb":
                return self._apply_reverb(audio_data, sample_rate, effect)
            elif effect_type == "echo":
                return self._apply_echo(audio_data, sample_rate, effect)
            elif effect_type == "pitch_shift":
                return self._apply_pitch_shift(audio_data, sample_rate, effect)
            elif effect_type == "speed_change":
                return self._apply_speed_change(audio_data, sample_rate, effect)
            else:
                self.logger.warning(f"مؤثر غير مدعوم: {effect_type}")
                return audio_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق المؤثر {effect.get('type', 'unknown')}: {str(e)}")
            return audio_data
    
    def _apply_reverb(self, audio_data: np.ndarray, sample_rate: int, 
                     effect: Dict[str, Any]) -> np.ndarray:
        """تطبيق صدى (Reverb)"""
        try:
            room_size = effect.get("room_size", self.settings.get("reverb_room_size", 0.3))
            damping = effect.get("damping", self.settings.get("reverb_damping", 0.5))
            
            # إنشاء استجابة نبضية بسيطة للصدى
            reverb_length = int(sample_rate * room_size)
            impulse_response = np.random.normal(0, 0.1, reverb_length)
            
            # تطبيق التخميد
            decay = np.exp(-np.arange(reverb_length) * damping / sample_rate)
            impulse_response *= decay
            
            # تطبيق الالتفاف
            reverb_audio = np.convolve(audio_data, impulse_response, mode='same')
            
            # مزج مع الصوت الأصلي
            wet_level = effect.get("wet_level", 0.3)
            result = audio_data * (1 - wet_level) + reverb_audio * wet_level
            
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق الصدى: {str(e)}")
            return audio_data
    
    def _apply_echo(self, audio_data: np.ndarray, sample_rate: int, 
                   effect: Dict[str, Any]) -> np.ndarray:
        """تطبيق صدى بسيط (Echo)"""
        try:
            delay_ms = effect.get("delay_ms", 500)
            feedback = effect.get("feedback", 0.3)
            mix = effect.get("mix", 0.5)
            
            # حساب التأخير بالعينات
            delay_samples = int(delay_ms * sample_rate / 1000)
            
            # إنشاء إشارة مؤخرة
            delayed_audio = np.zeros_like(audio_data)
            if delay_samples < len(audio_data):
                delayed_audio[delay_samples:] = audio_data[:-delay_samples] * feedback
            
            # مزج مع الصوت الأصلي
            result = audio_data * (1 - mix) + delayed_audio * mix
            
            return result
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق الصدى: {str(e)}")
            return audio_data
    
    def _apply_pitch_shift(self, audio_data: np.ndarray, sample_rate: int, 
                          effect: Dict[str, Any]) -> np.ndarray:
        """تغيير طبقة الصوت"""
        try:
            semitones = effect.get("semitones", 0)
            
            if semitones == 0:
                return audio_data
            
            # استخدام librosa لتغيير الطبقة
            shifted_audio = librosa.effects.pitch_shift(
                audio_data, sr=sample_rate, n_steps=semitones
            )
            
            return shifted_audio
            
        except Exception as e:
            self.logger.error(f"خطأ في تغيير طبقة الصوت: {str(e)}")
            return audio_data
    
    def _apply_speed_change(self, audio_data: np.ndarray, sample_rate: int, 
                           effect: Dict[str, Any]) -> np.ndarray:
        """تغيير سرعة الصوت"""
        try:
            speed_factor = effect.get("speed_factor", 1.0)
            
            if speed_factor == 1.0:
                return audio_data
            
            # استخدام librosa لتغيير السرعة
            stretched_audio = librosa.effects.time_stretch(audio_data, rate=speed_factor)
            
            return stretched_audio
            
        except Exception as e:
            self.logger.error(f"خطأ في تغيير سرعة الصوت: {str(e)}")
            return audio_data
    
    def save_processed_audio(self, track: AudioTrack, output_path: str) -> str:
        """حفظ الصوت المعالج"""
        try:
            if track.audio_data is None:
                raise ValueError("لا توجد بيانات صوتية للحفظ")
            
            # حفظ الملف
            sf.write(
                output_path, 
                track.audio_data, 
                track.sample_rate,
                subtype='PCM_16'
            )
            
            self.logger.info(f"تم حفظ الصوت المعالج: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الصوت: {str(e)}")
            raise
    
    def get_audio_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات معالجة الصوت"""
        return {
            "files_processed": self.stats["files_processed"],
            "noise_reductions": self.stats["noise_reductions"],
            "normalizations": self.stats["normalizations"],
            "effects_applied": self.stats["effects_applied"],
            "available_eq_presets": list(self.settings.get("eq_presets", {}).keys())
        }
