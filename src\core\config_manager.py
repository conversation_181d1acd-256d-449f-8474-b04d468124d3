#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الإعدادات - Configuration Manager
يدير جميع إعدادات التطبيق والحسابات المرتبطة
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
import keyring

class ConfigManager:
    """مدير الإعدادات والتكوين"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config_dir = Path.home() / ".smart_content_app"
        self.config_file = self.config_dir / "config.json"
        self.accounts_file = self.config_dir / "accounts.json"
        
        # إنشاء مجلد الإعدادات إذا لم يكن موجوداً
        self.config_dir.mkdir(exist_ok=True)
        
        # تحميل الإعدادات
        self.config = self._load_config()
        self.accounts = self._load_accounts()
        
        # إعداد التشفير
        self._setup_encryption()
    
    def _setup_encryption(self):
        """إعداد نظام التشفير"""
        try:
            # محاولة الحصول على مفتاح التشفير من keyring
            key = keyring.get_password("smart_content_app", "encryption_key")
            
            if not key:
                # إنشاء مفتاح جديد
                key = Fernet.generate_key().decode()
                keyring.set_password("smart_content_app", "encryption_key", key)
                self.logger.info("تم إنشاء مفتاح تشفير جديد")
            
            self.cipher = Fernet(key.encode())
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التشفير: {str(e)}")
            # استخدام مفتاح افتراضي (غير آمن للإنتاج)
            self.cipher = Fernet(Fernet.generate_key())
    
    def _load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        default_config = {
            "app_settings": {
                "language": "ar",
                "theme": "dark",
                "auto_start": False,
                "minimize_to_tray": True
            },
            "content_settings": {
                "content_types": ["stories", "lives"],
                "quality": "1080p",
                "max_duration": 60,
                "keywords": ["مضحك", "ترند", "فيرال"]
            },
            "editing_settings": {
                "template": "default",
                "add_music": True,
                "add_captions": True,
                "add_effects": True,
                "output_format": "mp4"
            },
            "publishing_settings": {
                "auto_publish": True,
                "best_time": "20:00",
                "hashtags": ["#ترند", "#مضحك", "#فيرال"],
                "description_template": "محتوى رائع! 🔥"
            }
        }
        
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج الإعدادات الافتراضية مع المحفوظة
                    return {**default_config, **config}
            else:
                # حفظ الإعدادات الافتراضية
                self._save_config(default_config)
                return default_config
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {str(e)}")
            return default_config
    
    def _load_accounts(self) -> Dict[str, Any]:
        """تحميل بيانات الحسابات المشفرة"""
        default_accounts = {
            "snapchat": {"username": "", "token": "", "active": False},
            "tiktok": {"username": "", "token": "", "active": False},
            "kick": {"username": "", "token": "", "active": False}
        }
        
        try:
            if self.accounts_file.exists():
                with open(self.accounts_file, 'rb') as f:
                    encrypted_data = f.read()
                    decrypted_data = self.cipher.decrypt(encrypted_data)
                    accounts = json.loads(decrypted_data.decode())
                    return {**default_accounts, **accounts}
            else:
                return default_accounts
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات الحسابات: {str(e)}")
            return default_accounts
    
    def _save_config(self, config: Dict[str, Any]):
        """حفظ الإعدادات إلى الملف"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            self.logger.info("تم حفظ الإعدادات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def _save_accounts(self, accounts: Dict[str, Any]):
        """حفظ بيانات الحسابات مشفرة"""
        try:
            data = json.dumps(accounts, ensure_ascii=False).encode()
            encrypted_data = self.cipher.encrypt(data)
            
            with open(self.accounts_file, 'wb') as f:
                f.write(encrypted_data)
            self.logger.info("تم حفظ بيانات الحسابات بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ بيانات الحسابات: {str(e)}")
    
    def get_setting(self, category: str, key: str, default=None):
        """الحصول على إعداد معين"""
        return self.config.get(category, {}).get(key, default)
    
    def set_setting(self, category: str, key: str, value: Any):
        """تعديل إعداد معين"""
        if category not in self.config:
            self.config[category] = {}
        
        self.config[category][key] = value
        self._save_config(self.config)
    
    def get_account(self, platform: str) -> Dict[str, Any]:
        """الحصول على بيانات حساب معين"""
        return self.accounts.get(platform, {})
    
    def set_account(self, platform: str, username: str, token: str, active: bool = True):
        """تعديل بيانات حساب معين"""
        self.accounts[platform] = {
            "username": username,
            "token": token,
            "active": active
        }
        self._save_accounts(self.accounts)
    
    def is_account_active(self, platform: str) -> bool:
        """التحقق من حالة الحساب"""
        account = self.get_account(platform)
        return account.get("active", False) and bool(account.get("token"))
    
    def get_all_settings(self) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        return self.config.copy()
    
    def get_all_accounts(self) -> Dict[str, Any]:
        """الحصول على جميع الحسابات (بدون كلمات المرور)"""
        safe_accounts = {}
        for platform, data in self.accounts.items():
            safe_accounts[platform] = {
                "username": data.get("username", ""),
                "active": data.get("active", False),
                "has_token": bool(data.get("token"))
            }
        return safe_accounts
