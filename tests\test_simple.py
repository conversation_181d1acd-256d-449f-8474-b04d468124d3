#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتحقق من عمل نظام الاختبارات
Simple test to verify testing system works
"""

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    assert 1 + 1 == 2
    assert "hello" == "hello"
    assert True is True

def test_string_operations():
    """اختبار عمليات النصوص"""
    text = "مرحبا بالعالم"
    assert len(text) > 0
    assert "مرحبا" in text
    assert text.startswith("مرحبا")

def test_list_operations():
    """اختبار عمليات القوائم"""
    numbers = [1, 2, 3, 4, 5]
    assert len(numbers) == 5
    assert 3 in numbers
    assert max(numbers) == 5
    assert min(numbers) == 1

def test_dictionary_operations():
    """اختبار عمليات القواميس"""
    data = {
        "name": "تطبيق المحتوى",
        "version": "1.0.0",
        "language": "Arabic"
    }
    
    assert "name" in data
    assert data["version"] == "1.0.0"
    assert len(data) == 3

if __name__ == "__main__":
    # تشغيل الاختبارات يدوياً
    print("🧪 تشغيل الاختبارات البسيطة...")
    
    try:
        test_basic_functionality()
        print("✅ اختبار الوظائف الأساسية - نجح")
    except Exception as e:
        print(f"❌ اختبار الوظائف الأساسية - فشل: {e}")
    
    try:
        test_string_operations()
        print("✅ اختبار عمليات النصوص - نجح")
    except Exception as e:
        print(f"❌ اختبار عمليات النصوص - فشل: {e}")
    
    try:
        test_list_operations()
        print("✅ اختبار عمليات القوائم - نجح")
    except Exception as e:
        print(f"❌ اختبار عمليات القوائم - فشل: {e}")
    
    try:
        test_dictionary_operations()
        print("✅ اختبار عمليات القواميس - نجح")
    except Exception as e:
        print(f"❌ اختبار عمليات القواميس - فشل: {e}")
    
    print("\n🎉 انتهت الاختبارات البسيطة!")
