#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحماية والأمان - Security Manager
يدير حماية البيانات والتشفير والأمان العام للتطبيق
"""

import os
import hashlib
import hmac
import secrets
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import psutil
import time

class SecurityManager:
    """مدير الأمان والحماية"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.app_dir = Path.home() / ".smart_content_app"
        self.security_file = self.app_dir / "security.dat"
        
        # إعداد الحماية
        self._setup_security()
        
        # مراقبة النشاط المشبوه
        self.suspicious_activity_count = 0
        self.last_activity_check = time.time()
    
    def _setup_security(self):
        """إعداد نظام الحماية"""
        try:
            # إنشاء مفتاح الجلسة
            self.session_key = self._generate_session_key()
            
            # التحقق من سلامة الملفات
            self._verify_file_integrity()
            
            # إعداد مراقبة العمليات
            self._setup_process_monitoring()
            
            self.logger.info("تم إعداد نظام الحماية بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد نظام الحماية: {str(e)}")
    
    def _generate_session_key(self) -> str:
        """إنشاء مفتاح جلسة فريد"""
        return secrets.token_hex(32)
    
    def _verify_file_integrity(self):
        """التحقق من سلامة ملفات التطبيق"""
        try:
            # قائمة الملفات المهمة للتحقق منها
            critical_files = [
                "main.py",
                "src/core/config_manager.py",
                "src/core/security.py"
            ]
            
            integrity_data = {}
            
            for file_path in critical_files:
                full_path = Path(file_path)
                if full_path.exists():
                    with open(full_path, 'rb') as f:
                        file_hash = hashlib.sha256(f.read()).hexdigest()
                        integrity_data[str(file_path)] = file_hash
            
            # حفظ بيانات السلامة
            self._save_integrity_data(integrity_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من سلامة الملفات: {str(e)}")
    
    def _save_integrity_data(self, data: Dict[str, str]):
        """حفظ بيانات سلامة الملفات"""
        try:
            import json
            with open(self.security_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ بيانات السلامة: {str(e)}")
    
    def _setup_process_monitoring(self):
        """إعداد مراقبة العمليات"""
        try:
            # الحصول على معرف العملية الحالية
            self.current_pid = os.getpid()
            
            # مراقبة استخدام الذاكرة والمعالج
            self.process = psutil.Process(self.current_pid)
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد مراقبة العمليات: {str(e)}")
    
    def encrypt_sensitive_data(self, data: str, password: str) -> str:
        """تشفير البيانات الحساسة"""
        try:
            # إنشاء مفتاح من كلمة المرور
            password_bytes = password.encode()
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            
            # تشفير البيانات
            cipher = Fernet(key)
            encrypted_data = cipher.encrypt(data.encode())
            
            # دمج الملح مع البيانات المشفرة
            return base64.urlsafe_b64encode(salt + encrypted_data).decode()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشفير البيانات: {str(e)}")
            return ""
    
    def decrypt_sensitive_data(self, encrypted_data: str, password: str) -> str:
        """فك تشفير البيانات الحساسة"""
        try:
            # فصل الملح عن البيانات
            combined_data = base64.urlsafe_b64decode(encrypted_data.encode())
            salt = combined_data[:16]
            encrypted_content = combined_data[16:]
            
            # إعادة إنشاء المفتاح
            password_bytes = password.encode()
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            
            # فك التشفير
            cipher = Fernet(key)
            decrypted_data = cipher.decrypt(encrypted_content)
            
            return decrypted_data.decode()
            
        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير البيانات: {str(e)}")
            return ""
    
    def validate_token(self, token: str, platform: str) -> bool:
        """التحقق من صحة الرمز المميز"""
        try:
            # التحقق من طول الرمز
            if len(token) < 10:
                return False
            
            # التحقق من تنسيق الرمز حسب المنصة
            if platform == "tiktok":
                return token.startswith("tt_") or len(token) > 20
            elif platform == "snapchat":
                return len(token) > 15
            elif platform == "kick":
                return len(token) > 10
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الرمز: {str(e)}")
            return False
    
    def check_suspicious_activity(self) -> bool:
        """فحص النشاط المشبوه"""
        try:
            current_time = time.time()
            
            # فحص استخدام الذاكرة
            memory_percent = self.process.memory_percent()
            if memory_percent > 80:  # أكثر من 80% من الذاكرة
                self.suspicious_activity_count += 1
                self.logger.warning(f"استخدام ذاكرة عالي: {memory_percent:.2f}%")
            
            # فحص استخدام المعالج
            cpu_percent = self.process.cpu_percent()
            if cpu_percent > 90:  # أكثر من 90% من المعالج
                self.suspicious_activity_count += 1
                self.logger.warning(f"استخدام معالج عالي: {cpu_percent:.2f}%")
            
            # فحص عدد الملفات المفتوحة
            try:
                open_files = len(self.process.open_files())
                if open_files > 100:  # أكثر من 100 ملف مفتوح
                    self.suspicious_activity_count += 1
                    self.logger.warning(f"عدد ملفات مفتوحة كثير: {open_files}")
            except:
                pass
            
            # إعادة تعيين العداد كل ساعة
            if current_time - self.last_activity_check > 3600:
                self.suspicious_activity_count = 0
                self.last_activity_check = current_time
            
            # إذا تجاوز النشاط المشبوه الحد المسموح
            if self.suspicious_activity_count > 5:
                self.logger.critical("تم اكتشاف نشاط مشبوه! سيتم إغلاق التطبيق")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص النشاط المشبوه: {str(e)}")
            return False
    
    def secure_delete_file(self, file_path: Path):
        """حذف آمن للملفات"""
        try:
            if file_path.exists():
                # الكتابة فوق الملف ببيانات عشوائية
                file_size = file_path.stat().st_size
                with open(file_path, 'wb') as f:
                    f.write(os.urandom(file_size))
                
                # حذف الملف
                file_path.unlink()
                self.logger.info(f"تم حذف الملف بأمان: {file_path}")
                
        except Exception as e:
            self.logger.error(f"خطأ في الحذف الآمن: {str(e)}")
    
    def generate_secure_filename(self, original_name: str) -> str:
        """إنشاء اسم ملف آمن"""
        try:
            # إزالة الأحرف الخطيرة
            safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_."
            safe_name = ''.join(c for c in original_name if c in safe_chars)
            
            # إضافة رقم عشوائي للتفرد
            random_suffix = secrets.token_hex(4)
            
            # الحصول على امتداد الملف
            file_ext = Path(original_name).suffix
            base_name = Path(safe_name).stem
            
            return f"{base_name}_{random_suffix}{file_ext}"
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء اسم ملف آمن: {str(e)}")
            return f"secure_file_{secrets.token_hex(8)}.tmp"
    
    def is_safe_to_continue(self) -> bool:
        """التحقق من أمان الاستمرار"""
        try:
            # فحص النشاط المشبوه
            if self.check_suspicious_activity():
                return False
            
            # فحص سلامة الملفات المهمة
            # (يمكن إضافة المزيد من الفحوصات هنا)
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص الأمان: {str(e)}")
            return False
