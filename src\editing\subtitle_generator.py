#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الترجمة - Subtitle Generator
يقوم بإنشاء وتنسيق الترجمة للفيديوهات
"""

import logging
import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import timedelta
import moviepy.editor as mp

class Subtitle:
    """ترجمة واحدة"""
    
    def __init__(self, text: str, start_time: float, end_time: float):
        self.text = text
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        
        # إعدادات التنسيق
        self.style = {
            "font_size": 24,
            "font_color": "white",
            "background_color": "black",
            "position": "bottom",
            "alignment": "center",
            "font_family": "Arial",
            "bold": False,
            "italic": False,
            "outline": True,
            "outline_color": "black",
            "outline_width": 2
        }
        
        # إعدادات الحركة
        self.animation = {
            "type": "none",  # none, fade, slide, typewriter
            "duration": 0.3,
            "direction": "up"
        }
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "style": self.style,
            "animation": self.animation
        }
    
    def to_srt_format(self, index: int) -> str:
        """تحويل إلى تنسيق SRT"""
        start_time = self._seconds_to_srt_time(self.start_time)
        end_time = self._seconds_to_srt_time(self.end_time)
        
        return f"{index}\n{start_time} --> {end_time}\n{self.text}\n\n"
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """تحويل الثواني إلى تنسيق وقت SRT"""
        td = timedelta(seconds=seconds)
        hours, remainder = divmod(td.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{milliseconds:03d}"

class SubtitleGenerator:
    """مولد الترجمة"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات الترجمة
        self.settings = self._load_subtitle_settings()
        
        # قواعد تقسيم النص
        self.text_rules = self._load_text_rules()
        
        # إحصائيات
        self.stats = {
            "subtitles_generated": 0,
            "total_duration": 0.0,
            "average_subtitle_length": 0.0
        }
    
    def _load_subtitle_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات الترجمة"""
        try:
            return self.config_manager.get_setting("editing_settings", "subtitle_generator", {
                "max_chars_per_line": 40,
                "max_lines_per_subtitle": 2,
                "min_subtitle_duration": 1.0,
                "max_subtitle_duration": 6.0,
                "reading_speed_wpm": 200,  # كلمة في الدقيقة
                "gap_between_subtitles": 0.1,
                "auto_split_long_sentences": True,
                "preserve_sentence_structure": True,
                "default_style": {
                    "font_size": 24,
                    "font_color": "white",
                    "background_color": "black",
                    "position": "bottom",
                    "alignment": "center",
                    "font_family": "Arial",
                    "bold": False,
                    "italic": False,
                    "outline": True,
                    "outline_color": "black",
                    "outline_width": 2
                },
                "animation_settings": {
                    "type": "fade",
                    "duration": 0.3,
                    "direction": "up"
                }
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الترجمة: {str(e)}")
            return {
                "max_chars_per_line": 40,
                "max_lines_per_subtitle": 2,
                "min_subtitle_duration": 1.0,
                "max_subtitle_duration": 6.0,
                "reading_speed_wpm": 200,
                "gap_between_subtitles": 0.1,
                "auto_split_long_sentences": True,
                "preserve_sentence_structure": True
            }
    
    def _load_text_rules(self) -> Dict[str, Any]:
        """تحميل قواعد تقسيم النص العربي"""
        return {
            "sentence_endings": [".", "!", "?", "؟", ".", "!", "،"],
            "pause_indicators": [",", "،", "؛", ";", ":", ":", "-", "–", "—"],
            "word_separators": [" ", "\t", "\n"],
            "arabic_punctuation": ["،", "؛", "؟", "!", ".", ":", "«", "»", """, """],
            "common_conjunctions": ["و", "أو", "لكن", "إذا", "عندما", "بينما", "حيث", "كما"],
            "break_words": ["نعم", "لا", "حسناً", "طيب", "أوكي", "آه", "أم", "إذن"]
        }
    
    def generate_subtitles_from_transcription(self, transcription_data: Dict[str, Any]) -> List[Subtitle]:
        """إنشاء ترجمة من بيانات النسخ النصي"""
        try:
            self.logger.info("بدء إنشاء الترجمة من النسخ النصي")
            
            subtitles = []
            segments = transcription_data.get("segments", [])
            
            for segment in segments:
                text = segment.get("text", "").strip()
                start_time = segment.get("start", 0.0)
                end_time = segment.get("end", start_time + 3.0)
                
                if not text:
                    continue
                
                # تقسيم النص الطويل إلى ترجمات متعددة
                segment_subtitles = self._split_text_to_subtitles(text, start_time, end_time)
                subtitles.extend(segment_subtitles)
            
            # تحسين التوقيتات
            subtitles = self._optimize_subtitle_timing(subtitles)
            
            # تطبيق الأنماط الافتراضية
            for subtitle in subtitles:
                subtitle.style.update(self.settings.get("default_style", {}))
                subtitle.animation.update(self.settings.get("animation_settings", {}))
            
            self.stats["subtitles_generated"] += len(subtitles)
            self.stats["total_duration"] += sum(sub.duration for sub in subtitles)
            
            if subtitles:
                avg_length = sum(len(sub.text) for sub in subtitles) / len(subtitles)
                self.stats["average_subtitle_length"] = avg_length
            
            self.logger.info(f"تم إنشاء {len(subtitles)} ترجمة")
            return subtitles
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الترجمة: {str(e)}")
            return []
    
    def _split_text_to_subtitles(self, text: str, start_time: float, end_time: float) -> List[Subtitle]:
        """تقسيم النص إلى ترجمات متعددة"""
        try:
            max_chars = self.settings.get("max_chars_per_line", 40)
            max_lines = self.settings.get("max_lines_per_subtitle", 2)
            max_chars_per_subtitle = max_chars * max_lines
            
            # إذا كان النص قصيراً، إنشاء ترجمة واحدة
            if len(text) <= max_chars_per_subtitle:
                subtitle = Subtitle(text, start_time, end_time)
                return [subtitle]
            
            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(text)
            
            subtitles = []
            current_text = ""
            segment_duration = end_time - start_time
            
            for i, sentence in enumerate(sentences):
                # التحقق من إمكانية إضافة الجملة للترجمة الحالية
                potential_text = current_text + " " + sentence if current_text else sentence
                
                if len(potential_text) <= max_chars_per_subtitle:
                    current_text = potential_text
                else:
                    # إنشاء ترجمة من النص الحالي
                    if current_text:
                        subtitle_start = start_time + (len(subtitles) / len(sentences)) * segment_duration
                        subtitle_end = start_time + ((len(subtitles) + 1) / len(sentences)) * segment_duration
                        
                        subtitle = Subtitle(current_text.strip(), subtitle_start, subtitle_end)
                        subtitles.append(subtitle)
                    
                    # بدء ترجمة جديدة
                    current_text = sentence
            
            # إضافة الترجمة الأخيرة
            if current_text:
                subtitle_start = start_time + (len(subtitles) / max(1, len(subtitles) + 1)) * segment_duration
                subtitle_end = end_time
                
                subtitle = Subtitle(current_text.strip(), subtitle_start, subtitle_end)
                subtitles.append(subtitle)
            
            return subtitles
            
        except Exception as e:
            self.logger.error(f"خطأ في تقسيم النص: {str(e)}")
            return [Subtitle(text, start_time, end_time)]
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """تقسيم النص إلى جمل"""
        try:
            # تنظيف النص
            text = re.sub(r'\s+', ' ', text.strip())
            
            # تقسيم بناءً على علامات الترقيم
            sentence_endings = self.text_rules["sentence_endings"]
            pattern = '|'.join(re.escape(ending) for ending in sentence_endings)
            
            sentences = re.split(f'({pattern})', text)
            
            # إعادة تجميع الجمل مع علامات الترقيم
            result = []
            current_sentence = ""
            
            for part in sentences:
                if part.strip():
                    current_sentence += part
                    if part in sentence_endings:
                        result.append(current_sentence.strip())
                        current_sentence = ""
            
            # إضافة الجملة الأخيرة إذا لم تنته بعلامة ترقيم
            if current_sentence.strip():
                result.append(current_sentence.strip())
            
            return [s for s in result if s.strip()]
            
        except Exception as e:
            self.logger.error(f"خطأ في تقسيم الجمل: {str(e)}")
            return [text]
    
    def _optimize_subtitle_timing(self, subtitles: List[Subtitle]) -> List[Subtitle]:
        """تحسين توقيتات الترجمة"""
        try:
            if not subtitles:
                return subtitles
            
            min_duration = self.settings.get("min_subtitle_duration", 1.0)
            max_duration = self.settings.get("max_subtitle_duration", 6.0)
            gap = self.settings.get("gap_between_subtitles", 0.1)
            reading_speed = self.settings.get("reading_speed_wpm", 200)
            
            optimized = []
            
            for i, subtitle in enumerate(subtitles):
                # حساب المدة المثلى بناءً على سرعة القراءة
                word_count = len(subtitle.text.split())
                optimal_duration = max(min_duration, (word_count / reading_speed) * 60)
                optimal_duration = min(optimal_duration, max_duration)
                
                # تعديل التوقيتات
                if subtitle.duration < min_duration:
                    # زيادة المدة
                    center_time = (subtitle.start_time + subtitle.end_time) / 2
                    subtitle.start_time = center_time - optimal_duration / 2
                    subtitle.end_time = center_time + optimal_duration / 2
                elif subtitle.duration > max_duration:
                    # تقليل المدة
                    subtitle.end_time = subtitle.start_time + optimal_duration
                
                # التأكد من عدم التداخل مع الترجمة التالية
                if i < len(subtitles) - 1:
                    next_subtitle = subtitles[i + 1]
                    if subtitle.end_time + gap > next_subtitle.start_time:
                        subtitle.end_time = next_subtitle.start_time - gap
                
                # التأكد من عدم التداخل مع الترجمة السابقة
                if i > 0:
                    prev_subtitle = optimized[-1]
                    if subtitle.start_time < prev_subtitle.end_time + gap:
                        subtitle.start_time = prev_subtitle.end_time + gap
                
                # التأكد من أن المدة لا تزال صالحة
                if subtitle.end_time <= subtitle.start_time:
                    subtitle.end_time = subtitle.start_time + min_duration
                
                subtitle.duration = subtitle.end_time - subtitle.start_time
                optimized.append(subtitle)
            
            return optimized
            
        except Exception as e:
            self.logger.error(f"خطأ في تحسين التوقيتات: {str(e)}")
            return subtitles
    
    def apply_style_template(self, subtitles: List[Subtitle], style_name: str) -> List[Subtitle]:
        """تطبيق قالب تنسيق على الترجمة"""
        try:
            style_templates = {
                "default": {
                    "font_size": 24,
                    "font_color": "white",
                    "background_color": "black",
                    "position": "bottom"
                },
                "modern": {
                    "font_size": 28,
                    "font_color": "#FFFFFF",
                    "background_color": "transparent",
                    "position": "bottom",
                    "outline": True,
                    "outline_color": "#000000",
                    "outline_width": 3
                },
                "colorful": {
                    "font_size": 26,
                    "font_color": "#FFD700",
                    "background_color": "rgba(0,0,0,0.7)",
                    "position": "bottom",
                    "bold": True
                },
                "minimal": {
                    "font_size": 22,
                    "font_color": "#F0F0F0",
                    "background_color": "transparent",
                    "position": "bottom",
                    "outline": False
                }
            }
            
            style = style_templates.get(style_name, style_templates["default"])
            
            for subtitle in subtitles:
                subtitle.style.update(style)
            
            self.logger.info(f"تم تطبيق قالب التنسيق: {style_name}")
            return subtitles
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيق قالب التنسيق: {str(e)}")
            return subtitles
    
    def export_to_srt(self, subtitles: List[Subtitle], output_path: str) -> bool:
        """تصدير الترجمة إلى ملف SRT"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, subtitle in enumerate(subtitles, 1):
                    f.write(subtitle.to_srt_format(i))
            
            self.logger.info(f"تم تصدير الترجمة إلى: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير الترجمة: {str(e)}")
            return False
    
    def create_video_subtitles(self, subtitles: List[Subtitle], video_size: Tuple[int, int]) -> List[mp.TextClip]:
        """إنشاء مقاطع نصية للفيديو"""
        try:
            text_clips = []
            
            for subtitle in subtitles:
                # إنشاء مقطع نصي
                txt_clip = mp.TextClip(
                    subtitle.text,
                    fontsize=subtitle.style.get("font_size", 24),
                    color=subtitle.style.get("font_color", "white"),
                    font=subtitle.style.get("font_family", "Arial"),
                    stroke_color=subtitle.style.get("outline_color", "black") if subtitle.style.get("outline", True) else None,
                    stroke_width=subtitle.style.get("outline_width", 2) if subtitle.style.get("outline", True) else 0
                ).set_start(subtitle.start_time).set_end(subtitle.end_time)
                
                # تحديد الموضع
                position = subtitle.style.get("position", "bottom")
                if position == "bottom":
                    txt_clip = txt_clip.set_position(("center", video_size[1] - 100))
                elif position == "top":
                    txt_clip = txt_clip.set_position(("center", 50))
                else:
                    txt_clip = txt_clip.set_position("center")
                
                # تطبيق الحركة
                animation_type = subtitle.animation.get("type", "none")
                if animation_type == "fade":
                    fade_duration = subtitle.animation.get("duration", 0.3)
                    txt_clip = txt_clip.fadein(fade_duration).fadeout(fade_duration)
                
                text_clips.append(txt_clip)
            
            self.logger.info(f"تم إنشاء {len(text_clips)} مقطع نصي")
            return text_clips
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المقاطع النصية: {str(e)}")
            return []
    
    def get_subtitle_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الترجمة"""
        return {
            "subtitles_generated": self.stats["subtitles_generated"],
            "total_duration": self.stats["total_duration"],
            "average_subtitle_length": self.stats["average_subtitle_length"],
            "available_styles": ["default", "modern", "colorful", "minimal"]
        }
