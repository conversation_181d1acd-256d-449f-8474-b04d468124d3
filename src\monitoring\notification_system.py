#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإشعارات والتنبيهات - Notification System
يدير إرسال الإشعارات والتنبيهات عبر قنوات متعددة
"""

import logging
import smtplib
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import requests
from plyer import notification as desktop_notification

@dataclass
class NotificationMessage:
    """رسالة إشعار"""
    message_id: str
    timestamp: datetime
    title: str
    message: str
    severity: str  # INFO, WARNING, ERROR, CRITICAL
    category: str  # SYSTEM, CONTENT, PUBLISHING, VALIDATION, PERFORMANCE
    channels: List[str]  # desktop, email, webhook, log
    metadata: Dict[str, Any] = None
    sent_channels: List[str] = None
    failed_channels: List[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.sent_channels is None:
            self.sent_channels = []
        if self.failed_channels is None:
            self.failed_channels = []

@dataclass
class NotificationChannel:
    """قناة إشعار"""
    name: str
    enabled: bool
    config: Dict[str, Any]
    handler_function: str
    retry_enabled: bool = True
    retry_delay: int = 60  # ثانية

class NotificationSystem:
    """نظام الإشعارات والتنبيهات"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # إعدادات الإشعارات
        self.notification_settings = self._load_notification_settings()
        
        # قنوات الإشعار
        self.channels = self._load_notification_channels()
        
        # قائمة انتظار الإشعارات
        self.notification_queue = []
        self.queue_lock = threading.Lock()
        
        # معالجات الإشعارات المخصصة
        self.custom_handlers = {}
        
        # إحصائيات الإشعارات
        self.notification_stats = {
            "total_notifications": 0,
            "successful_notifications": 0,
            "failed_notifications": 0,
            "notifications_by_severity": {"INFO": 0, "WARNING": 0, "ERROR": 0, "CRITICAL": 0},
            "notifications_by_channel": {},
            "last_notification": None
        }
        
        # حالة النظام
        self.system_active = False
        self.worker_thread = None
        self.stop_event = threading.Event()
        
        # ملفات البيانات
        self.data_dir = Path.home() / ".smart_content_app" / "notifications"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.notifications_file = self.data_dir / "notifications.json"
        self.stats_file = self.data_dir / "notification_stats.json"
        
        # تحميل البيانات المحفوظة
        self._load_saved_data()
        
        self.logger.info("تم تهيئة نظام الإشعارات والتنبيهات")
    
    def _load_notification_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات الإشعارات"""
        try:
            return self.config_manager.get_setting("notifications", "settings", {
                "enable_desktop_notifications": True,
                "enable_email_notifications": False,
                "enable_webhook_notifications": False,
                "notification_sound": True,
                "auto_dismiss_timeout": 10,  # ثواني
                "max_queue_size": 100,
                "batch_notifications": False,
                "batch_interval": 300,  # 5 دقائق
                "severity_filters": {
                    "desktop": ["WARNING", "ERROR", "CRITICAL"],
                    "email": ["ERROR", "CRITICAL"],
                    "webhook": ["CRITICAL"],
                    "log": ["INFO", "WARNING", "ERROR", "CRITICAL"]
                }
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات الإشعارات: {str(e)}")
            return {}
    
    def _load_notification_channels(self) -> Dict[str, NotificationChannel]:
        """تحميل قنوات الإشعار"""
        try:
            channels_config = self.config_manager.get_setting("notifications", "channels", {
                "desktop": {
                    "enabled": True,
                    "config": {
                        "app_name": "Smart Content App",
                        "timeout": 10
                    }
                },
                "email": {
                    "enabled": False,
                    "config": {
                        "smtp_server": "smtp.gmail.com",
                        "smtp_port": 587,
                        "username": "",
                        "password": "",
                        "from_email": "",
                        "to_emails": []
                    }
                },
                "webhook": {
                    "enabled": False,
                    "config": {
                        "url": "",
                        "headers": {},
                        "timeout": 30
                    }
                },
                "log": {
                    "enabled": True,
                    "config": {
                        "log_level": "INFO"
                    }
                }
            })
            
            channels = {}
            for name, config in channels_config.items():
                channels[name] = NotificationChannel(
                    name=name,
                    enabled=config.get("enabled", False),
                    config=config.get("config", {}),
                    handler_function=f"_send_{name}_notification"
                )
            
            return channels
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل قنوات الإشعار: {str(e)}")
            return {}
    
    def _load_saved_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل الإحصائيات
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    self.notification_stats.update(saved_stats)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات المحفوظة: {str(e)}")
    
    def start_system(self):
        """بدء نظام الإشعارات"""
        try:
            if self.system_active:
                self.logger.warning("نظام الإشعارات نشط بالفعل")
                return
            
            self.system_active = True
            self.stop_event.clear()
            
            # بدء خيط المعالجة
            self.worker_thread = threading.Thread(
                target=self._notification_worker,
                daemon=True,
                name="NotificationWorker"
            )
            self.worker_thread.start()
            
            self.logger.info("تم بدء نظام الإشعارات")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء نظام الإشعارات: {str(e)}")
            self.system_active = False
    
    def stop_system(self):
        """إيقاف نظام الإشعارات"""
        try:
            if not self.system_active:
                return
            
            self.system_active = False
            self.stop_event.set()
            
            # انتظار انتهاء خيط المعالجة
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=5)
            
            # حفظ البيانات النهائية
            self._save_notification_stats()
            
            self.logger.info("تم إيقاف نظام الإشعارات")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف نظام الإشعارات: {str(e)}")
    
    def send_notification(self, title: str, message: str, severity: str = "INFO", 
                         category: str = "SYSTEM", channels: List[str] = None,
                         metadata: Dict[str, Any] = None) -> str:
        """إرسال إشعار"""
        try:
            # إنشاء رسالة الإشعار
            notification_msg = NotificationMessage(
                message_id=f"notif_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.notification_queue)}",
                timestamp=datetime.now(),
                title=title,
                message=message,
                severity=severity,
                category=category,
                channels=channels or ["desktop", "log"],
                metadata=metadata or {}
            )
            
            # إضافة إلى قائمة الانتظار
            with self.queue_lock:
                if len(self.notification_queue) >= self.notification_settings.get("max_queue_size", 100):
                    # إزالة أقدم إشعار
                    self.notification_queue.pop(0)
                
                self.notification_queue.append(notification_msg)
            
            # تحديث الإحصائيات
            self.notification_stats["total_notifications"] += 1
            self.notification_stats["notifications_by_severity"][severity] += 1
            self.notification_stats["last_notification"] = datetime.now()
            
            self.logger.debug(f"تم إضافة إشعار إلى قائمة الانتظار: {notification_msg.message_id}")
            
            return notification_msg.message_id
            
        except Exception as e:
            self.logger.error(f"خطأ في إرسال الإشعار: {str(e)}")
            return ""
    
    def _notification_worker(self):
        """معالج الإشعارات"""
        try:
            while not self.stop_event.wait(1):  # فحص كل ثانية
                try:
                    # معالجة قائمة انتظار الإشعارات
                    notifications_to_process = []
                    
                    with self.queue_lock:
                        if self.notification_queue:
                            notifications_to_process = self.notification_queue.copy()
                            self.notification_queue.clear()
                    
                    # معالجة الإشعارات
                    for notification in notifications_to_process:
                        self._process_notification(notification)
                    
                except Exception as e:
                    self.logger.error(f"خطأ في معالج الإشعارات: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"خطأ في خيط معالج الإشعارات: {str(e)}")
        finally:
            self.system_active = False

    def _process_notification(self, notification: NotificationMessage):
        """معالجة إشعار واحد"""
        try:
            # تصفية القنوات حسب الشدة
            severity_filters = self.notification_settings.get("severity_filters", {})
            filtered_channels = []

            for channel in notification.channels:
                if channel in severity_filters:
                    if notification.severity in severity_filters[channel]:
                        filtered_channels.append(channel)
                else:
                    filtered_channels.append(channel)

            # إرسال عبر كل قناة
            for channel in filtered_channels:
                if channel in self.channels and self.channels[channel].enabled:
                    success = self._send_to_channel(notification, channel)

                    if success:
                        notification.sent_channels.append(channel)
                        # تحديث إحصائيات القناة
                        if channel not in self.notification_stats["notifications_by_channel"]:
                            self.notification_stats["notifications_by_channel"][channel] = 0
                        self.notification_stats["notifications_by_channel"][channel] += 1
                    else:
                        notification.failed_channels.append(channel)

            # تحديث الإحصائيات العامة
            if notification.sent_channels:
                self.notification_stats["successful_notifications"] += 1
            else:
                self.notification_stats["failed_notifications"] += 1

            # حفظ الإشعار في السجل
            self._save_notification_to_log(notification)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة الإشعار: {str(e)}")

    def _send_to_channel(self, notification: NotificationMessage, channel: str) -> bool:
        """إرسال إشعار عبر قناة معينة"""
        try:
            handler_name = self.channels[channel].handler_function

            # البحث عن المعالج المخصص أولاً
            if channel in self.custom_handlers:
                return self.custom_handlers[channel](notification)

            # استخدام المعالج المدمج
            if hasattr(self, handler_name):
                handler = getattr(self, handler_name)
                return handler(notification)
            else:
                self.logger.error(f"معالج غير موجود للقناة: {channel}")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في إرسال الإشعار عبر القناة {channel}: {str(e)}")
            return False

    def _send_desktop_notification(self, notification: NotificationMessage) -> bool:
        """إرسال إشعار سطح المكتب"""
        try:
            config = self.channels["desktop"].config

            # تحديد أيقونة حسب الشدة
            icon_map = {
                "INFO": None,
                "WARNING": None,
                "ERROR": None,
                "CRITICAL": None
            }

            desktop_notification.notify(
                title=notification.title,
                message=notification.message,
                app_name=config.get("app_name", "Smart Content App"),
                timeout=config.get("timeout", 10),
                toast=True
            )

            return True

        except Exception as e:
            self.logger.error(f"خطأ في إرسال إشعار سطح المكتب: {str(e)}")
            return False

    def _send_email_notification(self, notification: NotificationMessage) -> bool:
        """إرسال إشعار بريد إلكتروني"""
        try:
            config = self.channels["email"].config

            if not config.get("username") or not config.get("password"):
                self.logger.warning("إعدادات البريد الإلكتروني غير مكتملة")
                return False

            # إنشاء الرسالة
            msg = MimeMultipart()
            msg['From'] = config.get("from_email", config["username"])
            msg['Subject'] = f"[{notification.severity}] {notification.title}"

            # محتوى الرسالة
            body = f"""
            العنوان: {notification.title}
            الرسالة: {notification.message}
            الشدة: {notification.severity}
            الفئة: {notification.category}
            الوقت: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

            معرف الإشعار: {notification.message_id}
            """

            msg.attach(MimeText(body, 'plain', 'utf-8'))

            # إرسال لكل عنوان بريد
            to_emails = config.get("to_emails", [])
            if not to_emails:
                self.logger.warning("لا توجد عناوين بريد للإرسال إليها")
                return False

            # الاتصال بخادم SMTP
            server = smtplib.SMTP(config["smtp_server"], config["smtp_port"])
            server.starttls()
            server.login(config["username"], config["password"])

            # إرسال الرسالة
            for email in to_emails:
                msg['To'] = email
                text = msg.as_string()
                server.sendmail(config["username"], email, text)
                del msg['To']

            server.quit()
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إرسال إشعار البريد الإلكتروني: {str(e)}")
            return False

    def _send_webhook_notification(self, notification: NotificationMessage) -> bool:
        """إرسال إشعار عبر webhook"""
        try:
            config = self.channels["webhook"].config

            if not config.get("url"):
                self.logger.warning("رابط webhook غير محدد")
                return False

            # إعداد البيانات
            payload = {
                "message_id": notification.message_id,
                "timestamp": notification.timestamp.isoformat(),
                "title": notification.title,
                "message": notification.message,
                "severity": notification.severity,
                "category": notification.category,
                "metadata": notification.metadata
            }

            # إعداد الرؤوس
            headers = {
                "Content-Type": "application/json",
                **config.get("headers", {})
            }

            # إرسال الطلب
            response = requests.post(
                config["url"],
                json=payload,
                headers=headers,
                timeout=config.get("timeout", 30)
            )

            response.raise_for_status()
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إرسال إشعار webhook: {str(e)}")
            return False

    def _send_log_notification(self, notification: NotificationMessage) -> bool:
        """إرسال إشعار إلى السجل"""
        try:
            config = self.channels["log"].config
            log_level = config.get("log_level", "INFO")

            log_message = f"[{notification.category}] {notification.title}: {notification.message}"

            if log_level == "DEBUG":
                self.logger.debug(log_message)
            elif log_level == "INFO":
                self.logger.info(log_message)
            elif log_level == "WARNING":
                self.logger.warning(log_message)
            elif log_level == "ERROR":
                self.logger.error(log_message)
            elif log_level == "CRITICAL":
                self.logger.critical(log_message)

            return True

        except Exception as e:
            self.logger.error(f"خطأ في إرسال إشعار السجل: {str(e)}")
            return False

    def _save_notification_to_log(self, notification: NotificationMessage):
        """حفظ الإشعار في سجل الإشعارات"""
        try:
            # قراءة السجل الحالي
            notifications_log = []
            if self.notifications_file.exists():
                try:
                    with open(self.notifications_file, 'r', encoding='utf-8') as f:
                        notifications_log = json.load(f)
                except:
                    notifications_log = []

            # إضافة الإشعار الجديد
            notification_dict = asdict(notification)
            notification_dict["timestamp"] = notification.timestamp.isoformat()
            notifications_log.append(notification_dict)

            # الاحتفاظ بآخر 1000 إشعار فقط
            if len(notifications_log) > 1000:
                notifications_log = notifications_log[-1000:]

            # حفظ السجل
            with open(self.notifications_file, 'w', encoding='utf-8') as f:
                json.dump(notifications_log, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإشعار في السجل: {str(e)}")

    def _save_notification_stats(self):
        """حفظ إحصائيات الإشعارات"""
        try:
            stats_to_save = self.notification_stats.copy()

            # تحويل datetime إلى string
            if stats_to_save["last_notification"]:
                stats_to_save["last_notification"] = stats_to_save["last_notification"].isoformat()

            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_to_save, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ إحصائيات الإشعارات: {str(e)}")

    def add_custom_handler(self, channel: str, handler: Callable[[NotificationMessage], bool]):
        """إضافة معالج إشعارات مخصص"""
        try:
            self.custom_handlers[channel] = handler
            self.logger.info(f"تم إضافة معالج مخصص للقناة: {channel}")
        except Exception as e:
            self.logger.error(f"خطأ في إضافة معالج مخصص: {str(e)}")

    def remove_custom_handler(self, channel: str):
        """إزالة معالج إشعارات مخصص"""
        try:
            if channel in self.custom_handlers:
                del self.custom_handlers[channel]
                self.logger.info(f"تم إزالة معالج مخصص للقناة: {channel}")
        except Exception as e:
            self.logger.error(f"خطأ في إزالة معالج مخصص: {str(e)}")

    def get_notification_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الإشعارات"""
        try:
            stats = self.notification_stats.copy()

            # حساب معدلات النجاح
            total = stats["total_notifications"]
            if total > 0:
                stats["success_rate"] = (stats["successful_notifications"] / total) * 100
                stats["failure_rate"] = (stats["failed_notifications"] / total) * 100
            else:
                stats["success_rate"] = 0
                stats["failure_rate"] = 0

            # تحويل datetime إلى string
            if stats["last_notification"]:
                stats["last_notification"] = stats["last_notification"].isoformat()

            # إضافة معلومات القنوات
            stats["active_channels"] = [
                name for name, channel in self.channels.items() if channel.enabled
            ]

            stats["queue_size"] = len(self.notification_queue)
            stats["system_active"] = self.system_active

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الإشعارات: {str(e)}")
            return {}

    def get_recent_notifications(self, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على الإشعارات الحديثة"""
        try:
            if not self.notifications_file.exists():
                return []

            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)

            # إرجاع آخر notifications حسب الحد المطلوب
            return notifications[-limit:] if limit > 0 else notifications

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإشعارات الحديثة: {str(e)}")
            return []

    def get_notifications_by_severity(self, severity: str, hours: int = 24) -> List[Dict[str, Any]]:
        """الحصول على الإشعارات حسب الشدة"""
        try:
            notifications = self.get_recent_notifications(limit=-1)
            cutoff_time = datetime.now() - timedelta(hours=hours)

            filtered_notifications = []
            for notif in notifications:
                notif_time = datetime.fromisoformat(notif["timestamp"])
                if notif["severity"] == severity and notif_time > cutoff_time:
                    filtered_notifications.append(notif)

            return filtered_notifications

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإشعارات حسب الشدة: {str(e)}")
            return []

    def get_notifications_by_category(self, category: str, hours: int = 24) -> List[Dict[str, Any]]:
        """الحصول على الإشعارات حسب الفئة"""
        try:
            notifications = self.get_recent_notifications(limit=-1)
            cutoff_time = datetime.now() - timedelta(hours=hours)

            filtered_notifications = []
            for notif in notifications:
                notif_time = datetime.fromisoformat(notif["timestamp"])
                if notif["category"] == category and notif_time > cutoff_time:
                    filtered_notifications.append(notif)

            return filtered_notifications

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإشعارات حسب الفئة: {str(e)}")
            return []

    def update_channel_config(self, channel: str, config: Dict[str, Any]):
        """تحديث إعدادات قناة"""
        try:
            if channel in self.channels:
                self.channels[channel].config.update(config)

                # حفظ في الإعدادات
                channels_config = {}
                for name, ch in self.channels.items():
                    channels_config[name] = {
                        "enabled": ch.enabled,
                        "config": ch.config
                    }

                self.config_manager.set_setting("notifications", "channels", channels_config)
                self.logger.info(f"تم تحديث إعدادات القناة: {channel}")
            else:
                self.logger.error(f"قناة غير موجودة: {channel}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات القناة: {str(e)}")

    def enable_channel(self, channel: str):
        """تفعيل قناة"""
        try:
            if channel in self.channels:
                self.channels[channel].enabled = True
                self.logger.info(f"تم تفعيل القناة: {channel}")
            else:
                self.logger.error(f"قناة غير موجودة: {channel}")
        except Exception as e:
            self.logger.error(f"خطأ في تفعيل القناة: {str(e)}")

    def disable_channel(self, channel: str):
        """تعطيل قناة"""
        try:
            if channel in self.channels:
                self.channels[channel].enabled = False
                self.logger.info(f"تم تعطيل القناة: {channel}")
            else:
                self.logger.error(f"قناة غير موجودة: {channel}")
        except Exception as e:
            self.logger.error(f"خطأ في تعطيل القناة: {str(e)}")

    def test_channel(self, channel: str) -> bool:
        """اختبار قناة إشعار"""
        try:
            test_notification = NotificationMessage(
                message_id=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.now(),
                title="اختبار الإشعار",
                message="هذا إشعار اختبار للتحقق من عمل القناة",
                severity="INFO",
                category="SYSTEM",
                channels=[channel]
            )

            success = self._send_to_channel(test_notification, channel)

            if success:
                self.logger.info(f"نجح اختبار القناة: {channel}")
            else:
                self.logger.error(f"فشل اختبار القناة: {channel}")

            return success

        except Exception as e:
            self.logger.error(f"خطأ في اختبار القناة: {str(e)}")
            return False

    def clear_notification_queue(self):
        """مسح قائمة انتظار الإشعارات"""
        try:
            with self.queue_lock:
                cleared_count = len(self.notification_queue)
                self.notification_queue.clear()

            self.logger.info(f"تم مسح {cleared_count} إشعار من قائمة الانتظار")

        except Exception as e:
            self.logger.error(f"خطأ في مسح قائمة الانتظار: {str(e)}")

    def export_notifications_report(self, file_path: str, days: int = 7):
        """تصدير تقرير الإشعارات"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # جمع البيانات
            all_notifications = self.get_recent_notifications(limit=-1)

            # تصفية البيانات حسب التاريخ
            filtered_notifications = [
                notif for notif in all_notifications
                if datetime.fromisoformat(notif["timestamp"]) > cutoff_date
            ]

            # إنشاء التقرير
            report = {
                "report_generated": datetime.now().isoformat(),
                "period_days": days,
                "notification_stats": self.get_notification_stats(),
                "notification_settings": self.notification_settings,
                "channels_config": {
                    name: {
                        "enabled": channel.enabled,
                        "config": channel.config
                    }
                    for name, channel in self.channels.items()
                },
                "notifications": filtered_notifications,
                "summary": {
                    "total_notifications": len(filtered_notifications),
                    "notifications_by_severity": {},
                    "notifications_by_category": {},
                    "notifications_by_channel": {}
                }
            }

            # حساب الملخص
            for notif in filtered_notifications:
                severity = notif["severity"]
                category = notif["category"]

                if severity not in report["summary"]["notifications_by_severity"]:
                    report["summary"]["notifications_by_severity"][severity] = 0
                report["summary"]["notifications_by_severity"][severity] += 1

                if category not in report["summary"]["notifications_by_category"]:
                    report["summary"]["notifications_by_category"][category] = 0
                report["summary"]["notifications_by_category"][category] += 1

                for channel in notif["sent_channels"]:
                    if channel not in report["summary"]["notifications_by_channel"]:
                        report["summary"]["notifications_by_channel"][channel] = 0
                    report["summary"]["notifications_by_channel"][channel] += 1

            # حفظ التقرير
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير تقرير الإشعارات إلى {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في تصدير تقرير الإشعارات: {str(e)}")

    def cleanup_old_notifications(self, days: int = 30):
        """تنظيف الإشعارات القديمة"""
        try:
            if not self.notifications_file.exists():
                return

            cutoff_date = datetime.now() - timedelta(days=days)

            # قراءة الإشعارات الحالية
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                notifications = json.load(f)

            # تصفية الإشعارات الحديثة
            filtered_notifications = [
                notif for notif in notifications
                if datetime.fromisoformat(notif["timestamp"]) > cutoff_date
            ]

            # حفظ الإشعارات المصفاة
            with open(self.notifications_file, 'w', encoding='utf-8') as f:
                json.dump(filtered_notifications, f, ensure_ascii=False, indent=2)

            removed_count = len(notifications) - len(filtered_notifications)
            self.logger.info(f"تم تنظيف {removed_count} إشعار قديم")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الإشعارات القديمة: {str(e)}")

    def __del__(self):
        """تنظيف الموارد"""
        try:
            self.stop_system()
        except:
            pass
