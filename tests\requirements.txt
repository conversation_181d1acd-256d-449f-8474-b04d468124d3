# متطلبات الاختبارات - Testing Requirements

# إطار عمل الاختبارات الأساسي
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0
pytest-html>=3.1.0

# مكتبات مراقبة الأداء
psutil>=5.9.0
memory-profiler>=0.60.0

# مكتبات المعالجة العلمية
numpy>=1.21.0
scipy>=1.9.0

# مكتبات واجهة المستخدم للاختبار
PyQt6>=6.4.0

# مكتبات الشبكة والطلبات
requests>=2.28.0
responses>=0.22.0

# مكتبات معالجة الملفات
Pillow>=9.0.0
opencv-python>=4.6.0

# مكتبات الأمان والتشفير
cryptography>=38.0.0
bcrypt>=4.0.0

# مكتبات قواعد البيانات
sqlite3

# مكتبات التوقيت والجدولة
schedule>=1.2.0

# مكتبات التحقق والتصديق
jsonschema>=4.17.0

# مكتبات السجلات
loguru>=0.6.0

# مكتبات التحليل والإحصائيات
matplotlib>=3.6.0
seaborn>=0.12.0

# مكتبات إضافية للاختبار
faker>=15.0.0
factory-boy>=3.2.0
freezegun>=1.2.0
