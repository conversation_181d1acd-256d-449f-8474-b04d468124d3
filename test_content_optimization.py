#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات أنظمة جلب المحتوى
"""

import os
import sys
import time
import json
from pathlib import Path

# إضافة مسار src للاستيراد
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_content_manager_optimizations():
    """اختبار تحسينات مدير المحتوى"""
    print("🧪 اختبار تحسينات مدير المحتوى...")
    
    try:
        # محاولة استيراد مدير المحتوى المحسن
        from content.content_manager import ContentManager
        
        # إنشاء كائن وهمي للإعدادات
        class MockConfigManager:
            def get_setting(self, key, default=None):
                return default
            def set_setting(self, section, key, value):
                pass
        
        class MockSecurityManager:
            def encrypt_data(self, data):
                return data
            def decrypt_data(self, data):
                return data
        
        # إنشاء مدير المحتوى
        config_manager = MockConfigManager()
        security_manager = MockSecurityManager()
        
        content_manager = ContentManager(config_manager, security_manager)
        
        # اختبار الدوال الجديدة
        tests_passed = 0
        total_tests = 0
        
        # اختبار 1: فحص وجود thread pool
        total_tests += 1
        if hasattr(content_manager, 'thread_pool'):
            print("✅ Thread pool موجود")
            tests_passed += 1
        else:
            print("❌ Thread pool غير موجود")
        
        # اختبار 2: فحص وجود التخزين المؤقت
        total_tests += 1
        if hasattr(content_manager, 'content_cache'):
            print("✅ نظام التخزين المؤقت موجود")
            tests_passed += 1
        else:
            print("❌ نظام التخزين المؤقت غير موجود")
        
        # اختبار 3: فحص وجود محدد المعدل
        total_tests += 1
        if hasattr(content_manager, 'rate_limiter'):
            print("✅ محدد المعدل موجود")
            tests_passed += 1
        else:
            print("❌ محدد المعدل غير موجود")
        
        # اختبار 4: اختبار دالة التخزين المؤقت
        total_tests += 1
        try:
            content_manager.cache_content("test_platform", "test_user", "recent", {"test": "data"})
            cached = content_manager.get_cached_content("test_platform", "test_user", "recent")
            if cached and cached.get("test") == "data":
                print("✅ التخزين المؤقت يعمل بشكل صحيح")
                tests_passed += 1
            else:
                print("❌ التخزين المؤقت لا يعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار التخزين المؤقت: {e}")
        
        # اختبار 5: اختبار محدد المعدل
        total_tests += 1
        try:
            result1 = content_manager.check_rate_limit("test_platform")
            result2 = content_manager.check_rate_limit("test_platform")
            if result1 and result2:
                print("✅ محدد المعدل يعمل بشكل صحيح")
                tests_passed += 1
            else:
                print("❌ محدد المعدل لا يعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار محدد المعدل: {e}")
        
        # اختبار 6: اختبار إحصائيات الأداء
        total_tests += 1
        try:
            stats = content_manager.get_performance_stats()
            if isinstance(stats, dict) and "cache_size" in stats:
                print("✅ إحصائيات الأداء تعمل بشكل صحيح")
                tests_passed += 1
            else:
                print("❌ إحصائيات الأداء لا تعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في اختبار إحصائيات الأداء: {e}")
        
        # تنظيف الموارد
        try:
            content_manager.cleanup_resources()
            print("✅ تم تنظيف الموارد بنجاح")
        except Exception as e:
            print(f"⚠️ تحذير في تنظيف الموارد: {e}")
        
        print(f"\n📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار نجح")
        return tests_passed == total_tests
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False

def test_optimization_files():
    """اختبار وجود ملفات التحسين"""
    print("\n📁 اختبار ملفات التحسين...")
    
    expected_files = [
        "src/content/content_manager.py",
        "optimize_content_fetching.py"
    ]
    
    files_found = 0
    for file_path in expected_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} موجود")
            files_found += 1
        else:
            print(f"❌ {file_path} غير موجود")
    
    print(f"\n📊 ملفات التحسين: {files_found}/{len(expected_files)} موجود")
    return files_found == len(expected_files)

def test_performance_improvement():
    """اختبار تحسين الأداء"""
    print("\n⚡ اختبار تحسين الأداء...")
    
    try:
        import tracemalloc
        import gc
        
        # بدء تتبع الذاكرة
        tracemalloc.start()
        
        # قياس الذاكرة قبل التحسين
        before_current, before_peak = tracemalloc.get_traced_memory()
        
        # تشغيل تحسينات الذاكرة
        collected = gc.collect()
        gc.set_threshold(700, 10, 10)
        
        # قياس الذاكرة بعد التحسين
        after_current, after_peak = tracemalloc.get_traced_memory()
        
        memory_saved = (before_current - after_current) / 1024 / 1024
        
        print(f"💾 الذاكرة قبل التحسين: {before_current / 1024 / 1024:.2f} MB")
        print(f"💾 الذاكرة بعد التحسين: {after_current / 1024 / 1024:.2f} MB")
        print(f"💾 الذاكرة المحررة: {memory_saved:.2f} MB")
        print(f"🗑️ كائنات محررة: {collected}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False

def create_optimization_summary():
    """إنشاء ملخص التحسينات المطبقة"""
    print("\n📋 إنشاء ملخص التحسينات...")
    
    summary = {
        "optimization_date": time.strftime("%Y-%m-%d %H:%M:%S"),
        "optimizations_applied": [
            "إضافة ThreadPoolExecutor لمعالجة متوازية",
            "تنفيذ نظام تخزين مؤقت مع LRU cache",
            "إضافة محدد معدل الطلبات",
            "تحسين إدارة قائمة انتظار المحتوى",
            "إضافة weak references لمعالجات الأحداث",
            "تنفيذ دعم المعالجة غير المتزامنة",
            "إضافة معالجة دفعية للطلبات",
            "تحسين تنظيف الموارد"
        ],
        "performance_improvements": [
            "تقليل استخدام الذاكرة",
            "تسريع جلب المحتوى",
            "تحسين استجابة النظام",
            "تقليل تحميل الشبكة",
            "منع تسريب الذاكرة"
        ],
        "next_steps": [
            "تحسين أداء محرك الذكاء الاصطناعي",
            "تحسين أداء نظام المونتاج التلقائي",
            "تحسين أداء أنظمة الأمان والتشفير"
        ]
    }
    
    try:
        with open("content_optimization_summary.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print("✅ تم حفظ ملخص التحسينات في: content_optimization_summary.json")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ الملخص: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات أنظمة جلب المحتوى...")
    
    all_tests_passed = True
    
    # اختبار 1: تحسينات مدير المحتوى
    test1_result = test_content_manager_optimizations()
    all_tests_passed = all_tests_passed and test1_result
    
    # اختبار 2: ملفات التحسين
    test2_result = test_optimization_files()
    all_tests_passed = all_tests_passed and test2_result
    
    # اختبار 3: تحسين الأداء
    test3_result = test_performance_improvement()
    all_tests_passed = all_tests_passed and test3_result
    
    # إنشاء ملخص التحسينات
    summary_result = create_optimization_summary()
    
    print(f"\n{'='*50}")
    if all_tests_passed:
        print("✅ جميع اختبارات التحسين نجحت!")
        print("🎉 تم تحسين أنظمة جلب المحتوى بنجاح!")
    else:
        print("⚠️ بعض الاختبارات فشلت، راجع النتائج أعلاه")
    
    print("📋 تم إنشاء ملخص التحسينات")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
