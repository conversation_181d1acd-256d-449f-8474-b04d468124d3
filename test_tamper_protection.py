#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الحماية من التلاعب - Tamper Protection System Test
اختبار شامل لجميع وظائف نظام الحماية من التلاعب
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from security.tamper_protection import (
        TamperProtectionSystem, 
        ProtectionLevel, 
        IntegrityStatus
    )
    print("✅ تم استيراد نظام الحماية من التلاعب بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد نظام الحماية من التلاعب: {e}")
    sys.exit(1)

def test_tamper_protection_system():
    """اختبار نظام الحماية من التلاعب"""
    print("\n🔒 بدء اختبار نظام الحماية من التلاعب...")
    
    # إنشاء مجلد مؤقت للاختبار
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 مجلد الاختبار: {temp_dir}")
        
        try:
            # تهيئة نظام الحماية
            print("\n1️⃣ تهيئة نظام الحماية من التلاعب...")
            tamper_system = TamperProtectionSystem(base_dir=temp_dir)
            print("✅ تم تهيئة نظام الحماية من التلاعب")
            
            # إنشاء ملفات اختبار
            print("\n2️⃣ إنشاء ملفات الاختبار...")
            test_files = []
            
            # ملف نصي
            text_file = Path(temp_dir) / "test_document.txt"
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write("هذا ملف اختبار للحماية من التلاعب\nيحتوي على نص عربي وإنجليزي\nTest content for tamper protection")
            test_files.append(str(text_file))
            
            # ملف JSON
            json_file = Path(temp_dir) / "test_config.json"
            import json
            test_config = {
                "app_name": "تطبيق اختبار الحماية",
                "version": "1.0.0",
                "settings": {
                    "language": "ar",
                    "theme": "dark"
                }
            }
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, ensure_ascii=False, indent=2)
            test_files.append(str(json_file))
            
            # ملف ثنائي صغير
            binary_file = Path(temp_dir) / "test_binary.dat"
            with open(binary_file, 'wb') as f:
                f.write(b'\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F')
            test_files.append(str(binary_file))
            
            print(f"✅ تم إنشاء {len(test_files)} ملف اختبار")
            
            # اختبار حماية الملفات بمستويات مختلفة
            print("\n3️⃣ اختبار حماية الملفات...")
            protection_levels = [
                ProtectionLevel.BASIC,
                ProtectionLevel.STANDARD,
                ProtectionLevel.ADVANCED
            ]
            
            for i, file_path in enumerate(test_files):
                level = protection_levels[i % len(protection_levels)]
                success = tamper_system.protect_file(file_path, level)
                if success:
                    print(f"✅ تم حماية الملف {Path(file_path).name} بمستوى {level.value}")
                else:
                    print(f"❌ فشل في حماية الملف {Path(file_path).name}")
            
            # اختبار التحقق من سلامة الملفات
            print("\n4️⃣ اختبار التحقق من سلامة الملفات...")
            for file_path in test_files:
                status, details = tamper_system.verify_file_integrity(file_path)
                print(f"📄 {Path(file_path).name}: {status.value}")
                if status == IntegrityStatus.INTACT:
                    print(f"   ✅ الملف سليم")
                else:
                    print(f"   ⚠️ مشكلة في الملف: {details}")
            
            # اختبار فحص جميع الملفات
            print("\n5️⃣ اختبار فحص جميع الملفات المحمية...")
            scan_results = tamper_system.scan_protected_files()
            intact_count = sum(1 for status, _ in scan_results.values() if status == IntegrityStatus.INTACT)
            print(f"✅ نتائج الفحص: {intact_count}/{len(scan_results)} ملف سليم")
            
            # اختبار تعديل ملف ومراقبة التلاعب
            print("\n6️⃣ اختبار كشف التلاعب...")
            test_file_for_tampering = test_files[0]
            
            # تعديل الملف
            with open(test_file_for_tampering, 'a', encoding='utf-8') as f:
                f.write("\nهذا نص مضاف للاختبار - This is added text for testing")
            
            # التحقق من كشف التعديل
            status, details = tamper_system.verify_file_integrity(test_file_for_tampering)
            if status == IntegrityStatus.MODIFIED:
                print(f"✅ تم كشف التعديل في الملف {Path(test_file_for_tampering).name}")
                print(f"   📊 تفاصيل التحقق: {details}")
            else:
                print(f"❌ لم يتم كشف التعديل في الملف")
            
            # اختبار الإحصائيات
            print("\n7️⃣ اختبار الإحصائيات...")
            stats = tamper_system.get_protection_statistics()
            print(f"📊 إحصائيات الحماية:")
            print(f"   📁 إجمالي الملفات المحمية: {stats.get('total_protected_files', 0)}")
            print(f"   🚨 إجمالي التنبيهات: {stats.get('total_alerts', 0)}")
            print(f"   📈 مستويات الحماية: {stats.get('protection_levels', {})}")
            print(f"   📋 حالات الملفات: {stats.get('file_statuses', {})}")
            
            # اختبار التنبيهات
            print("\n8️⃣ اختبار التنبيهات...")
            alerts = tamper_system.get_alerts(limit=5)
            print(f"🚨 عدد التنبيهات الحديثة: {len(alerts)}")
            for alert in alerts:
                print(f"   ⚠️ {alert.alert_type}: {alert.description}")
                print(f"      📅 تاريخ الاكتشاف: {alert.detected_at}")
                print(f"      🔥 الشدة: {alert.severity}")
            
            # اختبار تحديث حماية الملف
            print("\n9️⃣ اختبار تحديث حماية الملف...")
            success = tamper_system.update_file_protection(test_file_for_tampering)
            if success:
                print(f"✅ تم تحديث حماية الملف {Path(test_file_for_tampering).name}")
                
                # التحقق مرة أخرى
                status, _ = tamper_system.verify_file_integrity(test_file_for_tampering)
                if status == IntegrityStatus.INTACT:
                    print(f"✅ الملف أصبح سليماً بعد التحديث")
                else:
                    print(f"❌ الملف لا يزال غير سليم")
            else:
                print(f"❌ فشل في تحديث حماية الملف")
            
            # اختبار النسخ الاحتياطي
            print("\n🔟 اختبار النسخ الاحتياطي...")
            backup_path = Path(temp_dir) / "backup_test"
            success = tamper_system.backup_protection_database(str(backup_path))
            if success:
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
                
                # التحقق من وجود ملفات النسخة الاحتياطية
                backup_files = list(backup_path.glob("*"))
                print(f"   📁 ملفات النسخة الاحتياطية: {len(backup_files)}")
                for backup_file in backup_files:
                    print(f"      📄 {backup_file.name}")
            else:
                print(f"❌ فشل في إنشاء النسخة الاحتياطية")
            
            # اختبار تصدير التقرير
            print("\n1️⃣1️⃣ اختبار تصدير التقرير...")
            report_file = tamper_system.export_protection_report()
            if report_file:
                print(f"✅ تم تصدير التقرير: {report_file}")
                
                # قراءة التقرير للتحقق
                with open(report_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                print(f"   📊 التقرير يحتوي على {len(report_data.get('protected_files', {}))} ملف محمي")
                print(f"   🚨 التقرير يحتوي على {len(report_data.get('recent_alerts', []))} تنبيه حديث")
            else:
                print(f"❌ فشل في تصدير التقرير")
            
            # اختبار حالة النظام
            print("\n1️⃣2️⃣ اختبار حالة النظام...")
            system_status = tamper_system.get_system_status()
            print(f"🔍 حالة النظام:")
            print(f"   ✅ النظام مهيأ: {system_status.get('system_initialized', False)}")
            print(f"   📊 حجم قاعدة البيانات: {system_status.get('protection_database_size', 0)}")
            print(f"   🚨 عدد التنبيهات: {system_status.get('alerts_count', 0)}")
            print(f"   ⚠️ التنبيهات غير المحلولة: {system_status.get('unresolved_alerts', 0)}")
            print(f"   💚 صحة النظام: {system_status.get('system_health', 'unknown')}")
            
            # اختبار تنظيف النظام
            print("\n1️⃣3️⃣ اختبار تنظيف النظام...")
            tamper_system.cleanup_system()
            print("✅ تم تنظيف النظام")
            
            print("\n🎉 تم اكتمال جميع اختبارات نظام الحماية من التلاعب بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام الحماية من التلاعب: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("🔒 اختبار نظام الحماية من التلاعب")
    print("=" * 50)
    
    success = test_tamper_protection_system()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        sys.exit(0)
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        sys.exit(1)
