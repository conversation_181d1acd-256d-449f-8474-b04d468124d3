#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل جميع اختبارات التطبيق
Run All Application Tests
"""

import sys
import os
import subprocess
import time
from pathlib import Path
import argparse

# إضافة مجلد المشروع إلى مسار Python
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """تشغيل أمر وطباعة النتائج"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ نجح في {duration:.2f} ثانية")
            if result.stdout:
                print(f"📄 النتائج:\n{result.stdout}")
        else:
            print(f"❌ فشل في {duration:.2f} ثانية")
            if result.stderr:
                print(f"🚨 الأخطاء:\n{result.stderr}")
            if result.stdout:
                print(f"📄 النتائج:\n{result.stdout}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الأمر: {str(e)}")
        return False

def install_test_dependencies():
    """تثبيت مكتبات الاختبار المطلوبة"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-asyncio>=0.21.0",
        "pytest-xdist>=3.0.0",  # للتشغيل المتوازي
        "pytest-html>=3.1.0",  # لتقارير HTML
        "coverage>=7.0.0"
    ]
    
    print("📦 تثبيت مكتبات الاختبار...")
    for dep in dependencies:
        command = f"pip install {dep}"
        success = run_command(command, f"تثبيت {dep}")
        if not success:
            print(f"⚠️ تحذير: فشل في تثبيت {dep}")

def run_unit_tests():
    """تشغيل اختبارات الوحدة"""
    commands = [
        ("python -m pytest tests/test_content_fetcher.py -v", "اختبارات نظام جلب المحتوى"),
        ("python -m pytest tests/test_ai_analysis.py -v", "اختبارات نظام الذكاء الاصطناعي"),
        ("python -m pytest tests/test_automated_editing.py -v", "اختبارات نظام المونتاج التلقائي"),
        ("python -m pytest tests/test_automated_publishing.py -v", "اختبارات نظام النشر التلقائي")
    ]
    
    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))
    
    return results

def run_security_tests():
    """تشغيل اختبارات الأمان"""
    commands = [
        ("python -m pytest tests/test_security_systems.py -v", "اختبارات أنظمة الأمان"),
        ("python -m pytest tests/test_security_systems.py::TestAdvancedEncryption -v", "اختبارات التشفير المتقدم"),
        ("python -m pytest tests/test_security_systems.py::TestAdvancedAuthentication -v", "اختبارات المصادقة المتقدمة"),
        ("python -m pytest tests/test_security_systems.py::TestMalwareProtection -v", "اختبارات الحماية من البرمجيات الخبيثة"),
        ("python -m pytest tests/test_security_systems.py::TestSecureBackup -v", "اختبارات النسخ الاحتياطي الآمن"),
        ("python -m pytest tests/test_security_systems.py::TestTamperProtection -v", "اختبارات الحماية من التلاعب")
    ]
    
    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))
    
    return results

def run_integration_tests():
    """تشغيل اختبارات التكامل"""
    commands = [
        ("python -m pytest tests/ -m integration -v", "جميع اختبارات التكامل")
    ]

    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))

    return results

def run_gui_tests():
    """تشغيل اختبارات واجهة المستخدم"""
    commands = [
        ("python -m pytest tests/test_gui.py -v", "اختبارات واجهة المستخدم الرسومية"),
        ("python -m pytest tests/test_gui.py::TestMainWindow -v", "اختبارات النافذة الرئيسية"),
        ("python -m pytest tests/test_gui.py::TestContentManagerWidget -v", "اختبارات ودجت إدارة المحتوى"),
        ("python -m pytest tests/test_gui.py::TestSettingsDialog -v", "اختبارات حوار الإعدادات"),
        ("python -m pytest tests/test_gui.py::TestMonitoringWidget -v", "اختبارات ودجت المراقبة"),
        ("python -m pytest tests/test_gui.py::TestSecurityWidget -v", "اختبارات ودجت الأمان")
    ]

    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))

    return results

def run_performance_tests():
    """تشغيل اختبارات الأداء"""
    commands = [
        ("python -m pytest tests/test_performance.py -v", "اختبارات الأداء والتحسين"),
        ("python -m pytest tests/test_performance.py::TestContentFetchingPerformance -v", "اختبارات أداء جلب المحتوى"),
        ("python -m pytest tests/test_performance.py::TestAIAnalysisPerformance -v", "اختبارات أداء الذكاء الاصطناعي"),
        ("python -m pytest tests/test_performance.py::TestVideoEditingPerformance -v", "اختبارات أداء المونتاج"),
        ("python -m pytest tests/test_performance.py::TestEncryptionPerformance -v", "اختبارات أداء التشفير"),
        ("python -m pytest tests/test_performance.py::TestSystemResourceUsage -v", "اختبارات استخدام موارد النظام")
    ]

    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))

    return results

def run_coverage_analysis():
    """تشغيل تحليل التغطية"""
    commands = [
        ("python -m pytest tests/ --cov=src --cov-report=html --cov-report=term", "تحليل تغطية الكود"),
        ("python -m coverage report --show-missing", "تقرير التغطية المفصل")
    ]
    
    results = []
    for command, description in commands:
        success = run_command(command, description)
        results.append((description, success))
    
    return results

def generate_test_report(all_results):
    """إنشاء تقرير شامل للاختبارات"""
    report_path = project_root / "test_report.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("تقرير اختبارات التطبيق الذكي لإنشاء المحتوى\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"تاريخ التشغيل: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            f.write(f"\n{category}:\n")
            f.write("-" * 40 + "\n")
            
            for test_name, success in results:
                status = "✅ نجح" if success else "❌ فشل"
                f.write(f"{status} {test_name}\n")
                total_tests += 1
                if success:
                    passed_tests += 1
        
        f.write(f"\n\nالملخص النهائي:\n")
        f.write("=" * 30 + "\n")
        f.write(f"إجمالي الاختبارات: {total_tests}\n")
        f.write(f"الاختبارات الناجحة: {passed_tests}\n")
        f.write(f"الاختبارات الفاشلة: {total_tests - passed_tests}\n")
        f.write(f"معدل النجاح: {(passed_tests/total_tests)*100:.1f}%\n")
    
    print(f"\n📊 تم إنشاء التقرير في: {report_path}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="تشغيل اختبارات التطبيق")
    parser.add_argument("--unit", action="store_true", help="تشغيل اختبارات الوحدة فقط")
    parser.add_argument("--security", action="store_true", help="تشغيل اختبارات الأمان فقط")
    parser.add_argument("--integration", action="store_true", help="تشغيل اختبارات التكامل فقط")
    parser.add_argument("--gui", action="store_true", help="تشغيل اختبارات واجهة المستخدم فقط")
    parser.add_argument("--performance", action="store_true", help="تشغيل اختبارات الأداء فقط")
    parser.add_argument("--coverage", action="store_true", help="تشغيل تحليل التغطية فقط")
    parser.add_argument("--install-deps", action="store_true", help="تثبيت مكتبات الاختبار")
    parser.add_argument("--all", action="store_true", help="تشغيل جميع الاختبارات")

    args = parser.parse_args()

    print("🚀 بدء تشغيل اختبارات التطبيق الذكي لإنشاء المحتوى")
    print("=" * 60)

    # تثبيت المكتبات إذا طُلب ذلك
    if args.install_deps or args.all:
        install_test_dependencies()

    all_results = {}

    # تشغيل الاختبارات حسب الخيارات المحددة
    if args.unit or args.all:
        print("\n🧪 تشغيل اختبارات الوحدة...")
        all_results["اختبارات الوحدة"] = run_unit_tests()

    if args.security or args.all:
        print("\n🔒 تشغيل اختبارات الأمان...")
        all_results["اختبارات الأمان"] = run_security_tests()

    if args.integration or args.all:
        print("\n🔗 تشغيل اختبارات التكامل...")
        all_results["اختبارات التكامل"] = run_integration_tests()

    if args.gui or args.all:
        print("\n🖥️ تشغيل اختبارات واجهة المستخدم...")
        all_results["اختبارات واجهة المستخدم"] = run_gui_tests()

    if args.performance or args.all:
        print("\n⚡ تشغيل اختبارات الأداء...")
        all_results["اختبارات الأداء"] = run_performance_tests()

    if args.coverage or args.all:
        print("\n📊 تشغيل تحليل التغطية...")
        all_results["تحليل التغطية"] = run_coverage_analysis()

    # إذا لم يتم تحديد أي خيار، تشغيل اختبارات الوحدة
    if not any([args.unit, args.security, args.integration, args.gui, args.performance, args.coverage, args.all]):
        print("\n🧪 تشغيل اختبارات الوحدة (افتراضي)...")
        all_results["اختبارات الوحدة"] = run_unit_tests()

    # إنشاء التقرير النهائي
    if all_results:
        generate_test_report(all_results)

    print("\n🎉 انتهى تشغيل الاختبارات!")

if __name__ == "__main__":
    main()
