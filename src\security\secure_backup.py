#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي الآمن - Secure Backup System
نظام شامل للنسخ الاحتياطي المشفر والاستعادة الآمنة
"""

import os
import time
import hashlib
import threading
import zipfile
import shutil
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

# مكتبات التشفير
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.backends import default_backend
    import base64
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("تحذير: مكتبة التشفير غير متوفرة. سيتم استخدام النسخ الاحتياطي غير المشفر.")

class BackupType(Enum):
    """نوع النسخة الاحتياطية"""
    FULL = "full"           # كاملة
    INCREMENTAL = "incremental"  # تزايدية
    DIFFERENTIAL = "differential"  # تفاضلية
    SELECTIVE = "selective"  # انتقائية

class BackupStatus(Enum):
    """حالة النسخة الاحتياطية"""
    PENDING = "pending"       # في الانتظار
    RUNNING = "running"       # قيد التشغيل
    COMPLETED = "completed"   # مكتملة
    FAILED = "failed"         # فاشلة
    CANCELLED = "cancelled"   # ملغية
    CORRUPTED = "corrupted"   # تالفة

class CompressionLevel(Enum):
    """مستوى الضغط"""
    NONE = 0      # بدون ضغط
    LOW = 1       # ضغط منخفض
    MEDIUM = 6    # ضغط متوسط
    HIGH = 9      # ضغط عالي

@dataclass
class BackupItem:
    """عنصر النسخة الاحتياطية"""
    source_path: str
    relative_path: str
    file_size: int
    file_hash: str
    modified_time: datetime
    is_directory: bool = False
    permissions: str = ""
    backup_time: Optional[datetime] = None

@dataclass
class BackupJob:
    """مهمة النسخة الاحتياطية"""
    job_id: str
    name: str
    description: str
    backup_type: BackupType
    source_paths: List[str]
    destination_path: str
    schedule_pattern: str
    encryption_enabled: bool
    compression_level: CompressionLevel
    include_patterns: List[str]
    exclude_patterns: List[str]
    retention_days: int
    created_at: datetime
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    is_active: bool = True

@dataclass
class BackupRecord:
    """سجل النسخة الاحتياطية"""
    backup_id: str
    job_id: str
    backup_type: BackupType
    status: BackupStatus
    start_time: datetime
    end_time: Optional[datetime]
    source_paths: List[str]
    backup_path: str
    total_files: int
    total_size: int
    compressed_size: int
    files_added: int
    files_modified: int
    files_deleted: int
    encryption_enabled: bool
    checksum: str
    error_message: str = ""
    duration_seconds: float = 0.0

class SecureBackupSystem:
    """نظام النسخ الاحتياطي الآمن"""
    
    def __init__(self, base_dir: str = "data"):
        """تهيئة نظام النسخ الاحتياطي الآمن"""
        self.base_dir = Path(base_dir)
        self.backup_dir = self.base_dir / "secure_backup"
        self.jobs_dir = self.backup_dir / "jobs"
        self.backups_dir = self.backup_dir / "backups"
        self.temp_dir = self.backup_dir / "temp"
        self.logs_dir = self.backup_dir / "logs"
        self.keys_dir = self.backup_dir / "keys"
        
        # إنشاء المجلدات
        for directory in [self.backup_dir, self.jobs_dir, self.backups_dir, 
                         self.temp_dir, self.logs_dir, self.keys_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # ملفات النظام
        self.jobs_file = self.backup_dir / "backup_jobs.json"
        self.records_file = self.backup_dir / "backup_records.json"
        self.config_file = self.backup_dir / "backup_config.json"
        self.master_key_file = self.keys_dir / "master.key"
        
        # قواعد البيانات
        self.backup_jobs: Dict[str, BackupJob] = {}
        self.backup_records: List[BackupRecord] = []
        
        # حالة النظام
        self.is_running = False
        self.current_backup: Optional[BackupRecord] = None
        self.scheduler_thread: Optional[threading.Thread] = None
        self.stop_scheduler = threading.Event()
        
        # مفاتيح التشفير
        self.master_key: Optional[bytes] = None
        self.encryption_keys: Dict[str, bytes] = {}
        
        # إعدادات النظام
        self.config = {
            "default_compression_level": CompressionLevel.MEDIUM.value,
            "default_retention_days": 30,
            "max_backup_size_gb": 100,
            "enable_encryption": True,
            "enable_compression": True,
            "backup_verification": True,
            "auto_cleanup": True,
            "max_concurrent_backups": 2,
            "backup_timeout_hours": 24,
            "chunk_size_mb": 64,
            "verify_after_backup": True,
            "create_recovery_info": True,
            "log_level": "INFO"
        }
        
        # إعداد نظام السجلات
        self.logger = logging.getLogger("SecureBackup")
        self.logger.setLevel(getattr(logging, self.config["log_level"]))
        
        # إعداد معالج السجلات
        log_file = self.logs_dir / "secure_backup.log"
        handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # فحص المتطلبات
        if not CRYPTO_AVAILABLE and self.config["enable_encryption"]:
            self.logger.warning("مكتبة التشفير غير متوفرة. سيتم تعطيل التشفير.")
            self.config["enable_encryption"] = False
        
        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            # تحميل الإعدادات
            self._load_config()
            
            # تهيئة التشفير
            if self.config["enable_encryption"] and CRYPTO_AVAILABLE:
                self._initialize_encryption()
            
            # تحميل قواعد البيانات
            self._load_backup_jobs()
            self._load_backup_records()
            
            # بدء المجدول
            self.start_scheduler()
            
            self.logger.info("تم تهيئة نظام النسخ الاحتياطي الآمن بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة النظام: {str(e)}")
    
    def _initialize_encryption(self):
        """تهيئة نظام التشفير"""
        try:
            # تحميل أو إنشاء المفتاح الرئيسي
            if self.master_key_file.exists():
                with open(self.master_key_file, 'rb') as f:
                    self.master_key = f.read()
            else:
                # إنشاء مفتاح رئيسي جديد
                self.master_key = Fernet.generate_key()
                with open(self.master_key_file, 'wb') as f:
                    f.write(self.master_key)
                
                # تأمين ملف المفتاح
                os.chmod(self.master_key_file, 0o600)
            
            self.logger.info("تم تهيئة نظام التشفير بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة التشفير: {str(e)}")
            self.config["enable_encryption"] = False
    
    def _generate_encryption_key(self, password: str, salt: bytes = None) -> bytes:
        """توليد مفتاح تشفير من كلمة مرور"""
        try:
            if salt is None:
                salt = os.urandom(16)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            return key
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد مفتاح التشفير: {str(e)}")
            return None
    
    def _load_config(self):
        """تحميل الإعدادات"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def _save_config(self):
        """حفظ الإعدادات"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def create_backup_job(self, name: str, description: str, source_paths: List[str],
                         destination_path: str, backup_type: BackupType = BackupType.FULL,
                         schedule_pattern: str = "daily", encryption_enabled: bool = True,
                         compression_level: CompressionLevel = CompressionLevel.MEDIUM,
                         include_patterns: List[str] = None, exclude_patterns: List[str] = None,
                         retention_days: int = 30) -> Tuple[bool, str]:
        """إنشاء مهمة نسخ احتياطي جديدة"""
        try:
            # التحقق من صحة المسارات
            for source_path in source_paths:
                if not os.path.exists(source_path):
                    return False, f"المسار المصدر غير موجود: {source_path}"
            
            # إنشاء معرف فريد للمهمة
            job_id = self._generate_job_id()
            
            # إنشاء مهمة النسخ الاحتياطي
            backup_job = BackupJob(
                job_id=job_id,
                name=name,
                description=description,
                backup_type=backup_type,
                source_paths=source_paths,
                destination_path=destination_path,
                schedule_pattern=schedule_pattern,
                encryption_enabled=encryption_enabled and self.config["enable_encryption"],
                compression_level=compression_level,
                include_patterns=include_patterns or [],
                exclude_patterns=exclude_patterns or ["*.tmp", "*.log", "__pycache__"],
                retention_days=retention_days,
                created_at=datetime.now()
            )
            
            # حساب موعد التشغيل التالي
            backup_job.next_run = self._calculate_next_run(schedule_pattern)
            
            # حفظ المهمة
            self.backup_jobs[job_id] = backup_job
            self._save_backup_jobs()
            
            self.logger.info(f"تم إنشاء مهمة النسخ الاحتياطي: {name} ({job_id})")
            return True, job_id
            
        except Exception as e:
            error_msg = f"خطأ في إنشاء مهمة النسخ الاحتياطي: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def run_backup_job(self, job_id: str, backup_type: BackupType = None) -> Tuple[bool, str]:
        """تشغيل مهمة النسخ الاحتياطي"""
        try:
            if job_id not in self.backup_jobs:
                return False, "مهمة النسخ الاحتياطي غير موجودة"

            job = self.backup_jobs[job_id]
            if not job.is_active:
                return False, "مهمة النسخ الاحتياطي غير نشطة"

            # استخدام نوع النسخة المحدد أو النوع الافتراضي للمهمة
            if backup_type is None:
                backup_type = job.backup_type

            # إنشاء سجل النسخة الاحتياطية
            backup_id = self._generate_backup_id()
            backup_record = BackupRecord(
                backup_id=backup_id,
                job_id=job_id,
                backup_type=backup_type,
                status=BackupStatus.RUNNING,
                start_time=datetime.now(),
                end_time=None,
                source_paths=job.source_paths,
                backup_path="",
                total_files=0,
                total_size=0,
                compressed_size=0,
                files_added=0,
                files_modified=0,
                files_deleted=0,
                encryption_enabled=job.encryption_enabled,
                checksum=""
            )

            self.current_backup = backup_record
            self.backup_records.append(backup_record)

            # تشغيل النسخ الاحتياطي في خيط منفصل
            backup_thread = threading.Thread(
                target=self._execute_backup,
                args=(job, backup_record),
                daemon=True
            )
            backup_thread.start()

            self.logger.info(f"تم بدء النسخ الاحتياطي: {job.name} ({backup_id})")
            return True, backup_id

        except Exception as e:
            error_msg = f"خطأ في تشغيل النسخ الاحتياطي: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def _execute_backup(self, job: BackupJob, backup_record: BackupRecord):
        """تنفيذ النسخ الاحتياطي"""
        try:
            start_time = time.time()

            # إنشاء مجلد النسخة الاحتياطية
            backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_folder = self.backups_dir / f"{job.job_id}_{backup_timestamp}"
            backup_folder.mkdir(parents=True, exist_ok=True)

            backup_record.backup_path = str(backup_folder)

            # جمع الملفات للنسخ الاحتياطي
            files_to_backup = self._collect_files_for_backup(job, backup_record.backup_type)
            backup_record.total_files = len(files_to_backup)

            # حساب الحجم الإجمالي
            total_size = sum(item.file_size for item in files_to_backup if not item.is_directory)
            backup_record.total_size = total_size

            # فحص حد الحجم
            max_size_bytes = self.config["max_backup_size_gb"] * 1024 * 1024 * 1024
            if total_size > max_size_bytes:
                raise Exception(f"حجم النسخة الاحتياطية ({total_size / (1024**3):.2f} GB) يتجاوز الحد المسموح")

            # إنشاء ملف النسخة الاحتياطية
            backup_file = backup_folder / f"backup_{backup_timestamp}.zip"

            # تنفيذ النسخ الاحتياطي
            self._create_backup_archive(files_to_backup, backup_file, job, backup_record)

            # التشفير إذا كان مطلوباً
            if job.encryption_enabled:
                encrypted_file = backup_folder / f"backup_{backup_timestamp}.enc"
                self._encrypt_backup_file(backup_file, encrypted_file, job.job_id)
                backup_file.unlink()  # حذف الملف غير المشفر
                backup_file = encrypted_file

            # حساب المجموع الاختباري
            backup_record.checksum = self._calculate_file_checksum(backup_file)
            backup_record.compressed_size = backup_file.stat().st_size

            # التحقق من النسخة الاحتياطية
            if self.config["verify_after_backup"]:
                if not self._verify_backup(backup_file, backup_record):
                    raise Exception("فشل في التحقق من النسخة الاحتياطية")

            # إنشاء معلومات الاستعادة
            if self.config["create_recovery_info"]:
                self._create_recovery_info(backup_folder, job, backup_record, files_to_backup)

            # تحديث سجل النسخة الاحتياطية
            backup_record.status = BackupStatus.COMPLETED
            backup_record.end_time = datetime.now()
            backup_record.duration_seconds = time.time() - start_time

            # تحديث مهمة النسخ الاحتياطي
            job.last_run = datetime.now()
            job.next_run = self._calculate_next_run(job.schedule_pattern)

            # حفظ البيانات
            self._save_backup_jobs()
            self._save_backup_records()

            self.logger.info(f"اكتمل النسخ الاحتياطي بنجاح: {job.name} ({backup_record.backup_id})")

        except Exception as e:
            # تحديث حالة الفشل
            backup_record.status = BackupStatus.FAILED
            backup_record.error_message = str(e)
            backup_record.end_time = datetime.now()
            backup_record.duration_seconds = time.time() - start_time

            self.logger.error(f"فشل النسخ الاحتياطي {backup_record.backup_id}: {str(e)}")

        finally:
            self.current_backup = None
            self._save_backup_records()

    def _collect_files_for_backup(self, job: BackupJob, backup_type: BackupType) -> List[BackupItem]:
        """جمع الملفات للنسخ الاحتياطي"""
        try:
            files_to_backup = []

            for source_path in job.source_paths:
                source_path = Path(source_path)

                if source_path.is_file():
                    # ملف واحد
                    if self._should_include_file(str(source_path), job):
                        backup_item = self._create_backup_item(source_path, source_path.parent)
                        files_to_backup.append(backup_item)

                elif source_path.is_dir():
                    # مجلد
                    for root, dirs, files in os.walk(source_path):
                        root_path = Path(root)

                        # فلترة المجلدات
                        dirs[:] = [d for d in dirs if self._should_include_file(str(root_path / d), job)]

                        # إضافة المجلد
                        if self._should_include_file(str(root_path), job):
                            backup_item = self._create_backup_item(root_path, source_path, is_directory=True)
                            files_to_backup.append(backup_item)

                        # إضافة الملفات
                        for file in files:
                            file_path = root_path / file
                            if self._should_include_file(str(file_path), job):
                                backup_item = self._create_backup_item(file_path, source_path)
                                files_to_backup.append(backup_item)

            # فلترة حسب نوع النسخة الاحتياطية
            if backup_type == BackupType.INCREMENTAL:
                files_to_backup = self._filter_incremental_files(files_to_backup, job.job_id)
            elif backup_type == BackupType.DIFFERENTIAL:
                files_to_backup = self._filter_differential_files(files_to_backup, job.job_id)

            return files_to_backup

        except Exception as e:
            self.logger.error(f"خطأ في جمع الملفات للنسخ الاحتياطي: {str(e)}")
            return []

    def _create_backup_item(self, file_path: Path, base_path: Path, is_directory: bool = False) -> BackupItem:
        """إنشاء عنصر نسخة احتياطية"""
        try:
            relative_path = str(file_path.relative_to(base_path))

            if is_directory:
                return BackupItem(
                    source_path=str(file_path),
                    relative_path=relative_path,
                    file_size=0,
                    file_hash="",
                    modified_time=datetime.fromtimestamp(file_path.stat().st_mtime),
                    is_directory=True,
                    permissions=oct(file_path.stat().st_mode)[-3:],
                    backup_time=datetime.now()
                )
            else:
                stat = file_path.stat()
                file_hash = self._calculate_file_checksum(file_path)

                return BackupItem(
                    source_path=str(file_path),
                    relative_path=relative_path,
                    file_size=stat.st_size,
                    file_hash=file_hash,
                    modified_time=datetime.fromtimestamp(stat.st_mtime),
                    is_directory=False,
                    permissions=oct(stat.st_mode)[-3:],
                    backup_time=datetime.now()
                )

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء عنصر النسخة الاحتياطية {file_path}: {str(e)}")
            return None

    def _should_include_file(self, file_path: str, job: BackupJob) -> bool:
        """فحص إذا كان يجب تضمين الملف في النسخة الاحتياطية"""
        try:
            file_path = file_path.lower()

            # فحص أنماط الاستبعاد
            for pattern in job.exclude_patterns:
                if pattern.lower() in file_path:
                    return False

            # فحص أنماط التضمين (إذا كانت محددة)
            if job.include_patterns:
                for pattern in job.include_patterns:
                    if pattern.lower() in file_path:
                        return True
                return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في فحص تضمين الملف {file_path}: {str(e)}")
            return False

    def _decrypt_backup_file(self, encrypted_file: Path, decrypted_file: Path, job_id: str):
        """فك تشفير ملف النسخة الاحتياطية"""
        try:
            if not CRYPTO_AVAILABLE:
                raise Exception("مكتبة التشفير غير متوفرة")

            # تحميل مفتاح التشفير للمهمة
            if job_id not in self.encryption_keys:
                self._load_job_encryption_key(job_id)

            encryption_key = self.encryption_keys[job_id]
            fernet = Fernet(encryption_key)

            with open(encrypted_file, 'rb') as infile, open(decrypted_file, 'wb') as outfile:
                # قراءة معلومات التشفير
                info_length = int.from_bytes(infile.read(4), byteorder='big')
                info_data = infile.read(info_length)
                encryption_info = json.loads(info_data.decode())

                # فك تشفير البيانات بقطع
                while True:
                    chunk_length_bytes = infile.read(4)
                    if len(chunk_length_bytes) < 4:
                        break

                    chunk_length = int.from_bytes(chunk_length_bytes, byteorder='big')
                    encrypted_chunk = infile.read(chunk_length)

                    if len(encrypted_chunk) < chunk_length:
                        break

                    decrypted_chunk = fernet.decrypt(encrypted_chunk)
                    outfile.write(decrypted_chunk)

            self.logger.info(f"تم فك تشفير النسخة الاحتياطية: {decrypted_file}")

        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير النسخة الاحتياطية: {str(e)}")
            raise

    def _verify_backup(self, backup_file: Path, backup_record: BackupRecord) -> bool:
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            # التحقق من وجود الملف
            if not backup_file.exists():
                return False

            # التحقق من المجموع الاختباري
            current_checksum = self._calculate_file_checksum(backup_file)
            if backup_record.checksum and current_checksum != backup_record.checksum:
                self.logger.error(f"المجموع الاختباري غير متطابق للنسخة الاحتياطية {backup_record.backup_id}")
                return False

            # التحقق من إمكانية قراءة الأرشيف (للملفات غير المشفرة)
            if backup_file.suffix == '.zip':
                try:
                    with zipfile.ZipFile(backup_file, 'r') as zipf:
                        # فحص سلامة الأرشيف
                        bad_files = zipf.testzip()
                        if bad_files:
                            self.logger.error(f"ملفات تالفة في الأرشيف: {bad_files}")
                            return False
                except zipfile.BadZipFile:
                    self.logger.error(f"ملف الأرشيف تالف: {backup_file}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من النسخة الاحتياطية: {str(e)}")
            return False

    def _calculate_file_checksum(self, file_path: Path) -> str:
        """حساب المجموع الاختباري للملف"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.logger.error(f"خطأ في حساب المجموع الاختباري للملف {file_path}: {str(e)}")
            return ""

    def _create_recovery_info(self, backup_folder: Path, job: BackupJob,
                             backup_record: BackupRecord, files_list: List[BackupItem]):
        """إنشاء معلومات الاستعادة"""
        try:
            recovery_info = {
                "backup_id": backup_record.backup_id,
                "job_id": backup_record.job_id,
                "job_name": job.name,
                "backup_type": backup_record.backup_type.value,
                "created_at": backup_record.start_time.isoformat(),
                "source_paths": backup_record.source_paths,
                "total_files": backup_record.total_files,
                "total_size": backup_record.total_size,
                "compressed_size": backup_record.compressed_size,
                "encryption_enabled": backup_record.encryption_enabled,
                "checksum": backup_record.checksum,
                "files_count": len(files_list),
                "recovery_instructions": {
                    "ar": "لاستعادة هذه النسخة الاحتياطية، استخدم معرف النسخة الاحتياطية مع نظام الاستعادة",
                    "en": "To restore this backup, use the backup ID with the restoration system"
                }
            }

            recovery_file = backup_folder / "recovery_info.json"
            with open(recovery_file, 'w', encoding='utf-8') as f:
                json.dump(recovery_info, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم إنشاء معلومات الاستعادة: {recovery_file}")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء معلومات الاستعادة: {str(e)}")

    def _filter_incremental_files(self, files_list: List[BackupItem], job_id: str) -> List[BackupItem]:
        """فلترة الملفات للنسخة الاحتياطية التزايدية"""
        try:
            # البحث عن آخر نسخة احتياطية مكتملة
            last_backup = None
            for record in reversed(self.backup_records):
                if (record.job_id == job_id and
                    record.status == BackupStatus.COMPLETED):
                    last_backup = record
                    break

            if not last_backup:
                # إذا لم توجد نسخة سابقة، إرجاع جميع الملفات
                return files_list

            # فلترة الملفات المعدلة منذ آخر نسخة احتياطية
            filtered_files = []
            for file_item in files_list:
                if file_item.modified_time > last_backup.start_time:
                    filtered_files.append(file_item)

            return filtered_files

        except Exception as e:
            self.logger.error(f"خطأ في فلترة الملفات التزايدية: {str(e)}")
            return files_list

    def _filter_differential_files(self, files_list: List[BackupItem], job_id: str) -> List[BackupItem]:
        """فلترة الملفات للنسخة الاحتياطية التفاضلية"""
        try:
            # البحث عن آخر نسخة احتياطية كاملة
            last_full_backup = None
            for record in reversed(self.backup_records):
                if (record.job_id == job_id and
                    record.backup_type == BackupType.FULL and
                    record.status == BackupStatus.COMPLETED):
                    last_full_backup = record
                    break

            if not last_full_backup:
                # إذا لم توجد نسخة كاملة سابقة، إرجاع جميع الملفات
                return files_list

            # فلترة الملفات المعدلة منذ آخر نسخة كاملة
            filtered_files = []
            for file_item in files_list:
                if file_item.modified_time > last_full_backup.start_time:
                    filtered_files.append(file_item)

            return filtered_files

        except Exception as e:
            self.logger.error(f"خطأ في فلترة الملفات التفاضلية: {str(e)}")
            return files_list

    def _is_file_new(self, backup_item: BackupItem, job_id: str) -> bool:
        """فحص إذا كان الملف جديد"""
        try:
            # البحث في النسخ الاحتياطية السابقة
            for record in reversed(self.backup_records):
                if record.job_id == job_id and record.status == BackupStatus.COMPLETED:
                    # فحص إذا كان الملف موجود في النسخة السابقة
                    # هذا يتطلب تحميل قائمة الملفات من النسخة السابقة
                    # للبساطة، سنعتبر الملف جديد إذا كان تاريخ التعديل حديث
                    if backup_item.modified_time > record.start_time:
                        return True
                    break

            return False

        except Exception as e:
            self.logger.error(f"خطأ في فحص الملف الجديد: {str(e)}")
            return True

    def start_scheduler(self):
        """بدء مجدول النسخ الاحتياطي"""
        try:
            if self.is_running:
                return

            self.is_running = True
            self.stop_scheduler.clear()

            # بدء خيط المجدول
            self.scheduler_thread = threading.Thread(
                target=self._scheduler_loop,
                daemon=True,
                name="BackupScheduler"
            )
            self.scheduler_thread.start()

            self.logger.info("تم بدء مجدول النسخ الاحتياطي")

        except Exception as e:
            self.logger.error(f"خطأ في بدء المجدول: {str(e)}")

    def stop_scheduler(self):
        """إيقاف مجدول النسخ الاحتياطي"""
        try:
            self.is_running = False
            self.stop_scheduler.set()

            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=10)

            self.logger.info("تم إيقاف مجدول النسخ الاحتياطي")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المجدول: {str(e)}")

    def _scheduler_loop(self):
        """حلقة المجدول"""
        try:
            while not self.stop_scheduler.is_set():
                try:
                    current_time = datetime.now()

                    # فحص المهام المجدولة
                    for job_id, job in self.backup_jobs.items():
                        if (job.is_active and
                            job.next_run and
                            current_time >= job.next_run):

                            # تشغيل النسخ الاحتياطي
                            self.logger.info(f"تشغيل النسخ الاحتياطي المجدول: {job.name}")
                            success, result = self.run_backup_job(job_id)

                            if not success:
                                self.logger.error(f"فشل النسخ الاحتياطي المجدول {job.name}: {result}")

                    # تنظيف النسخ الاحتياطية القديمة
                    if self.config["auto_cleanup"]:
                        self._cleanup_old_backups()

                    # انتظار دقيقة واحدة قبل الفحص التالي
                    self.stop_scheduler.wait(60)

                except Exception as e:
                    self.logger.error(f"خطأ في حلقة المجدول: {str(e)}")
                    self.stop_scheduler.wait(300)  # انتظار 5 دقائق عند الخطأ

        except Exception as e:
            self.logger.error(f"خطأ في خيط المجدول: {str(e)}")

    def _calculate_next_run(self, schedule_pattern: str) -> datetime:
        """حساب موعد التشغيل التالي"""
        try:
            current_time = datetime.now()

            if schedule_pattern == "daily":
                return current_time + timedelta(days=1)
            elif schedule_pattern == "weekly":
                return current_time + timedelta(weeks=1)
            elif schedule_pattern == "monthly":
                return current_time + timedelta(days=30)
            elif schedule_pattern.startswith("every_"):
                # مثال: every_6_hours
                parts = schedule_pattern.split("_")
                if len(parts) == 3:
                    interval = int(parts[1])
                    unit = parts[2]

                    if unit == "hours":
                        return current_time + timedelta(hours=interval)
                    elif unit == "days":
                        return current_time + timedelta(days=interval)
                    elif unit == "minutes":
                        return current_time + timedelta(minutes=interval)

            # افتراضي: يومياً
            return current_time + timedelta(days=1)

        except Exception as e:
            self.logger.error(f"خطأ في حساب موعد التشغيل التالي: {str(e)}")
            return datetime.now() + timedelta(days=1)

    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            current_time = datetime.now()

            for job_id, job in self.backup_jobs.items():
                cutoff_date = current_time - timedelta(days=job.retention_days)

                # البحث عن النسخ الاحتياطية القديمة
                old_backups = [
                    record for record in self.backup_records
                    if (record.job_id == job_id and
                        record.start_time < cutoff_date and
                        record.status == BackupStatus.COMPLETED)
                ]

                # حذف النسخ الاحتياطية القديمة
                for backup_record in old_backups:
                    try:
                        backup_folder = Path(backup_record.backup_path)
                        if backup_folder.exists():
                            shutil.rmtree(backup_folder)

                        # تحديث حالة السجل
                        backup_record.status = BackupStatus.CANCELLED

                        self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup_record.backup_id}")

                    except Exception as e:
                        self.logger.error(f"خطأ في حذف النسخة الاحتياطية القديمة {backup_record.backup_id}: {str(e)}")

            # حفظ التحديثات
            self._save_backup_records()

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    def get_backup_jobs(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة مهام النسخ الاحتياطي"""
        try:
            jobs_list = []
            for job in self.backup_jobs.values():
                job_dict = asdict(job)
                # تحويل التواريخ والـ enums
                job_dict['backup_type'] = job.backup_type.value
                job_dict['compression_level'] = job.compression_level.value
                job_dict['created_at'] = job.created_at.isoformat()
                if job.last_run:
                    job_dict['last_run'] = job.last_run.isoformat()
                if job.next_run:
                    job_dict['next_run'] = job.next_run.isoformat()

                jobs_list.append(job_dict)

            return jobs_list

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة المهام: {str(e)}")
            return []

    def get_backup_records(self, job_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على سجلات النسخ الاحتياطي"""
        try:
            records = self.backup_records

            # فلترة حسب معرف المهمة
            if job_id:
                records = [r for r in records if r.job_id == job_id]

            # ترتيب حسب التاريخ (الأحدث أولاً)
            records = sorted(records, key=lambda x: x.start_time, reverse=True)

            # تحديد العدد
            records = records[:limit]

            # تحويل إلى قاموس
            records_list = []
            for record in records:
                record_dict = asdict(record)
                # تحويل التواريخ والـ enums
                record_dict['backup_type'] = record.backup_type.value
                record_dict['status'] = record.status.value
                record_dict['start_time'] = record.start_time.isoformat()
                if record.end_time:
                    record_dict['end_time'] = record.end_time.isoformat()

                records_list.append(record_dict)

            return records_list

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على سجلات النسخ الاحتياطي: {str(e)}")
            return []

    def get_backup_statistics(self, days: int = 30) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطي"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # فلترة السجلات حسب التاريخ
            recent_records = [
                record for record in self.backup_records
                if record.start_time >= cutoff_date
            ]

            # حساب الإحصائيات
            stats = {
                "period_days": days,
                "total_jobs": len(self.backup_jobs),
                "active_jobs": len([j for j in self.backup_jobs.values() if j.is_active]),
                "total_backups": len(recent_records),
                "successful_backups": len([r for r in recent_records if r.status == BackupStatus.COMPLETED]),
                "failed_backups": len([r for r in recent_records if r.status == BackupStatus.FAILED]),
                "total_size_gb": sum(r.total_size for r in recent_records if r.status == BackupStatus.COMPLETED) / (1024**3),
                "compressed_size_gb": sum(r.compressed_size for r in recent_records if r.status == BackupStatus.COMPLETED) / (1024**3),
                "total_files": sum(r.total_files for r in recent_records if r.status == BackupStatus.COMPLETED),
                "average_duration_minutes": 0,
                "compression_ratio": 0,
                "backup_types": {},
                "scheduler_status": self.is_running,
                "current_backup": self.current_backup.backup_id if self.current_backup else None
            }

            # حساب متوسط المدة
            completed_records = [r for r in recent_records if r.status == BackupStatus.COMPLETED and r.duration_seconds > 0]
            if completed_records:
                avg_duration = sum(r.duration_seconds for r in completed_records) / len(completed_records)
                stats["average_duration_minutes"] = round(avg_duration / 60, 2)

            # حساب نسبة الضغط
            if stats["total_size_gb"] > 0:
                stats["compression_ratio"] = round((1 - stats["compressed_size_gb"] / stats["total_size_gb"]) * 100, 2)

            # إحصائيات أنواع النسخ الاحتياطي
            for record in recent_records:
                backup_type = record.backup_type.value
                stats["backup_types"][backup_type] = stats["backup_types"].get(backup_type, 0) + 1

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات النسخ الاحتياطي: {str(e)}")
            return {}

    def _generate_job_id(self) -> str:
        """توليد معرف فريد للمهمة"""
        return f"JOB_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.backup_jobs):04d}"

    def _generate_backup_id(self) -> str:
        """توليد معرف فريد للنسخة الاحتياطية"""
        return f"BAK_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.backup_records):04d}"

    def _save_backup_jobs(self):
        """حفظ مهام النسخ الاحتياطي"""
        try:
            jobs_data = {}
            for job_id, job in self.backup_jobs.items():
                job_dict = asdict(job)
                # تحويل التواريخ والـ enums إلى نص
                job_dict['backup_type'] = job.backup_type.value
                job_dict['compression_level'] = job.compression_level.value
                job_dict['created_at'] = job.created_at.isoformat()
                if job.last_run:
                    job_dict['last_run'] = job.last_run.isoformat()
                if job.next_run:
                    job_dict['next_run'] = job.next_run.isoformat()

                jobs_data[job_id] = job_dict

            with open(self.jobs_file, 'w', encoding='utf-8') as f:
                json.dump(jobs_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ مهام النسخ الاحتياطي: {str(e)}")

    def _load_backup_jobs(self):
        """تحميل مهام النسخ الاحتياطي"""
        try:
            if not self.jobs_file.exists():
                return

            with open(self.jobs_file, 'r', encoding='utf-8') as f:
                jobs_data = json.load(f)

            for job_id, job_dict in jobs_data.items():
                # تحويل النصوص إلى التواريخ والـ enums
                job_dict['backup_type'] = BackupType(job_dict['backup_type'])
                job_dict['compression_level'] = CompressionLevel(job_dict['compression_level'])
                job_dict['created_at'] = datetime.fromisoformat(job_dict['created_at'])

                if job_dict.get('last_run'):
                    job_dict['last_run'] = datetime.fromisoformat(job_dict['last_run'])
                else:
                    job_dict['last_run'] = None

                if job_dict.get('next_run'):
                    job_dict['next_run'] = datetime.fromisoformat(job_dict['next_run'])
                else:
                    job_dict['next_run'] = None

                # إنشاء كائن المهمة
                backup_job = BackupJob(**job_dict)
                self.backup_jobs[job_id] = backup_job

        except Exception as e:
            self.logger.error(f"خطأ في تحميل مهام النسخ الاحتياطي: {str(e)}")

    def _save_backup_records(self):
        """حفظ سجلات النسخ الاحتياطي"""
        try:
            records_data = []
            for record in self.backup_records:
                record_dict = asdict(record)
                # تحويل التواريخ والـ enums إلى نص
                record_dict['backup_type'] = record.backup_type.value
                record_dict['status'] = record.status.value
                record_dict['start_time'] = record.start_time.isoformat()
                if record.end_time:
                    record_dict['end_time'] = record.end_time.isoformat()
                else:
                    record_dict['end_time'] = None

                records_data.append(record_dict)

            with open(self.records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ سجلات النسخ الاحتياطي: {str(e)}")

    def _load_backup_records(self):
        """تحميل سجلات النسخ الاحتياطي"""
        try:
            if not self.records_file.exists():
                return

            with open(self.records_file, 'r', encoding='utf-8') as f:
                records_data = json.load(f)

            for record_dict in records_data:
                # تحويل النصوص إلى التواريخ والـ enums
                record_dict['backup_type'] = BackupType(record_dict['backup_type'])
                record_dict['status'] = BackupStatus(record_dict['status'])
                record_dict['start_time'] = datetime.fromisoformat(record_dict['start_time'])

                if record_dict.get('end_time'):
                    record_dict['end_time'] = datetime.fromisoformat(record_dict['end_time'])
                else:
                    record_dict['end_time'] = None

                # إنشاء كائن السجل
                backup_record = BackupRecord(**record_dict)
                self.backup_records.append(backup_record)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل سجلات النسخ الاحتياطي: {str(e)}")

    def delete_backup_job(self, job_id: str) -> Tuple[bool, str]:
        """حذف مهمة النسخ الاحتياطي"""
        try:
            if job_id not in self.backup_jobs:
                return False, "مهمة النسخ الاحتياطي غير موجودة"

            job = self.backup_jobs[job_id]

            # حذف جميع النسخ الاحتياطية المرتبطة بالمهمة
            job_backups = [r for r in self.backup_records if r.job_id == job_id]
            for backup_record in job_backups:
                try:
                    backup_folder = Path(backup_record.backup_path)
                    if backup_folder.exists():
                        shutil.rmtree(backup_folder)
                except Exception as e:
                    self.logger.warning(f"تعذر حذف مجلد النسخة الاحتياطية {backup_record.backup_path}: {str(e)}")

            # حذف سجلات النسخ الاحتياطية
            self.backup_records = [r for r in self.backup_records if r.job_id != job_id]

            # حذف مفتاح التشفير
            key_file = self.keys_dir / f"{job_id}.key"
            if key_file.exists():
                key_file.unlink()

            if job_id in self.encryption_keys:
                del self.encryption_keys[job_id]

            # حذف المهمة
            del self.backup_jobs[job_id]

            # حفظ التحديثات
            self._save_backup_jobs()
            self._save_backup_records()

            self.logger.info(f"تم حذف مهمة النسخ الاحتياطي: {job.name} ({job_id})")
            return True, "تم حذف المهمة بنجاح"

        except Exception as e:
            error_msg = f"خطأ في حذف مهمة النسخ الاحتياطي: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def delete_backup(self, backup_id: str) -> Tuple[bool, str]:
        """حذف نسخة احتياطية محددة"""
        try:
            # البحث عن سجل النسخة الاحتياطية
            backup_record = None
            for record in self.backup_records:
                if record.backup_id == backup_id:
                    backup_record = record
                    break

            if not backup_record:
                return False, "سجل النسخة الاحتياطية غير موجود"

            # حذف مجلد النسخة الاحتياطية
            backup_folder = Path(backup_record.backup_path)
            if backup_folder.exists():
                shutil.rmtree(backup_folder)

            # حذف السجل
            self.backup_records.remove(backup_record)

            # حفظ التحديثات
            self._save_backup_records()

            self.logger.info(f"تم حذف النسخة الاحتياطية: {backup_id}")
            return True, "تم حذف النسخة الاحتياطية بنجاح"

        except Exception as e:
            error_msg = f"خطأ في حذف النسخة الاحتياطية: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def update_backup_job(self, job_id: str, **kwargs) -> Tuple[bool, str]:
        """تحديث مهمة النسخ الاحتياطي"""
        try:
            if job_id not in self.backup_jobs:
                return False, "مهمة النسخ الاحتياطي غير موجودة"

            job = self.backup_jobs[job_id]

            # تحديث الخصائص المسموحة
            allowed_fields = ['name', 'description', 'source_paths', 'destination_path',
                            'schedule_pattern', 'encryption_enabled', 'compression_level',
                            'include_patterns', 'exclude_patterns', 'retention_days', 'is_active']

            for field, value in kwargs.items():
                if field in allowed_fields:
                    if field == 'compression_level' and isinstance(value, str):
                        value = CompressionLevel(value)
                    setattr(job, field, value)

            # إعادة حساب موعد التشغيل التالي إذا تم تغيير الجدولة
            if 'schedule_pattern' in kwargs:
                job.next_run = self._calculate_next_run(job.schedule_pattern)

            # حفظ التحديثات
            self._save_backup_jobs()

            self.logger.info(f"تم تحديث مهمة النسخ الاحتياطي: {job.name} ({job_id})")
            return True, "تم تحديث المهمة بنجاح"

        except Exception as e:
            error_msg = f"خطأ في تحديث مهمة النسخ الاحتياطي: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            return {
                "system_running": self.is_running,
                "scheduler_active": self.scheduler_thread and self.scheduler_thread.is_alive(),
                "current_backup": self.current_backup.backup_id if self.current_backup else None,
                "total_jobs": len(self.backup_jobs),
                "active_jobs": len([j for j in self.backup_jobs.values() if j.is_active]),
                "total_backups": len(self.backup_records),
                "encryption_enabled": self.config["enable_encryption"],
                "compression_enabled": self.config["enable_compression"],
                "auto_cleanup_enabled": self.config["auto_cleanup"],
                "backup_directory": str(self.backup_dir),
                "disk_usage_gb": self._get_disk_usage() / (1024**3),
                "next_scheduled_backup": self._get_next_scheduled_backup()
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حالة النظام: {str(e)}")
            return {}

    def _get_disk_usage(self) -> int:
        """حساب استخدام القرص للنسخ الاحتياطية"""
        try:
            total_size = 0
            for backup_record in self.backup_records:
                if backup_record.status == BackupStatus.COMPLETED:
                    backup_folder = Path(backup_record.backup_path)
                    if backup_folder.exists():
                        for file_path in backup_folder.rglob('*'):
                            if file_path.is_file():
                                total_size += file_path.stat().st_size
            return total_size
        except Exception as e:
            self.logger.error(f"خطأ في حساب استخدام القرص: {str(e)}")
            return 0

    def _get_next_scheduled_backup(self) -> Optional[str]:
        """الحصول على موعد النسخة الاحتياطية التالية"""
        try:
            next_backup = None
            next_time = None

            for job in self.backup_jobs.values():
                if job.is_active and job.next_run:
                    if next_time is None or job.next_run < next_time:
                        next_time = job.next_run
                        next_backup = f"{job.name} في {job.next_run.strftime('%Y-%m-%d %H:%M:%S')}"

            return next_backup

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على موعد النسخة التالية: {str(e)}")
            return None

    def shutdown(self):
        """إغلاق النظام"""
        try:
            self.logger.info("بدء إغلاق نظام النسخ الاحتياطي الآمن...")

            # إيقاف المجدول
            self.stop_scheduler()

            # حفظ جميع البيانات
            self._save_backup_jobs()
            self._save_backup_records()
            self._save_config()

            # تنظيف الملفات المؤقتة
            if self.temp_dir.exists():
                for temp_file in self.temp_dir.glob('*'):
                    try:
                        if temp_file.is_file():
                            temp_file.unlink()
                        elif temp_file.is_dir():
                            shutil.rmtree(temp_file)
                    except Exception as e:
                        self.logger.warning(f"تعذر حذف الملف المؤقت {temp_file}: {str(e)}")

            self.logger.info("تم إغلاق نظام النسخ الاحتياطي الآمن بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق النظام: {str(e)}")


# دوال مساعدة للاستخدام المباشر
def create_secure_backup_system(base_dir: str = "data") -> SecureBackupSystem:
    """إنشاء نظام النسخ الاحتياطي الآمن"""
    return SecureBackupSystem(base_dir)


def backup_files(source_paths: List[str], destination_path: str,
                encryption_password: str = None) -> Tuple[bool, str]:
    """نسخ احتياطي سريع للملفات"""
    try:
        backup_system = SecureBackupSystem()

        # إنشاء مهمة نسخ احتياطي مؤقتة
        success, job_id = backup_system.create_backup_job(
            name="نسخة احتياطية سريعة",
            description="نسخة احتياطية سريعة للملفات المحددة",
            source_paths=source_paths,
            destination_path=destination_path,
            backup_type=BackupType.FULL,
            encryption_enabled=encryption_password is not None
        )

        if not success:
            return False, job_id

        # تشغيل النسخ الاحتياطي
        success, backup_id = backup_system.run_backup_job(job_id)

        # حذف المهمة المؤقتة
        backup_system.delete_backup_job(job_id)

        return success, backup_id

    except Exception as e:
        return False, f"خطأ في النسخ الاحتياطي السريع: {str(e)}"


if __name__ == "__main__":
    # مثال على الاستخدام
    backup_system = SecureBackupSystem()

    # إنشاء مهمة نسخ احتياطي
    success, job_id = backup_system.create_backup_job(
        name="نسخة احتياطية يومية",
        description="نسخة احتياطية يومية للمستندات المهمة",
        source_paths=["./documents", "./projects"],
        destination_path="./backups",
        schedule_pattern="daily"
    )

    if success:
        print(f"تم إنشاء مهمة النسخ الاحتياطي: {job_id}")

        # تشغيل النسخ الاحتياطي فوراً
        success, backup_id = backup_system.run_backup_job(job_id)
        if success:
            print(f"تم بدء النسخ الاحتياطي: {backup_id}")
        else:
            print(f"فشل في بدء النسخ الاحتياطي: {backup_id}")
    else:
        print(f"فشل في إنشاء مهمة النسخ الاحتياطي: {job_id}")

    # عرض الإحصائيات
    stats = backup_system.get_backup_statistics()
    print(f"إحصائيات النسخ الاحتياطي: {stats}")

    # إغلاق النظام
    backup_system.shutdown()

    def _create_backup_archive(self, files_to_backup: List[BackupItem], backup_file: Path,
                              job: BackupJob, backup_record: BackupRecord):
        """إنشاء أرشيف النسخة الاحتياطية"""
        try:
            compression_level = job.compression_level.value if self.config["enable_compression"] else 0

            with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED, compresslevel=compression_level) as zipf:
                files_added = 0

                for backup_item in files_to_backup:
                    try:
                        source_path = Path(backup_item.source_path)

                        if backup_item.is_directory:
                            # إضافة مجلد فارغ
                            zipf.writestr(backup_item.relative_path + '/', '')
                        else:
                            # إضافة ملف
                            zipf.write(source_path, backup_item.relative_path)
                            files_added += 1

                            # تحديث الإحصائيات
                            if backup_record.backup_type == BackupType.FULL:
                                backup_record.files_added += 1
                            else:
                                # تحديد إذا كان الملف جديد أم معدل
                                if self._is_file_new(backup_item, job.job_id):
                                    backup_record.files_added += 1
                                else:
                                    backup_record.files_modified += 1

                    except Exception as e:
                        self.logger.warning(f"تعذر إضافة الملف {backup_item.source_path}: {str(e)}")
                        continue

                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    "backup_id": backup_record.backup_id,
                    "job_id": backup_record.job_id,
                    "backup_type": backup_record.backup_type.value,
                    "created_at": backup_record.start_time.isoformat(),
                    "total_files": len(files_to_backup),
                    "files_added": files_added,
                    "source_paths": backup_record.source_paths,
                    "encryption_enabled": backup_record.encryption_enabled
                }

                zipf.writestr('backup_info.json', json.dumps(backup_info, ensure_ascii=False, indent=2))

                # إضافة قائمة الملفات
                files_list = []
                for item in files_to_backup:
                    files_list.append(asdict(item))

                zipf.writestr('files_list.json', json.dumps(files_list, ensure_ascii=False, indent=2, default=str))

            self.logger.info(f"تم إنشاء أرشيف النسخة الاحتياطية: {backup_file}")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء أرشيف النسخة الاحتياطية: {str(e)}")
            raise

    def _encrypt_backup_file(self, source_file: Path, encrypted_file: Path, job_id: str):
        """تشفير ملف النسخة الاحتياطية"""
        try:
            if not CRYPTO_AVAILABLE:
                raise Exception("مكتبة التشفير غير متوفرة")

            # الحصول على مفتاح التشفير للمهمة
            encryption_key = self._get_job_encryption_key(job_id)
            fernet = Fernet(encryption_key)

            # قراءة وتشفير الملف
            with open(source_file, 'rb') as infile, open(encrypted_file, 'wb') as outfile:
                # كتابة معلومات التشفير
                encryption_info = {
                    "algorithm": "Fernet",
                    "job_id": job_id,
                    "encrypted_at": datetime.now().isoformat()
                }

                info_data = json.dumps(encryption_info).encode()
                info_length = len(info_data)

                # كتابة طول معلومات التشفير ثم المعلومات
                outfile.write(info_length.to_bytes(4, byteorder='big'))
                outfile.write(info_data)

                # تشفير وكتابة البيانات بقطع
                chunk_size = self.config["chunk_size_mb"] * 1024 * 1024
                while True:
                    chunk = infile.read(chunk_size)
                    if not chunk:
                        break

                    encrypted_chunk = fernet.encrypt(chunk)
                    chunk_length = len(encrypted_chunk)

                    # كتابة طول القطعة ثم القطعة المشفرة
                    outfile.write(chunk_length.to_bytes(4, byteorder='big'))
                    outfile.write(encrypted_chunk)

            self.logger.info(f"تم تشفير النسخة الاحتياطية: {encrypted_file}")

        except Exception as e:
            self.logger.error(f"خطأ في تشفير النسخة الاحتياطية: {str(e)}")
            raise

    def _get_job_encryption_key(self, job_id: str) -> bytes:
        """الحصول على مفتاح التشفير للمهمة"""
        try:
            if job_id not in self.encryption_keys:
                # إنشاء مفتاح جديد للمهمة
                job_key = Fernet.generate_key()
                self.encryption_keys[job_id] = job_key

                # حفظ المفتاح مشفراً بالمفتاح الرئيسي
                master_fernet = Fernet(self.master_key)
                encrypted_key = master_fernet.encrypt(job_key)

                key_file = self.keys_dir / f"{job_id}.key"
                with open(key_file, 'wb') as f:
                    f.write(encrypted_key)

                os.chmod(key_file, 0o600)

            return self.encryption_keys[job_id]

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على مفتاح التشفير للمهمة {job_id}: {str(e)}")
            raise

    def _load_job_encryption_key(self, job_id: str) -> bytes:
        """تحميل مفتاح التشفير للمهمة"""
        try:
            key_file = self.keys_dir / f"{job_id}.key"
            if not key_file.exists():
                raise Exception(f"مفتاح التشفير للمهمة {job_id} غير موجود")

            # قراءة وفك تشفير المفتاح
            with open(key_file, 'rb') as f:
                encrypted_key = f.read()

            master_fernet = Fernet(self.master_key)
            job_key = master_fernet.decrypt(encrypted_key)

            self.encryption_keys[job_id] = job_key
            return job_key

        except Exception as e:
            self.logger.error(f"خطأ في تحميل مفتاح التشفير للمهمة {job_id}: {str(e)}")
            raise

    def restore_backup(self, backup_id: str, restore_path: str,
                      selective_files: List[str] = None) -> Tuple[bool, str]:
        """استعادة النسخة الاحتياطية"""
        try:
            # البحث عن سجل النسخة الاحتياطية
            backup_record = None
            for record in self.backup_records:
                if record.backup_id == backup_id:
                    backup_record = record
                    break

            if not backup_record:
                return False, "سجل النسخة الاحتياطية غير موجود"

            if backup_record.status != BackupStatus.COMPLETED:
                return False, "النسخة الاحتياطية غير مكتملة"

            # العثور على ملف النسخة الاحتياطية
            backup_folder = Path(backup_record.backup_path)
            if not backup_folder.exists():
                return False, "مجلد النسخة الاحتياطية غير موجود"

            # البحث عن ملف النسخة الاحتياطية
            backup_file = None
            for file in backup_folder.glob("backup_*.zip"):
                backup_file = file
                break

            if not backup_file:
                for file in backup_folder.glob("backup_*.enc"):
                    backup_file = file
                    break

            if not backup_file:
                return False, "ملف النسخة الاحتياطية غير موجود"

            # فك التشفير إذا كان مطلوباً
            if backup_file.suffix == '.enc':
                decrypted_file = self.temp_dir / f"temp_{backup_id}.zip"
                self._decrypt_backup_file(backup_file, decrypted_file, backup_record.job_id)
                backup_file = decrypted_file

            # التحقق من النسخة الاحتياطية
            if not self._verify_backup(backup_file, backup_record):
                return False, "فشل في التحقق من النسخة الاحتياطية"

            # استعادة الملفات
            restore_path = Path(restore_path)
            restore_path.mkdir(parents=True, exist_ok=True)

            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # قراءة قائمة الملفات
                files_list = []
                if 'files_list.json' in zipf.namelist():
                    files_data = zipf.read('files_list.json').decode('utf-8')
                    files_list = json.loads(files_data)

                # استعادة الملفات المحددة أو جميع الملفات
                files_to_restore = zipf.namelist()
                if selective_files:
                    files_to_restore = [f for f in files_to_restore if any(sf in f for sf in selective_files)]

                # استبعاد ملفات النظام
                files_to_restore = [f for f in files_to_restore if not f.startswith('backup_info.json') and not f.startswith('files_list.json')]

                # استعادة الملفات
                for file_name in files_to_restore:
                    try:
                        zipf.extract(file_name, restore_path)

                        # استعادة الصلاحيات إذا كانت متوفرة
                        file_info = next((f for f in files_list if f['relative_path'] == file_name), None)
                        if file_info and file_info.get('permissions'):
                            restored_file = restore_path / file_name
                            if restored_file.exists():
                                os.chmod(restored_file, int(file_info['permissions'], 8))

                    except Exception as e:
                        self.logger.warning(f"تعذر استعادة الملف {file_name}: {str(e)}")
                        continue

            # تنظيف الملف المؤقت
            if backup_file.parent == self.temp_dir:
                backup_file.unlink()

            self.logger.info(f"تم استعادة النسخة الاحتياطية {backup_id} إلى {restore_path}")
            return True, f"تم استعادة النسخة الاحتياطية إلى {restore_path}"

        except Exception as e:
            error_msg = f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
