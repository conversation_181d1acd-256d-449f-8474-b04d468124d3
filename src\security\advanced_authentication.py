#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المصادقة المتقدم - Advanced Authentication System
نظام شامل للمصادقة متعددة العوامل وإدارة الجلسات الآمنة
"""

import os
import json
import hashlib
import hmac
import secrets
import time
import logging
import base64
import threading
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

# مكتبات اختيارية للمصادقة الثنائية
try:
    import pyotp
    import qrcode
    TOTP_AVAILABLE = True
except ImportError:
    TOTP_AVAILABLE = False

# مكتبات التشفير
try:
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.backends import default_backend
    from cryptography.fernet import Fernet
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("تحذير: مكتبة التشفير غير متوفرة. سيتم استخدام التشفير الأساسي.")

class AuthenticationMethod(Enum):
    """طرق المصادقة"""
    PASSWORD = "password"           # كلمة المرور
    TOTP = "totp"                  # رمز زمني (Google Authenticator)
    SMS = "sms"                    # رسالة نصية
    EMAIL = "email"                # بريد إلكتروني
    BIOMETRIC = "biometric"        # بصمة/وجه
    HARDWARE_TOKEN = "hardware"    # رمز أجهزة
    BACKUP_CODES = "backup"        # رموز احتياطية

class SessionStatus(Enum):
    """حالة الجلسة"""
    ACTIVE = "active"              # نشطة
    EXPIRED = "expired"            # منتهية الصلاحية
    TERMINATED = "terminated"      # منهية
    SUSPENDED = "suspended"        # معلقة
    LOCKED = "locked"              # مقفلة

class SecurityLevel(Enum):
    """مستوى الأمان"""
    LOW = "low"                    # منخفض - كلمة مرور فقط
    MEDIUM = "medium"              # متوسط - كلمة مرور + عامل واحد
    HIGH = "high"                  # عالي - كلمة مرور + عاملين
    CRITICAL = "critical"          # حرج - جميع العوامل المتاحة

@dataclass
class User:
    """معلومات المستخدم"""
    user_id: str
    username: str
    email: str
    phone: Optional[str] = None
    password_hash: str = ""
    salt: str = ""
    totp_secret: Optional[str] = None
    backup_codes: List[str] = None
    security_level: SecurityLevel = SecurityLevel.MEDIUM
    enabled_methods: Set[AuthenticationMethod] = None
    failed_attempts: int = 0
    locked_until: Optional[datetime] = None
    last_login: Optional[datetime] = None
    created_at: datetime = None
    updated_at: datetime = None
    is_active: bool = True
    
    def __post_init__(self):
        if self.backup_codes is None:
            self.backup_codes = []
        if self.enabled_methods is None:
            self.enabled_methods = {AuthenticationMethod.PASSWORD}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()

@dataclass
class Session:
    """جلسة المستخدم"""
    session_id: str
    user_id: str
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    status: SessionStatus = SessionStatus.ACTIVE
    security_level: SecurityLevel = SecurityLevel.MEDIUM
    authenticated_methods: Set[AuthenticationMethod] = None
    device_fingerprint: Optional[str] = None
    location: Optional[str] = None
    
    def __post_init__(self):
        if self.authenticated_methods is None:
            self.authenticated_methods = set()

@dataclass
class AuthenticationAttempt:
    """محاولة مصادقة"""
    attempt_id: str
    user_id: str
    method: AuthenticationMethod
    success: bool
    timestamp: datetime
    ip_address: str
    user_agent: str
    failure_reason: Optional[str] = None
    additional_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_data is None:
            self.additional_data = {}

class AdvancedAuthenticationSystem:
    """نظام المصادقة المتقدم"""
    
    def __init__(self, base_dir: str = "data"):
        """تهيئة نظام المصادقة المتقدم"""
        self.base_dir = Path(base_dir)
        self.auth_dir = self.base_dir / "authentication"
        self.users_dir = self.auth_dir / "users"
        self.sessions_dir = self.auth_dir / "sessions"
        self.logs_dir = self.auth_dir / "logs"
        
        # إنشاء المجلدات
        for directory in [self.auth_dir, self.users_dir, self.sessions_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # ملفات النظام
        self.users_db_file = self.users_dir / "users_database.json"
        self.sessions_db_file = self.sessions_dir / "sessions_database.json"
        self.config_file = self.auth_dir / "auth_config.json"
        self.attempts_log_file = self.logs_dir / "auth_attempts.json"
        
        # قواعد البيانات
        self.users_database: Dict[str, User] = {}
        self.sessions_database: Dict[str, Session] = {}
        self.authentication_attempts: List[AuthenticationAttempt] = []
        
        # إعدادات النظام
        self.config = {
            "password_min_length": 8,
            "password_require_uppercase": True,
            "password_require_lowercase": True,
            "password_require_numbers": True,
            "password_require_symbols": True,
            "max_failed_attempts": 5,
            "lockout_duration_minutes": 30,
            "session_timeout_minutes": 60,
            "totp_window": 1,
            "backup_codes_count": 10,
            "password_history_count": 5,
            "force_password_change_days": 90,
            "session_cleanup_interval": 3600,  # ساعة واحدة
            "enable_device_fingerprinting": True,
            "enable_location_tracking": False,
            "require_mfa_for_admin": True
        }
        
        # مفتاح التشفير الرئيسي
        self.master_key = None
        self.encryption_key = None
        
        # إعداد نظام السجلات
        self.logger = logging.getLogger("AdvancedAuthentication")
        self.logger.setLevel(logging.INFO)
        
        # إعداد معالج السجلات
        log_file = self.logs_dir / "authentication.log"
        handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # فحص المتطلبات
        if not CRYPTO_AVAILABLE:
            raise ImportError("مكتبة التشفير (cryptography) مطلوبة لتشغيل النظام")

        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            # تحميل الإعدادات
            self._load_config()
            
            # إعداد التشفير
            self._setup_encryption()
            
            # تحميل قواعد البيانات
            self._load_users_database()
            self._load_sessions_database()
            self._load_authentication_attempts()
            
            # بدء تنظيف الجلسات التلقائي
            self._start_session_cleanup()
            
            self.logger.info("تم تهيئة نظام المصادقة المتقدم بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة نظام المصادقة المتقدم: {str(e)}")
            raise
    
    def _setup_encryption(self):
        """إعداد التشفير"""
        try:
            master_key_file = self.auth_dir / "master_key.key"
            
            if master_key_file.exists():
                # تحميل المفتاح الموجود
                with open(master_key_file, 'rb') as f:
                    self.master_key = f.read()
            else:
                # إنتاج مفتاح جديد
                self.master_key = Fernet.generate_key()
                with open(master_key_file, 'wb') as f:
                    f.write(self.master_key)
                # تعيين صلاحيات الملف (قراءة فقط للمالك)
                os.chmod(master_key_file, 0o600)
            
            self.encryption_key = Fernet(self.master_key)
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التشفير: {str(e)}")
            raise
    
    def _load_config(self):
        """تحميل إعدادات النظام"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            self.logger.warning(f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def _save_config(self):
        """حفظ إعدادات النظام"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def _hash_password(self, password: str, salt: str = None) -> Tuple[str, str]:
        """تشفير كلمة المرور"""
        try:
            if salt is None:
                salt = secrets.token_hex(32)
            
            # استخدام PBKDF2 مع SHA256
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt.encode(),
                iterations=100000,
                backend=default_backend()
            )
            
            password_hash = base64.b64encode(kdf.derive(password.encode())).decode()
            return password_hash, salt
            
        except Exception as e:
            self.logger.error(f"خطأ في تشفير كلمة المرور: {str(e)}")
            raise
    
    def _verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            computed_hash, _ = self._hash_password(password, salt)
            return hmac.compare_digest(password_hash, computed_hash)
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من كلمة المرور: {str(e)}")
            return False
    
    def _validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """التحقق من قوة كلمة المرور"""
        errors = []
        
        # طول كلمة المرور
        if len(password) < self.config["password_min_length"]:
            errors.append(f"كلمة المرور يجب أن تكون {self.config['password_min_length']} أحرف على الأقل")
        
        # الأحرف الكبيرة
        if self.config["password_require_uppercase"] and not any(c.isupper() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # الأحرف الصغيرة
        if self.config["password_require_lowercase"] and not any(c.islower() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # الأرقام
        if self.config["password_require_numbers"] and not any(c.isdigit() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        # الرموز
        if self.config["password_require_symbols"]:
            symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in symbols for c in password):
                errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        return len(errors) == 0, errors

    def create_user(self, username: str, email: str, password: str,
                   phone: str = None, security_level: SecurityLevel = SecurityLevel.MEDIUM) -> Tuple[bool, str]:
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من وجود المستخدم
            if self._user_exists(username, email):
                return False, "اسم المستخدم أو البريد الإلكتروني موجود بالفعل"

            # التحقق من قوة كلمة المرور
            is_strong, errors = self._validate_password_strength(password)
            if not is_strong:
                return False, "كلمة المرور ضعيفة: " + ", ".join(errors)

            # تشفير كلمة المرور
            password_hash, salt = self._hash_password(password)

            # إنشاء المستخدم
            user_id = str(uuid.uuid4())
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                phone=phone,
                password_hash=password_hash,
                salt=salt,
                security_level=security_level
            )

            # حفظ المستخدم
            self.users_database[user_id] = user
            self._save_users_database()

            self.logger.info(f"تم إنشاء مستخدم جديد: {username}")
            return True, user_id

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المستخدم {username}: {str(e)}")
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"

    def _user_exists(self, username: str, email: str) -> bool:
        """التحقق من وجود المستخدم"""
        for user in self.users_database.values():
            if user.username == username or user.email == email:
                return True
        return False

    def authenticate_user(self, username: str, password: str,
                         ip_address: str = "unknown", user_agent: str = "unknown") -> Tuple[bool, str, Optional[str]]:
        """مصادقة المستخدم"""
        try:
            # البحث عن المستخدم
            user = self._find_user_by_username(username)
            if not user:
                self._log_authentication_attempt(
                    "", AuthenticationMethod.PASSWORD, False,
                    ip_address, user_agent, "مستخدم غير موجود"
                )
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None

            # التحقق من حالة المستخدم
            if not user.is_active:
                self._log_authentication_attempt(
                    user.user_id, AuthenticationMethod.PASSWORD, False,
                    ip_address, user_agent, "حساب معطل"
                )
                return False, "الحساب معطل", None

            # التحقق من القفل
            if user.locked_until and datetime.now() < user.locked_until:
                remaining_time = user.locked_until - datetime.now()
                self._log_authentication_attempt(
                    user.user_id, AuthenticationMethod.PASSWORD, False,
                    ip_address, user_agent, "حساب مقفل"
                )
                return False, f"الحساب مقفل لمدة {remaining_time.seconds // 60} دقيقة", None

            # التحقق من كلمة المرور
            if not self._verify_password(password, user.password_hash, user.salt):
                # زيادة عدد المحاولات الفاشلة
                user.failed_attempts += 1

                # قفل الحساب إذا تجاوز الحد المسموح
                if user.failed_attempts >= self.config["max_failed_attempts"]:
                    user.locked_until = datetime.now() + timedelta(
                        minutes=self.config["lockout_duration_minutes"]
                    )
                    self.logger.warning(f"تم قفل حساب المستخدم {username} بسبب المحاولات الفاشلة")

                user.updated_at = datetime.now()
                self._save_users_database()

                self._log_authentication_attempt(
                    user.user_id, AuthenticationMethod.PASSWORD, False,
                    ip_address, user_agent, "كلمة مرور خاطئة"
                )
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None

            # نجحت المصادقة - إعادة تعيين المحاولات الفاشلة
            user.failed_attempts = 0
            user.locked_until = None
            user.last_login = datetime.now()
            user.updated_at = datetime.now()
            self._save_users_database()

            # تسجيل المحاولة الناجحة
            self._log_authentication_attempt(
                user.user_id, AuthenticationMethod.PASSWORD, True,
                ip_address, user_agent
            )

            # إنشاء جلسة
            session_id = self._create_session(user.user_id, ip_address, user_agent)

            self.logger.info(f"نجحت مصادقة المستخدم: {username}")
            return True, "تمت المصادقة بنجاح", session_id

        except Exception as e:
            self.logger.error(f"خطأ في مصادقة المستخدم {username}: {str(e)}")
            return False, f"خطأ في المصادقة: {str(e)}", None

    def _find_user_by_username(self, username: str) -> Optional[User]:
        """البحث عن المستخدم بالاسم"""
        for user in self.users_database.values():
            if user.username == username:
                return user
        return None

    def _find_user_by_id(self, user_id: str) -> Optional[User]:
        """البحث عن المستخدم بالمعرف"""
        return self.users_database.get(user_id)

    def _create_session(self, user_id: str, ip_address: str, user_agent: str) -> str:
        """إنشاء جلسة جديدة"""
        try:
            session_id = secrets.token_urlsafe(32)

            # إنشاء الجلسة
            session = Session(
                session_id=session_id,
                user_id=user_id,
                created_at=datetime.now(),
                last_activity=datetime.now(),
                expires_at=datetime.now() + timedelta(minutes=self.config["session_timeout_minutes"]),
                ip_address=ip_address,
                user_agent=user_agent,
                authenticated_methods={AuthenticationMethod.PASSWORD}
            )

            # إضافة بصمة الجهاز إذا كانت مفعلة
            if self.config["enable_device_fingerprinting"]:
                session.device_fingerprint = self._generate_device_fingerprint(user_agent, ip_address)

            # حفظ الجلسة
            self.sessions_database[session_id] = session
            self._save_sessions_database()

            return session_id

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الجلسة للمستخدم {user_id}: {str(e)}")
            raise

    def _generate_device_fingerprint(self, user_agent: str, ip_address: str) -> str:
        """إنتاج بصمة الجهاز"""
        try:
            fingerprint_data = f"{user_agent}:{ip_address}:{secrets.token_hex(8)}"
            return hashlib.sha256(fingerprint_data.encode()).hexdigest()
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج بصمة الجهاز: {str(e)}")
            return secrets.token_hex(16)

    def validate_session(self, session_id: str, ip_address: str = None,
                        user_agent: str = None) -> Tuple[bool, Optional[User], Optional[str]]:
        """التحقق من صحة الجلسة"""
        try:
            session = self.sessions_database.get(session_id)
            if not session:
                return False, None, "جلسة غير موجودة"

            # التحقق من حالة الجلسة
            if session.status != SessionStatus.ACTIVE:
                return False, None, f"الجلسة {session.status.value}"

            # التحقق من انتهاء الصلاحية
            if datetime.now() > session.expires_at:
                session.status = SessionStatus.EXPIRED
                self._save_sessions_database()
                return False, None, "انتهت صلاحية الجلسة"

            # التحقق من عنوان IP إذا كان متوفراً
            if ip_address and session.ip_address != ip_address:
                self.logger.warning(f"تغيير عنوان IP للجلسة {session_id}: {session.ip_address} -> {ip_address}")
                # يمكن إضافة منطق إضافي هنا للتعامل مع تغيير IP

            # تحديث آخر نشاط
            session.last_activity = datetime.now()
            self._save_sessions_database()

            # الحصول على المستخدم
            user = self._find_user_by_id(session.user_id)
            if not user or not user.is_active:
                return False, None, "مستخدم غير صالح"

            return True, user, None

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الجلسة {session_id}: {str(e)}")
            return False, None, f"خطأ في التحقق من الجلسة: {str(e)}"

    def setup_totp(self, user_id: str) -> Tuple[bool, str, Optional[str]]:
        """إعداد المصادقة الثنائية TOTP"""
        try:
            user = self._find_user_by_id(user_id)
            if not user:
                return False, "مستخدم غير موجود", None

            # إنتاج مفتاح TOTP سري
            secret = pyotp.random_base32()

            # إنشاء رابط QR Code
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user.email,
                issuer_name="تطبيق المحتوى التلقائي"
            )

            # حفظ المفتاح السري (مشفر)
            encrypted_secret = self.encryption_key.encrypt(secret.encode())
            user.totp_secret = base64.b64encode(encrypted_secret).decode()
            user.enabled_methods.add(AuthenticationMethod.TOTP)
            user.updated_at = datetime.now()

            self._save_users_database()

            self.logger.info(f"تم إعداد TOTP للمستخدم {user.username}")
            return True, "تم إعداد المصادقة الثنائية بنجاح", totp_uri

        except Exception as e:
            self.logger.error(f"خطأ في إعداد TOTP للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في إعداد المصادقة الثنائية: {str(e)}", None

    def verify_totp(self, user_id: str, totp_code: str) -> Tuple[bool, str]:
        """التحقق من رمز TOTP"""
        try:
            user = self._find_user_by_id(user_id)
            if not user or not user.totp_secret:
                return False, "المصادقة الثنائية غير مفعلة"

            # فك تشفير المفتاح السري
            encrypted_secret = base64.b64decode(user.totp_secret.encode())
            secret = self.encryption_key.decrypt(encrypted_secret).decode()

            # التحقق من الرمز
            totp = pyotp.TOTP(secret)
            is_valid = totp.verify(totp_code, valid_window=self.config["totp_window"])

            if is_valid:
                self.logger.info(f"نجح التحقق من TOTP للمستخدم {user.username}")
                return True, "تم التحقق من الرمز بنجاح"
            else:
                self.logger.warning(f"فشل التحقق من TOTP للمستخدم {user.username}")
                return False, "رمز التحقق غير صحيح"

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من TOTP للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في التحقق من الرمز: {str(e)}"

    def generate_backup_codes(self, user_id: str) -> Tuple[bool, str, List[str]]:
        """إنتاج رموز احتياطية"""
        try:
            user = self._find_user_by_id(user_id)
            if not user:
                return False, "مستخدم غير موجود", []

            # إنتاج رموز احتياطية
            backup_codes = []
            for _ in range(self.config["backup_codes_count"]):
                code = secrets.token_hex(4).upper()  # 8 أحرف
                backup_codes.append(code)

            # تشفير وحفظ الرموز
            encrypted_codes = []
            for code in backup_codes:
                encrypted_code = self.encryption_key.encrypt(code.encode())
                encrypted_codes.append(base64.b64encode(encrypted_code).decode())

            user.backup_codes = encrypted_codes
            user.enabled_methods.add(AuthenticationMethod.BACKUP_CODES)
            user.updated_at = datetime.now()

            self._save_users_database()

            self.logger.info(f"تم إنتاج رموز احتياطية للمستخدم {user.username}")
            return True, "تم إنتاج الرموز الاحتياطية بنجاح", backup_codes

        except Exception as e:
            self.logger.error(f"خطأ في إنتاج الرموز الاحتياطية للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في إنتاج الرموز الاحتياطية: {str(e)}", []

    def verify_backup_code(self, user_id: str, backup_code: str) -> Tuple[bool, str]:
        """التحقق من الرمز الاحتياطي"""
        try:
            user = self._find_user_by_id(user_id)
            if not user or not user.backup_codes:
                return False, "الرموز الاحتياطية غير متوفرة"

            # البحث عن الرمز
            for i, encrypted_code in enumerate(user.backup_codes):
                try:
                    encrypted_data = base64.b64decode(encrypted_code.encode())
                    decrypted_code = self.encryption_key.decrypt(encrypted_data).decode()

                    if hmac.compare_digest(backup_code.upper(), decrypted_code):
                        # إزالة الرمز المستخدم
                        user.backup_codes.pop(i)
                        user.updated_at = datetime.now()
                        self._save_users_database()

                        self.logger.info(f"تم استخدام رمز احتياطي للمستخدم {user.username}")
                        return True, "تم التحقق من الرمز الاحتياطي بنجاح"

                except Exception:
                    continue

            self.logger.warning(f"فشل التحقق من الرمز الاحتياطي للمستخدم {user.username}")
            return False, "الرمز الاحتياطي غير صحيح"

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الرمز الاحتياطي للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في التحقق من الرمز الاحتياطي: {str(e)}"

    def authenticate_mfa(self, session_id: str, method: AuthenticationMethod,
                        code: str, ip_address: str = "unknown",
                        user_agent: str = "unknown") -> Tuple[bool, str]:
        """مصادقة متعددة العوامل"""
        try:
            # التحقق من الجلسة
            is_valid, user, error = self.validate_session(session_id, ip_address, user_agent)
            if not is_valid:
                return False, error or "جلسة غير صالحة"

            # التحقق من طريقة المصادقة
            if method not in user.enabled_methods:
                self._log_authentication_attempt(
                    user.user_id, method, False, ip_address, user_agent,
                    "طريقة مصادقة غير مفعلة"
                )
                return False, "طريقة المصادقة غير مفعلة"

            # تنفيذ المصادقة حسب النوع
            success = False
            error_message = ""

            if method == AuthenticationMethod.TOTP:
                success, error_message = self.verify_totp(user.user_id, code)
            elif method == AuthenticationMethod.BACKUP_CODES:
                success, error_message = self.verify_backup_code(user.user_id, code)
            else:
                error_message = "طريقة مصادقة غير مدعومة"

            # تسجيل المحاولة
            self._log_authentication_attempt(
                user.user_id, method, success, ip_address, user_agent,
                None if success else error_message
            )

            # تحديث الجلسة في حالة النجاح
            if success:
                session = self.sessions_database[session_id]
                session.authenticated_methods.add(method)
                session.last_activity = datetime.now()
                self._save_sessions_database()

            return success, error_message

        except Exception as e:
            self.logger.error(f"خطأ في المصادقة متعددة العوامل: {str(e)}")
            return False, f"خطأ في المصادقة: {str(e)}"

    def logout_session(self, session_id: str) -> Tuple[bool, str]:
        """تسجيل الخروج من الجلسة"""
        try:
            session = self.sessions_database.get(session_id)
            if not session:
                return False, "جلسة غير موجودة"

            # تحديث حالة الجلسة
            session.status = SessionStatus.TERMINATED
            self._save_sessions_database()

            self.logger.info(f"تم تسجيل الخروج من الجلسة {session_id}")
            return True, "تم تسجيل الخروج بنجاح"

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج من الجلسة {session_id}: {str(e)}")
            return False, f"خطأ في تسجيل الخروج: {str(e)}"

    def logout_all_sessions(self, user_id: str, except_session: str = None) -> Tuple[bool, str, int]:
        """تسجيل الخروج من جميع الجلسات"""
        try:
            terminated_count = 0

            for session_id, session in self.sessions_database.items():
                if (session.user_id == user_id and
                    session.status == SessionStatus.ACTIVE and
                    session_id != except_session):

                    session.status = SessionStatus.TERMINATED
                    terminated_count += 1

            self._save_sessions_database()

            self.logger.info(f"تم تسجيل الخروج من {terminated_count} جلسة للمستخدم {user_id}")
            return True, f"تم تسجيل الخروج من {terminated_count} جلسة", terminated_count

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج من جميع الجلسات للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في تسجيل الخروج: {str(e)}", 0

    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """الحصول على جلسات المستخدم"""
        try:
            sessions = []

            for session_id, session in self.sessions_database.items():
                if session.user_id == user_id:
                    session_info = {
                        "session_id": session_id,
                        "created_at": session.created_at.isoformat(),
                        "last_activity": session.last_activity.isoformat(),
                        "expires_at": session.expires_at.isoformat(),
                        "status": session.status.value,
                        "ip_address": session.ip_address,
                        "user_agent": session.user_agent,
                        "authenticated_methods": [method.value for method in session.authenticated_methods],
                        "device_fingerprint": session.device_fingerprint,
                        "location": session.location
                    }
                    sessions.append(session_info)

            return sessions

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على جلسات المستخدم {user_id}: {str(e)}")
            return []

    def change_password(self, user_id: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """تغيير كلمة المرور"""
        try:
            user = self._find_user_by_id(user_id)
            if not user:
                return False, "مستخدم غير موجود"

            # التحقق من كلمة المرور الحالية
            if not self._verify_password(old_password, user.password_hash, user.salt):
                return False, "كلمة المرور الحالية غير صحيحة"

            # التحقق من قوة كلمة المرور الجديدة
            is_strong, errors = self._validate_password_strength(new_password)
            if not is_strong:
                return False, "كلمة المرور الجديدة ضعيفة: " + ", ".join(errors)

            # تشفير كلمة المرور الجديدة
            new_password_hash, new_salt = self._hash_password(new_password)

            # تحديث كلمة المرور
            user.password_hash = new_password_hash
            user.salt = new_salt
            user.updated_at = datetime.now()

            self._save_users_database()

            self.logger.info(f"تم تغيير كلمة المرور للمستخدم {user.username}")
            return True, "تم تغيير كلمة المرور بنجاح"

        except Exception as e:
            self.logger.error(f"خطأ في تغيير كلمة المرور للمستخدم {user_id}: {str(e)}")
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    def reset_password(self, username: str, new_password: str) -> Tuple[bool, str]:
        """إعادة تعيين كلمة المرور (للمدير)"""
        try:
            user = self._find_user_by_username(username)
            if not user:
                return False, "مستخدم غير موجود"

            # التحقق من قوة كلمة المرور الجديدة
            is_strong, errors = self._validate_password_strength(new_password)
            if not is_strong:
                return False, "كلمة المرور الجديدة ضعيفة: " + ", ".join(errors)

            # تشفير كلمة المرور الجديدة
            new_password_hash, new_salt = self._hash_password(new_password)

            # تحديث كلمة المرور
            user.password_hash = new_password_hash
            user.salt = new_salt
            user.failed_attempts = 0
            user.locked_until = None
            user.updated_at = datetime.now()

            self._save_users_database()

            # إنهاء جميع الجلسات النشطة
            self.logout_all_sessions(user.user_id)

            self.logger.info(f"تم إعادة تعيين كلمة المرور للمستخدم {user.username}")
            return True, "تم إعادة تعيين كلمة المرور بنجاح"

        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين كلمة المرور للمستخدم {username}: {str(e)}")
            return False, f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"

    def lock_user(self, user_id: str, duration_minutes: int = None) -> Tuple[bool, str]:
        """قفل المستخدم"""
        try:
            user = self._find_user_by_id(user_id)
            if not user:
                return False, "مستخدم غير موجود"

            if duration_minutes is None:
                duration_minutes = self.config["lockout_duration_minutes"]

            user.locked_until = datetime.now() + timedelta(minutes=duration_minutes)
            user.updated_at = datetime.now()

            self._save_users_database()

            # إنهاء جميع الجلسات النشطة
            self.logout_all_sessions(user.user_id)

            self.logger.info(f"تم قفل المستخدم {user.username} لمدة {duration_minutes} دقيقة")
            return True, f"تم قفل المستخدم لمدة {duration_minutes} دقيقة"

        except Exception as e:
            self.logger.error(f"خطأ في قفل المستخدم {user_id}: {str(e)}")
            return False, f"خطأ في قفل المستخدم: {str(e)}"

    def unlock_user(self, user_id: str) -> Tuple[bool, str]:
        """إلغاء قفل المستخدم"""
        try:
            user = self._find_user_by_id(user_id)
            if not user:
                return False, "مستخدم غير موجود"

            user.locked_until = None
            user.failed_attempts = 0
            user.updated_at = datetime.now()

            self._save_users_database()

            self.logger.info(f"تم إلغاء قفل المستخدم {user.username}")
            return True, "تم إلغاء قفل المستخدم بنجاح"

        except Exception as e:
            self.logger.error(f"خطأ في إلغاء قفل المستخدم {user_id}: {str(e)}")
            return False, f"خطأ في إلغاء قفل المستخدم: {str(e)}"

    def _log_authentication_attempt(self, user_id: str, method: AuthenticationMethod,
                                   success: bool, ip_address: str, user_agent: str,
                                   failure_reason: str = None):
        """تسجيل محاولة المصادقة"""
        try:
            attempt = AuthenticationAttempt(
                attempt_id=str(uuid.uuid4()),
                user_id=user_id,
                method=method,
                success=success,
                timestamp=datetime.now(),
                ip_address=ip_address,
                user_agent=user_agent,
                failure_reason=failure_reason
            )

            self.authentication_attempts.append(attempt)

            # الاحتفاظ بآخر 1000 محاولة فقط
            if len(self.authentication_attempts) > 1000:
                self.authentication_attempts = self.authentication_attempts[-1000:]

            self._save_authentication_attempts()

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل محاولة المصادقة: {str(e)}")

    def _load_users_database(self):
        """تحميل قاعدة بيانات المستخدمين"""
        try:
            if self.users_db_file.exists():
                with open(self.users_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                for user_id, user_data in data.items():
                    # تحويل التواريخ
                    if user_data.get('created_at'):
                        user_data['created_at'] = datetime.fromisoformat(user_data['created_at'])
                    if user_data.get('updated_at'):
                        user_data['updated_at'] = datetime.fromisoformat(user_data['updated_at'])
                    if user_data.get('last_login'):
                        user_data['last_login'] = datetime.fromisoformat(user_data['last_login'])
                    if user_data.get('locked_until'):
                        user_data['locked_until'] = datetime.fromisoformat(user_data['locked_until'])

                    # تحويل مستوى الأمان
                    if user_data.get('security_level'):
                        user_data['security_level'] = SecurityLevel(user_data['security_level'])

                    # تحويل طرق المصادقة
                    if user_data.get('enabled_methods'):
                        user_data['enabled_methods'] = {
                            AuthenticationMethod(method) for method in user_data['enabled_methods']
                        }

                    self.users_database[user_id] = User(**user_data)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل قاعدة بيانات المستخدمين: {str(e)}")

    def _save_users_database(self):
        """حفظ قاعدة بيانات المستخدمين"""
        try:
            data = {}
            for user_id, user in self.users_database.items():
                user_dict = asdict(user)

                # تحويل التواريخ إلى نص
                if user_dict.get('created_at'):
                    user_dict['created_at'] = user_dict['created_at'].isoformat()
                if user_dict.get('updated_at'):
                    user_dict['updated_at'] = user_dict['updated_at'].isoformat()
                if user_dict.get('last_login'):
                    user_dict['last_login'] = user_dict['last_login'].isoformat()
                if user_dict.get('locked_until'):
                    user_dict['locked_until'] = user_dict['locked_until'].isoformat()

                # تحويل مستوى الأمان
                if user_dict.get('security_level'):
                    user_dict['security_level'] = user_dict['security_level'].value

                # تحويل طرق المصادقة
                if user_dict.get('enabled_methods'):
                    user_dict['enabled_methods'] = [
                        method.value for method in user_dict['enabled_methods']
                    ]

                data[user_id] = user_dict

            with open(self.users_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات المستخدمين: {str(e)}")

    def _load_sessions_database(self):
        """تحميل قاعدة بيانات الجلسات"""
        try:
            if self.sessions_db_file.exists():
                with open(self.sessions_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                for session_id, session_data in data.items():
                    # تحويل التواريخ
                    if session_data.get('created_at'):
                        session_data['created_at'] = datetime.fromisoformat(session_data['created_at'])
                    if session_data.get('last_activity'):
                        session_data['last_activity'] = datetime.fromisoformat(session_data['last_activity'])
                    if session_data.get('expires_at'):
                        session_data['expires_at'] = datetime.fromisoformat(session_data['expires_at'])

                    # تحويل حالة الجلسة
                    if session_data.get('status'):
                        session_data['status'] = SessionStatus(session_data['status'])

                    # تحويل مستوى الأمان
                    if session_data.get('security_level'):
                        session_data['security_level'] = SecurityLevel(session_data['security_level'])

                    # تحويل طرق المصادقة
                    if session_data.get('authenticated_methods'):
                        session_data['authenticated_methods'] = {
                            AuthenticationMethod(method) for method in session_data['authenticated_methods']
                        }

                    self.sessions_database[session_id] = Session(**session_data)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل قاعدة بيانات الجلسات: {str(e)}")

    def _save_sessions_database(self):
        """حفظ قاعدة بيانات الجلسات"""
        try:
            data = {}
            for session_id, session in self.sessions_database.items():
                session_dict = asdict(session)

                # تحويل التواريخ إلى نص
                if session_dict.get('created_at'):
                    session_dict['created_at'] = session_dict['created_at'].isoformat()
                if session_dict.get('last_activity'):
                    session_dict['last_activity'] = session_dict['last_activity'].isoformat()
                if session_dict.get('expires_at'):
                    session_dict['expires_at'] = session_dict['expires_at'].isoformat()

                # تحويل حالة الجلسة
                if session_dict.get('status'):
                    session_dict['status'] = session_dict['status'].value

                # تحويل مستوى الأمان
                if session_dict.get('security_level'):
                    session_dict['security_level'] = session_dict['security_level'].value

                # تحويل طرق المصادقة
                if session_dict.get('authenticated_methods'):
                    session_dict['authenticated_methods'] = [
                        method.value for method in session_dict['authenticated_methods']
                    ]

                data[session_id] = session_dict

            with open(self.sessions_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات الجلسات: {str(e)}")

    def _load_authentication_attempts(self):
        """تحميل سجل محاولات المصادقة"""
        try:
            if self.attempts_log_file.exists():
                with open(self.attempts_log_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                for attempt_data in data:
                    # تحويل التاريخ
                    if attempt_data.get('timestamp'):
                        attempt_data['timestamp'] = datetime.fromisoformat(attempt_data['timestamp'])

                    # تحويل طريقة المصادقة
                    if attempt_data.get('method'):
                        attempt_data['method'] = AuthenticationMethod(attempt_data['method'])

                    self.authentication_attempts.append(AuthenticationAttempt(**attempt_data))

        except Exception as e:
            self.logger.error(f"خطأ في تحميل سجل محاولات المصادقة: {str(e)}")

    def _save_authentication_attempts(self):
        """حفظ سجل محاولات المصادقة"""
        try:
            data = []
            for attempt in self.authentication_attempts:
                attempt_dict = asdict(attempt)

                # تحويل التاريخ إلى نص
                if attempt_dict.get('timestamp'):
                    attempt_dict['timestamp'] = attempt_dict['timestamp'].isoformat()

                # تحويل طريقة المصادقة
                if attempt_dict.get('method'):
                    attempt_dict['method'] = attempt_dict['method'].value

                data.append(attempt_dict)

            with open(self.attempts_log_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ سجل محاولات المصادقة: {str(e)}")

    def _start_session_cleanup(self):
        """بدء تنظيف الجلسات التلقائي"""
        def cleanup_sessions():
            while True:
                try:
                    self._cleanup_expired_sessions()
                    time.sleep(self.config["session_cleanup_interval"])
                except Exception as e:
                    self.logger.error(f"خطأ في تنظيف الجلسات: {str(e)}")
                    time.sleep(60)  # انتظار دقيقة في حالة الخطأ

        cleanup_thread = threading.Thread(target=cleanup_sessions, daemon=True)
        cleanup_thread.start()

    def _cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            current_time = datetime.now()
            expired_sessions = []

            for session_id, session in self.sessions_database.items():
                if (session.status == SessionStatus.ACTIVE and
                    current_time > session.expires_at):
                    session.status = SessionStatus.EXPIRED
                    expired_sessions.append(session_id)

            if expired_sessions:
                self._save_sessions_database()
                self.logger.info(f"تم تنظيف {len(expired_sessions)} جلسة منتهية الصلاحية")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الجلسات المنتهية الصلاحية: {str(e)}")

    def get_authentication_statistics(self, days: int = 30) -> Dict[str, Any]:
        """الحصول على إحصائيات المصادقة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            # تصفية المحاولات حسب التاريخ
            recent_attempts = [
                attempt for attempt in self.authentication_attempts
                if attempt.timestamp >= cutoff_date
            ]

            # إحصائيات عامة
            total_attempts = len(recent_attempts)
            successful_attempts = len([a for a in recent_attempts if a.success])
            failed_attempts = total_attempts - successful_attempts
            success_rate = (successful_attempts / total_attempts * 100) if total_attempts > 0 else 0

            # إحصائيات حسب طريقة المصادقة
            method_stats = {}
            for method in AuthenticationMethod:
                method_attempts = [a for a in recent_attempts if a.method == method]
                method_successful = len([a for a in method_attempts if a.success])
                method_stats[method.value] = {
                    "total": len(method_attempts),
                    "successful": method_successful,
                    "failed": len(method_attempts) - method_successful,
                    "success_rate": (method_successful / len(method_attempts) * 100) if method_attempts else 0
                }

            # إحصائيات حسب اليوم
            daily_stats = {}
            for i in range(days):
                date = (datetime.now() - timedelta(days=i)).date()
                date_str = date.isoformat()

                day_attempts = [
                    a for a in recent_attempts
                    if a.timestamp.date() == date
                ]

                daily_stats[date_str] = {
                    "total": len(day_attempts),
                    "successful": len([a for a in day_attempts if a.success]),
                    "failed": len([a for a in day_attempts if not a.success])
                }

            # أكثر عناوين IP نشاطاً
            ip_stats = {}
            for attempt in recent_attempts:
                ip = attempt.ip_address
                if ip not in ip_stats:
                    ip_stats[ip] = {"total": 0, "successful": 0, "failed": 0}

                ip_stats[ip]["total"] += 1
                if attempt.success:
                    ip_stats[ip]["successful"] += 1
                else:
                    ip_stats[ip]["failed"] += 1

            # ترتيب عناوين IP حسب النشاط
            top_ips = sorted(ip_stats.items(), key=lambda x: x[1]["total"], reverse=True)[:10]

            # إحصائيات المستخدمين
            user_stats = {
                "total_users": len(self.users_database),
                "active_users": len([u for u in self.users_database.values() if u.is_active]),
                "locked_users": len([u for u in self.users_database.values()
                                   if u.locked_until and datetime.now() < u.locked_until]),
                "users_with_mfa": len([u for u in self.users_database.values()
                                     if AuthenticationMethod.TOTP in u.enabled_methods])
            }

            # إحصائيات الجلسات
            active_sessions = len([s for s in self.sessions_database.values()
                                 if s.status == SessionStatus.ACTIVE])

            return {
                "period_days": days,
                "total_attempts": total_attempts,
                "successful_attempts": successful_attempts,
                "failed_attempts": failed_attempts,
                "success_rate": round(success_rate, 2),
                "method_statistics": method_stats,
                "daily_statistics": daily_stats,
                "top_ip_addresses": dict(top_ips),
                "user_statistics": user_stats,
                "active_sessions": active_sessions,
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات المصادقة: {str(e)}")
            return {}

    def get_security_report(self) -> Dict[str, Any]:
        """تقرير الأمان الشامل"""
        try:
            # تحليل المخاطر الأمنية
            security_issues = []

            # فحص المستخدمين بدون MFA
            users_without_mfa = [
                u for u in self.users_database.values()
                if (u.is_active and u.security_level in [SecurityLevel.HIGH, SecurityLevel.CRITICAL] and
                    AuthenticationMethod.TOTP not in u.enabled_methods)
            ]

            if users_without_mfa:
                security_issues.append({
                    "type": "missing_mfa",
                    "severity": "high",
                    "description": f"{len(users_without_mfa)} مستخدم بمستوى أمان عالي بدون مصادقة ثنائية",
                    "affected_users": [u.username for u in users_without_mfa]
                })

            # فحص المحاولات المشبوهة
            recent_attempts = [
                a for a in self.authentication_attempts
                if a.timestamp >= datetime.now() - timedelta(hours=24)
            ]

            # تحليل عناوين IP المشبوهة
            ip_failure_count = {}
            for attempt in recent_attempts:
                if not attempt.success:
                    ip = attempt.ip_address
                    ip_failure_count[ip] = ip_failure_count.get(ip, 0) + 1

            suspicious_ips = [ip for ip, count in ip_failure_count.items() if count >= 10]

            if suspicious_ips:
                security_issues.append({
                    "type": "suspicious_ips",
                    "severity": "medium",
                    "description": f"{len(suspicious_ips)} عنوان IP مع محاولات فاشلة متكررة",
                    "ip_addresses": suspicious_ips
                })

            # فحص الجلسات طويلة المدى
            long_sessions = [
                s for s in self.sessions_database.values()
                if (s.status == SessionStatus.ACTIVE and
                    datetime.now() - s.created_at > timedelta(days=7))
            ]

            if long_sessions:
                security_issues.append({
                    "type": "long_sessions",
                    "severity": "low",
                    "description": f"{len(long_sessions)} جلسة نشطة لأكثر من أسبوع",
                    "session_count": len(long_sessions)
                })

            # تقييم الأمان العام
            security_score = 100
            for issue in security_issues:
                if issue["severity"] == "high":
                    security_score -= 20
                elif issue["severity"] == "medium":
                    security_score -= 10
                elif issue["severity"] == "low":
                    security_score -= 5

            security_score = max(0, security_score)

            return {
                "security_score": security_score,
                "security_level": (
                    "ممتاز" if security_score >= 90 else
                    "جيد" if security_score >= 70 else
                    "متوسط" if security_score >= 50 else
                    "ضعيف"
                ),
                "security_issues": security_issues,
                "recommendations": self._get_security_recommendations(security_issues),
                "system_status": {
                    "total_users": len(self.users_database),
                    "active_sessions": len([s for s in self.sessions_database.values()
                                          if s.status == SessionStatus.ACTIVE]),
                    "failed_attempts_24h": len([a for a in recent_attempts if not a.success]),
                    "unique_ips_24h": len(set(a.ip_address for a in recent_attempts))
                },
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الأمان: {str(e)}")
            return {}

    def _get_security_recommendations(self, security_issues: List[Dict[str, Any]]) -> List[str]:
        """الحصول على توصيات الأمان"""
        recommendations = []

        for issue in security_issues:
            if issue["type"] == "missing_mfa":
                recommendations.append("تفعيل المصادقة الثنائية لجميع المستخدمين ذوي المستوى الأمني العالي")
            elif issue["type"] == "suspicious_ips":
                recommendations.append("مراجعة عناوين IP المشبوهة وإضافتها للقائمة السوداء إذا لزم الأمر")
            elif issue["type"] == "long_sessions":
                recommendations.append("مراجعة الجلسات طويلة المدى وتقليل مدة انتهاء الصلاحية")

        # توصيات عامة
        recommendations.extend([
            "مراجعة سجلات المصادقة بانتظام",
            "تحديث كلمات المرور بشكل دوري",
            "تفعيل التنبيهات للأنشطة المشبوهة",
            "إجراء نسخ احتياطية منتظمة لبيانات المصادقة"
        ])

        return recommendations

    def export_authentication_data(self, export_path: str = None) -> Tuple[bool, str]:
        """تصدير بيانات المصادقة"""
        try:
            if export_path is None:
                export_path = self.auth_dir / f"auth_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            else:
                export_path = Path(export_path)

            # إعداد البيانات للتصدير
            export_data = {
                "export_info": {
                    "version": "1.0",
                    "exported_at": datetime.now().isoformat(),
                    "system_config": self.config
                },
                "users": {},
                "sessions": {},
                "authentication_attempts": [],
                "statistics": self.get_authentication_statistics(30)
            }

            # تصدير المستخدمين (بدون كلمات المرور)
            for user_id, user in self.users_database.items():
                user_dict = asdict(user)
                # إزالة البيانات الحساسة
                user_dict.pop('password_hash', None)
                user_dict.pop('salt', None)
                user_dict.pop('totp_secret', None)
                user_dict.pop('backup_codes', None)

                # تحويل التواريخ والتعدادات
                if user_dict.get('created_at'):
                    user_dict['created_at'] = user_dict['created_at'].isoformat()
                if user_dict.get('updated_at'):
                    user_dict['updated_at'] = user_dict['updated_at'].isoformat()
                if user_dict.get('last_login'):
                    user_dict['last_login'] = user_dict['last_login'].isoformat()
                if user_dict.get('locked_until'):
                    user_dict['locked_until'] = user_dict['locked_until'].isoformat()
                if user_dict.get('security_level'):
                    user_dict['security_level'] = user_dict['security_level'].value
                if user_dict.get('enabled_methods'):
                    user_dict['enabled_methods'] = [method.value for method in user_dict['enabled_methods']]

                export_data["users"][user_id] = user_dict

            # تصدير الجلسات
            for session_id, session in self.sessions_database.items():
                session_dict = asdict(session)

                # تحويل التواريخ والتعدادات
                if session_dict.get('created_at'):
                    session_dict['created_at'] = session_dict['created_at'].isoformat()
                if session_dict.get('last_activity'):
                    session_dict['last_activity'] = session_dict['last_activity'].isoformat()
                if session_dict.get('expires_at'):
                    session_dict['expires_at'] = session_dict['expires_at'].isoformat()
                if session_dict.get('status'):
                    session_dict['status'] = session_dict['status'].value
                if session_dict.get('security_level'):
                    session_dict['security_level'] = session_dict['security_level'].value
                if session_dict.get('authenticated_methods'):
                    session_dict['authenticated_methods'] = [method.value for method in session_dict['authenticated_methods']]

                export_data["sessions"][session_id] = session_dict

            # تصدير محاولات المصادقة (آخر 100 محاولة فقط)
            recent_attempts = self.authentication_attempts[-100:]
            for attempt in recent_attempts:
                attempt_dict = asdict(attempt)

                if attempt_dict.get('timestamp'):
                    attempt_dict['timestamp'] = attempt_dict['timestamp'].isoformat()
                if attempt_dict.get('method'):
                    attempt_dict['method'] = attempt_dict['method'].value

                export_data["authentication_attempts"].append(attempt_dict)

            # حفظ البيانات
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير بيانات المصادقة إلى: {export_path}")
            return True, str(export_path)

        except Exception as e:
            self.logger.error(f"خطأ في تصدير بيانات المصادقة: {str(e)}")
            return False, f"خطأ في التصدير: {str(e)}"

    def cleanup_old_data(self, days: int = 90) -> Tuple[bool, str, Dict[str, int]]:
        """تنظيف البيانات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cleanup_stats = {
                "expired_sessions": 0,
                "old_attempts": 0,
                "inactive_users": 0
            }

            # تنظيف الجلسات المنتهية الصلاحية
            sessions_to_remove = []
            for session_id, session in self.sessions_database.items():
                if (session.status in [SessionStatus.EXPIRED, SessionStatus.TERMINATED] and
                    session.last_activity < cutoff_date):
                    sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                del self.sessions_database[session_id]
                cleanup_stats["expired_sessions"] += 1

            # تنظيف محاولات المصادقة القديمة
            old_attempts = [
                attempt for attempt in self.authentication_attempts
                if attempt.timestamp < cutoff_date
            ]

            self.authentication_attempts = [
                attempt for attempt in self.authentication_attempts
                if attempt.timestamp >= cutoff_date
            ]

            cleanup_stats["old_attempts"] = len(old_attempts)

            # حفظ التغييرات
            self._save_sessions_database()
            self._save_authentication_attempts()

            total_cleaned = sum(cleanup_stats.values())
            message = f"تم تنظيف {total_cleaned} عنصر قديم"

            self.logger.info(f"تم تنظيف البيانات القديمة: {cleanup_stats}")
            return True, message, cleanup_stats

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف البيانات القديمة: {str(e)}")
            return False, f"خطأ في التنظيف: {str(e)}", {}

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            current_time = datetime.now()

            # إحصائيات المستخدمين
            total_users = len(self.users_database)
            active_users = len([u for u in self.users_database.values() if u.is_active])
            locked_users = len([u for u in self.users_database.values()
                              if u.locked_until and current_time < u.locked_until])

            # إحصائيات الجلسات
            total_sessions = len(self.sessions_database)
            active_sessions = len([s for s in self.sessions_database.values()
                                 if s.status == SessionStatus.ACTIVE])
            expired_sessions = len([s for s in self.sessions_database.values()
                                  if s.status == SessionStatus.EXPIRED])

            # إحصائيات المصادقة (آخر 24 ساعة)
            recent_cutoff = current_time - timedelta(hours=24)
            recent_attempts = [a for a in self.authentication_attempts if a.timestamp >= recent_cutoff]
            recent_successful = len([a for a in recent_attempts if a.success])
            recent_failed = len(recent_attempts) - recent_successful

            # حالة النظام
            system_health = "صحي"
            if recent_failed > recent_successful * 2:  # إذا كانت المحاولات الفاشلة أكثر من ضعف الناجحة
                system_health = "تحذير - محاولات فاشلة كثيرة"
            elif locked_users > total_users * 0.1:  # إذا كان أكثر من 10% من المستخدمين مقفلين
                system_health = "تحذير - مستخدمين مقفلين كثيرين"

            return {
                "system_health": system_health,
                "uptime": "متاح",  # يمكن تحسينه لحساب الوقت الفعلي
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "locked": locked_users,
                    "with_mfa": len([u for u in self.users_database.values()
                                   if AuthenticationMethod.TOTP in u.enabled_methods])
                },
                "sessions": {
                    "total": total_sessions,
                    "active": active_sessions,
                    "expired": expired_sessions
                },
                "authentication_24h": {
                    "total_attempts": len(recent_attempts),
                    "successful": recent_successful,
                    "failed": recent_failed,
                    "success_rate": round((recent_successful / len(recent_attempts) * 100) if recent_attempts else 0, 2)
                },
                "database_sizes": {
                    "users": len(self.users_database),
                    "sessions": len(self.sessions_database),
                    "attempts": len(self.authentication_attempts)
                },
                "configuration": {
                    "session_timeout": self.config["session_timeout_minutes"],
                    "max_failed_attempts": self.config["max_failed_attempts"],
                    "lockout_duration": self.config["lockout_duration_minutes"],
                    "totp_available": TOTP_AVAILABLE
                },
                "last_updated": current_time.isoformat()
            }

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حالة النظام: {str(e)}")
            return {"error": f"خطأ في الحصول على حالة النظام: {str(e)}"}

    def shutdown(self):
        """إيقاف النظام بأمان"""
        try:
            # حفظ جميع البيانات
            self._save_users_database()
            self._save_sessions_database()
            self._save_authentication_attempts()
            self._save_config()

            self.logger.info("تم إيقاف نظام المصادقة المتقدم بأمان")

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف النظام: {str(e)}")


# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نظام المصادقة
    auth_system = AdvancedAuthenticationSystem()

    # إنشاء مستخدم تجريبي
    success, result = auth_system.create_user(
        username="admin",
        email="<EMAIL>",
        password="AdminPass123!",
        security_level=SecurityLevel.HIGH
    )

    if success:
        print(f"تم إنشاء المستخدم بنجاح: {result}")

        # محاولة تسجيل الدخول
        success, message, session_id = auth_system.authenticate_user(
            username="admin",
            password="AdminPass123!",
            ip_address="127.0.0.1",
            user_agent="Test Client"
        )

        if success:
            print(f"تم تسجيل الدخول بنجاح: {session_id}")

            # إعداد المصادقة الثنائية
            if TOTP_AVAILABLE:
                success, message, qr_uri = auth_system.setup_totp(result)
                if success:
                    print(f"تم إعداد المصادقة الثنائية: {qr_uri}")

            # الحصول على إحصائيات النظام
            stats = auth_system.get_authentication_statistics()
            print(f"إحصائيات النظام: {stats}")

            # الحصول على حالة النظام
            status = auth_system.get_system_status()
            print(f"حالة النظام: {status}")
        else:
            print(f"فشل تسجيل الدخول: {message}")
    else:
        print(f"فشل إنشاء المستخدم: {result}")

    # إيقاف النظام
    auth_system.shutdown()
