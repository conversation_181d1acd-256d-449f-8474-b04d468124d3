#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جالب محتوى Kick.com - Kick Content Fetcher
يجلب Live streams والمحتوى من منصة Kick.com
"""

import logging
import requests
import json
import time
import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs

from .base_fetcher import BaseFetcher, ContentItem

class KickFetcher(BaseFetcher):
    """جالب محتوى Kick.com"""
    
    def __init__(self, config_manager, security_manager):
        super().__init__("kick", config_manager, security_manager)
        
        # إعدادات Kick API
        self.base_url = "https://kick.com"
        self.api_base = "https://kick.com/api/v1"
        self.api_v2 = "https://kick.com/api/v2"
        
        # رؤوس خاصة بـ Kick
        self.session.headers.update({
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Origin': 'https://kick.com',
            'Referer': 'https://kick.com/',
            'X-Requested-With': 'XMLHttpRequest'
        })
        
        # قائمة المستخدمين المراقبين
        self.monitored_users = self._load_monitored_users()
        
        # معرفات البث المباشر النشط
        self.active_live_streams = {}
        
        # معرفات المحتوى المعروف
        self.known_content_ids = set()
    
    def _load_monitored_users(self) -> List[str]:
        """تحميل قائمة المستخدمين المراقبين"""
        try:
            users = self.config_manager.get_setting("kick_settings", "monitored_users", [])
            return users if isinstance(users, list) else []
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المستخدمين المراقبين: {str(e)}")
            return []
    
    def add_monitored_user(self, username: str) -> bool:
        """إضافة مستخدم للمراقبة"""
        try:
            username = username.lower().strip()
            
            if username not in self.monitored_users:
                self.monitored_users.append(username)
                self.config_manager.set_setting(
                    "kick_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إضافة المستخدم للمراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المستخدم: {str(e)}")
            return False
    
    def remove_monitored_user(self, username: str) -> bool:
        """إزالة مستخدم من المراقبة"""
        try:
            username = username.lower().strip()
            if username in self.monitored_users:
                self.monitored_users.remove(username)
                self.config_manager.set_setting(
                    "kick_settings", 
                    "monitored_users", 
                    self.monitored_users
                )
                self.logger.info(f"تم إزالة المستخدم من المراقبة: {username}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"خطأ في إزالة المستخدم: {str(e)}")
            return False
    
    def _build_auth_headers(self, token: str) -> Dict[str, str]:
        """بناء رؤوس المصادقة لـ Kick"""
        return {
            'Authorization': f'Bearer {token}',
            'X-Kick-Token': token
        }
    
    def test_connection(self) -> bool:
        """اختبار الاتصال مع Kick"""
        try:
            # اختبار الوصول لـ API
            response = self.session.get(f"{self.api_base}/channels", timeout=10)
            
            if response.status_code == 200:
                self.logger.info("تم اختبار الاتصال بنجاح")
                return True
            else:
                self.logger.error(f"فشل اختبار الاتصال: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في اختبار الاتصال: {str(e)}")
            return False
    
    def fetch_content(self, content_types: List[str] = None, 
                     limit: int = 10) -> List[ContentItem]:
        """جلب المحتوى من Kick"""
        try:
            content_types = content_types or ["lives", "clips"]
            all_content = []
            
            if "lives" in content_types:
                live_streams = self._fetch_live_streams(limit // 2)
                all_content.extend(live_streams)
            
            if "clips" in content_types:
                clips = self._fetch_clips(limit // 2)
                all_content.extend(clips)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            all_content.sort(key=lambda x: x.timestamp, reverse=True)
            
            self.stats["total_fetched"] += len(all_content)
            return all_content[:limit]
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المحتوى: {str(e)}")
            return []
    
    def _fetch_live_streams(self, limit: int) -> List[ContentItem]:
        """جلب البث المباشر من المستخدمين المراقبين"""
        live_streams = []
        
        try:
            for username in self.monitored_users:
                user_lives = self._fetch_user_live_stream(username)
                if user_lives:
                    live_streams.extend(user_lives)
                
                # تأخير بسيط لتجنب Rate Limiting
                time.sleep(1)
                
                if len(live_streams) >= limit:
                    break
            
            return live_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب البث المباشر: {str(e)}")
            return []
    
    def _fetch_user_live_stream(self, username: str) -> List[ContentItem]:
        """جلب البث المباشر لمستخدم معين"""
        try:
            # استخدام Kick API للحصول على معلومات القناة
            api_url = f"{self.api_base}/channels/{username}"
            response = self.session.get(api_url, timeout=15)
            
            if response.status_code != 200:
                self.logger.warning(f"لا يمكن الوصول لمستخدم: {username}")
                return []
            
            channel_data = response.json()
            
            # التحقق من وجود بث مباشر
            if self._is_channel_live(channel_data):
                live_item = self._create_live_stream_item(channel_data, username)
                if live_item:
                    return [live_item]
            
            return []
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب البث المباشر للمستخدم {username}: {str(e)}")
            return []
    
    def _is_channel_live(self, channel_data: Dict) -> bool:
        """التحقق من كون القناة تبث مباشرة"""
        try:
            # طرق مختلفة للتحقق من البث المباشر
            if "livestream" in channel_data:
                livestream = channel_data["livestream"]
                return livestream is not None and isinstance(livestream, dict)
            
            if "is_live" in channel_data:
                return bool(channel_data["is_live"])
            
            if "live" in channel_data:
                return bool(channel_data["live"])
            
            # التحقق من وجود معرف البث
            if "current_livestream_id" in channel_data:
                return channel_data["current_livestream_id"] is not None
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من حالة البث: {str(e)}")
            return False
    
    def _create_live_stream_item(self, channel_data: Dict, username: str) -> Optional[ContentItem]:
        """إنشاء عنصر بث مباشر من بيانات القناة"""
        try:
            # استخراج معلومات البث المباشر
            livestream = channel_data.get("livestream", {})
            
            # URL البث
            stream_url = f"{self.base_url}/{username}"
            
            # معلومات البث
            title = livestream.get("session_title", f"بث مباشر من {username}")
            if not title or title.strip() == "":
                title = f"بث مباشر من {username}"
            
            # الوصف
            description = channel_data.get("user", {}).get("bio", "")
            
            # عدد المشاهدين
            viewer_count = livestream.get("viewer_count", 0)
            
            # الفئة
            category = livestream.get("category", {})
            category_name = category.get("name", "") if category else ""
            
            # معلومات إضافية
            metadata = {
                "format": "live_stream",
                "source": "kick_api",
                "viewer_count": viewer_count,
                "category": category_name,
                "stream_id": livestream.get("id"),
                "channel_id": channel_data.get("id"),
                "original_data": livestream
            }
            
            # إنشاء عنصر المحتوى
            live_item = ContentItem(
                platform="kick",
                content_type="live",
                url=stream_url,
                title=title,
                description=description,
                author=username,
                timestamp=datetime.now(),
                metadata=metadata
            )
            
            # تتبع البث المباشر النشط
            self.active_live_streams[username] = {
                "start_time": datetime.now(),
                "content_id": live_item.id,
                "stream_id": livestream.get("id"),
                "viewer_count": viewer_count
            }
            
            return live_item
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء عنصر بث مباشر: {str(e)}")
            return None
    
    def _fetch_clips(self, limit: int) -> List[ContentItem]:
        """جلب المقاطع من المستخدمين المراقبين"""
        clips = []
        
        try:
            for username in self.monitored_users:
                user_clips = self._fetch_user_clips(username, limit // len(self.monitored_users) + 1)
                clips.extend(user_clips)
                
                time.sleep(1)
                
                if len(clips) >= limit:
                    break
            
            return clips
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المقاطع: {str(e)}")
            return []
    
    def _fetch_user_clips(self, username: str, limit: int) -> List[ContentItem]:
        """جلب مقاطع مستخدم معين"""
        try:
            # محاولة الحصول على معرف القناة أولاً
            channel_response = self.session.get(f"{self.api_base}/channels/{username}")
            if channel_response.status_code != 200:
                return []
            
            channel_data = channel_response.json()
            channel_id = channel_data.get("id")
            
            if not channel_id:
                return []
            
            # جلب المقاطع
            clips_url = f"{self.api_base}/channels/{channel_id}/clips"
            clips_response = self.session.get(clips_url, params={"limit": limit})
            
            if clips_response.status_code != 200:
                return []
            
            clips_data = clips_response.json()
            clips = []
            
            for clip_data in clips_data.get("data", []):
                clip_item = self._create_clip_item(clip_data, username)
                if clip_item and clip_item.id not in self.known_content_ids:
                    clips.append(clip_item)
                    self.known_content_ids.add(clip_item.id)
            
            return clips
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب مقاطع المستخدم {username}: {str(e)}")
            return []
    
    def _create_clip_item(self, clip_data: Dict, username: str) -> Optional[ContentItem]:
        """إنشاء عنصر مقطع من البيانات"""
        try:
            # URL المقطع
            clip_url = clip_data.get("clip_url", "")
            if not clip_url:
                clip_id = clip_data.get("id")
                clip_url = f"{self.base_url}/clips/{clip_id}" if clip_id else ""
            
            # معلومات المقطع
            title = clip_data.get("title", f"مقطع من {username}")
            description = clip_data.get("description", "")
            
            # تاريخ الإنشاء
            created_at = clip_data.get("created_at", "")
            timestamp = datetime.now()
            if created_at:
                try:
                    timestamp = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except:
                    pass
            
            # معلومات إضافية
            metadata = {
                "format": "clip",
                "source": "kick_api",
                "duration": clip_data.get("duration", 0),
                "view_count": clip_data.get("view_count", 0),
                "clip_id": clip_data.get("id"),
                "original_data": clip_data
            }
            
            return ContentItem(
                platform="kick",
                content_type="clip",
                url=clip_url,
                title=title,
                description=description,
                author=username,
                timestamp=timestamp,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء عنصر مقطع: {str(e)}")
            return None
    
    def get_channel_info(self, username: str) -> Optional[Dict]:
        """الحصول على معلومات القناة"""
        try:
            response = self.session.get(f"{self.api_base}/channels/{username}")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات القناة: {str(e)}")
            return None
    
    def get_live_stream_details(self, username: str) -> Optional[Dict]:
        """الحصول على تفاصيل البث المباشر"""
        try:
            channel_info = self.get_channel_info(username)
            if channel_info and self._is_channel_live(channel_info):
                return channel_info.get("livestream", {})
            return None
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على تفاصيل البث المباشر: {str(e)}")
            return None
    
    def get_active_live_streams(self) -> Dict[str, Dict]:
        """الحصول على البث المباشر النشط"""
        return self.active_live_streams.copy()
    
    def stop_monitoring_live_stream(self, username: str):
        """إيقاف مراقبة بث مباشر معين"""
        if username in self.active_live_streams:
            del self.active_live_streams[username]
            self.logger.info(f"تم إيقاف مراقبة البث المباشر: {username}")
    
    def get_monitored_users(self) -> List[str]:
        """الحصول على قائمة المستخدمين المراقبين"""
        return self.monitored_users.copy()
    
    def search_channels(self, query: str, limit: int = 10) -> List[Dict]:
        """البحث عن القنوات"""
        try:
            search_url = f"{self.api_base}/search/channels"
            response = self.session.get(search_url, params={
                "query": query,
                "limit": limit
            })
            
            if response.status_code == 200:
                return response.json().get("data", [])
            return []
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن القنوات: {str(e)}")
            return []
    
    def get_trending_streams(self, limit: int = 10) -> List[ContentItem]:
        """الحصول على البث المباشر الرائج"""
        try:
            trending_url = f"{self.api_base}/channels/live"
            response = self.session.get(trending_url, params={"limit": limit})
            
            if response.status_code != 200:
                return []
            
            trending_data = response.json()
            trending_streams = []
            
            for stream_data in trending_data.get("data", []):
                if self._is_channel_live(stream_data):
                    username = stream_data.get("slug", "")
                    if username:
                        stream_item = self._create_live_stream_item(stream_data, username)
                        if stream_item:
                            trending_streams.append(stream_item)
            
            return trending_streams
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على البث الرائج: {str(e)}")
            return []
    
    def get_platform_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات خاصة بـ Kick"""
        base_stats = self.get_stats()
        base_stats.update({
            "monitored_users_count": len(self.monitored_users),
            "monitored_users": self.monitored_users,
            "active_live_streams": len(self.active_live_streams),
            "known_content": len(self.known_content_ids)
        })
        return base_stats
