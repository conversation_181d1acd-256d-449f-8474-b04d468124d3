#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام المصادقة المتقدم
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent / "src"))

from security.advanced_authentication import (
    AdvancedAuthenticationSystem,
    AuthenticationMethod,
    SecurityLevel,
    SessionStatus
)

def test_basic_authentication():
    """اختبار المصادقة الأساسية"""
    print("🔐 اختبار المصادقة الأساسية...")
    
    # إنشاء مجلد مؤقت للاختبار
    with tempfile.TemporaryDirectory() as temp_dir:
        # إنشاء نظام المصادقة
        auth_system = AdvancedAuthenticationSystem(base_dir=temp_dir)
        
        # اختبار إنشاء مستخدم
        print("📝 اختبار إنشاء مستخدم...")
        success, user_id = auth_system.create_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPass123!",
            security_level=SecurityLevel.MEDIUM
        )
        
        if success:
            print(f"✅ تم إنشاء المستخدم بنجاح: {user_id}")
        else:
            print(f"❌ فشل إنشاء المستخدم: {user_id}")
            return False
        
        # اختبار تسجيل الدخول
        print("🔑 اختبار تسجيل الدخول...")
        success, message, session_id = auth_system.authenticate_user(
            username="testuser",
            password="TestPass123!",
            ip_address="127.0.0.1",
            user_agent="Test Client"
        )
        
        if success:
            print(f"✅ تم تسجيل الدخول بنجاح: {session_id}")
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        # اختبار التحقق من الجلسة
        print("🔍 اختبار التحقق من الجلسة...")
        is_valid, user, error = auth_system.validate_session(
            session_id=session_id,
            ip_address="127.0.0.1"
        )
        
        if is_valid and user:
            print(f"✅ الجلسة صالحة للمستخدم: {user.username}")
        else:
            print(f"❌ الجلسة غير صالحة: {error}")
            return False
        
        # اختبار تسجيل الخروج
        print("🚪 اختبار تسجيل الخروج...")
        success, message = auth_system.logout_session(session_id)
        
        if success:
            print(f"✅ تم تسجيل الخروج بنجاح: {message}")
        else:
            print(f"❌ فشل تسجيل الخروج: {message}")
            return False
        
        # إيقاف النظام
        auth_system.shutdown()
        
        print("✅ اكتمل اختبار المصادقة الأساسية بنجاح!")
        return True

def test_password_management():
    """اختبار إدارة كلمات المرور"""
    print("\n🔒 اختبار إدارة كلمات المرور...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        auth_system = AdvancedAuthenticationSystem(base_dir=temp_dir)
        
        # إنشاء مستخدم
        success, user_id = auth_system.create_user(
            username="passtest",
            email="<EMAIL>",
            password="OldPass123!",
            security_level=SecurityLevel.HIGH
        )
        
        if not success:
            print(f"❌ فشل إنشاء المستخدم: {user_id}")
            return False
        
        # اختبار تغيير كلمة المرور
        print("🔄 اختبار تغيير كلمة المرور...")
        success, message = auth_system.change_password(
            user_id=user_id,
            old_password="OldPass123!",
            new_password="NewPass456!"
        )
        
        if success:
            print(f"✅ تم تغيير كلمة المرور: {message}")
        else:
            print(f"❌ فشل تغيير كلمة المرور: {message}")
            return False
        
        # اختبار تسجيل الدخول بكلمة المرور الجديدة
        print("🔑 اختبار تسجيل الدخول بكلمة المرور الجديدة...")
        success, message, session_id = auth_system.authenticate_user(
            username="passtest",
            password="NewPass456!",
            ip_address="127.0.0.1"
        )
        
        if success:
            print(f"✅ تم تسجيل الدخول بكلمة المرور الجديدة: {session_id}")
        else:
            print(f"❌ فشل تسجيل الدخول بكلمة المرور الجديدة: {message}")
            return False
        
        # اختبار كلمة مرور ضعيفة
        print("⚠️ اختبار كلمة مرور ضعيفة...")
        success, message = auth_system.change_password(
            user_id=user_id,
            old_password="NewPass456!",
            new_password="123"
        )
        
        if not success and "ضعيفة" in message:
            print(f"✅ تم رفض كلمة المرور الضعيفة: {message}")
        else:
            print(f"❌ لم يتم رفض كلمة المرور الضعيفة: {message}")
            return False
        
        auth_system.shutdown()
        print("✅ اكتمل اختبار إدارة كلمات المرور بنجاح!")
        return True

def test_session_management():
    """اختبار إدارة الجلسات"""
    print("\n🕐 اختبار إدارة الجلسات...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        auth_system = AdvancedAuthenticationSystem(base_dir=temp_dir)
        
        # إنشاء مستخدم
        success, user_id = auth_system.create_user(
            username="sessiontest",
            email="<EMAIL>",
            password="SessionPass123!",
            security_level=SecurityLevel.MEDIUM
        )
        
        if not success:
            print(f"❌ فشل إنشاء المستخدم: {user_id}")
            return False
        
        # إنشاء عدة جلسات
        sessions = []
        for i in range(3):
            success, message, session_id = auth_system.authenticate_user(
                username="sessiontest",
                password="SessionPass123!",
                ip_address=f"192.168.1.{i+1}",
                user_agent=f"Client {i+1}"
            )
            
            if success:
                sessions.append(session_id)
                print(f"✅ تم إنشاء الجلسة {i+1}: {session_id}")
            else:
                print(f"❌ فشل إنشاء الجلسة {i+1}: {message}")
                return False
        
        # اختبار الحصول على جلسات المستخدم
        print("📋 اختبار الحصول على جلسات المستخدم...")
        user_sessions = auth_system.get_user_sessions(user_id)
        
        if len(user_sessions) == 3:
            print(f"✅ تم العثور على {len(user_sessions)} جلسة للمستخدم")
        else:
            print(f"❌ عدد الجلسات غير صحيح: {len(user_sessions)}")
            return False
        
        # اختبار إنهاء جميع الجلسات ما عدا الأولى
        print("🚪 اختبار إنهاء جميع الجلسات...")
        success, message, count = auth_system.logout_all_sessions(
            user_id=user_id,
            except_session=sessions[0]
        )
        
        if success and count == 2:
            print(f"✅ تم إنهاء {count} جلسة: {message}")
        else:
            print(f"❌ فشل إنهاء الجلسات: {message}")
            return False
        
        auth_system.shutdown()
        print("✅ اكتمل اختبار إدارة الجلسات بنجاح!")
        return True

def test_statistics_and_reporting():
    """اختبار الإحصائيات والتقارير"""
    print("\n📊 اختبار الإحصائيات والتقارير...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        auth_system = AdvancedAuthenticationSystem(base_dir=temp_dir)
        
        # إنشاء مستخدم وتسجيل عدة محاولات
        success, user_id = auth_system.create_user(
            username="statstest",
            email="<EMAIL>",
            password="StatsPass123!",
            security_level=SecurityLevel.HIGH
        )
        
        if not success:
            print(f"❌ فشل إنشاء المستخدم: {user_id}")
            return False
        
        # محاولات ناجحة وفاشلة
        for i in range(5):
            # محاولة ناجحة
            auth_system.authenticate_user(
                username="statstest",
                password="StatsPass123!",
                ip_address="127.0.0.1"
            )
            
            # محاولة فاشلة
            auth_system.authenticate_user(
                username="statstest",
                password="WrongPassword",
                ip_address="127.0.0.1"
            )
        
        # اختبار الإحصائيات
        print("📈 اختبار الحصول على الإحصائيات...")
        stats = auth_system.get_authentication_statistics(days=1)
        
        if stats and stats.get("total_attempts", 0) > 0:
            print(f"✅ تم الحصول على الإحصائيات: {stats['total_attempts']} محاولة")
            print(f"   - ناجحة: {stats['successful_attempts']}")
            print(f"   - فاشلة: {stats['failed_attempts']}")
            print(f"   - معدل النجاح: {stats['success_rate']}%")
        else:
            print("❌ فشل الحصول على الإحصائيات")
            return False
        
        # اختبار تقرير الأمان
        print("🛡️ اختبار تقرير الأمان...")
        security_report = auth_system.get_security_report()
        
        if security_report and "security_score" in security_report:
            print(f"✅ تم إنشاء تقرير الأمان: نقاط الأمان {security_report['security_score']}")
            print(f"   - مستوى الأمان: {security_report['security_level']}")
            print(f"   - عدد المشاكل: {len(security_report.get('security_issues', []))}")
        else:
            print("❌ فشل إنشاء تقرير الأمان")
            return False
        
        # اختبار حالة النظام
        print("⚙️ اختبار حالة النظام...")
        system_status = auth_system.get_system_status()
        
        if system_status and "system_health" in system_status:
            print(f"✅ تم الحصول على حالة النظام: {system_status['system_health']}")
            print(f"   - المستخدمين: {system_status['users']['total']}")
            print(f"   - الجلسات النشطة: {system_status['sessions']['active']}")
        else:
            print("❌ فشل الحصول على حالة النظام")
            return False
        
        auth_system.shutdown()
        print("✅ اكتمل اختبار الإحصائيات والتقارير بنجاح!")
        return True

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام المصادقة المتقدم")
    print("=" * 50)
    
    tests = [
        test_basic_authentication,
        test_password_management,
        test_session_management,
        test_statistics_and_reporting
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ فشل الاختبار: {test.__name__}")
        except Exception as e:
            print(f"❌ خطأ في الاختبار {test.__name__}: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
