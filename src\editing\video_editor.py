#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر الفيديو التلقائي - Automated Video Editor
يقوم بمونتاج الفيديوهات تلقائياً بناءً على التحليل الذكي
"""

import logging
import os
import json
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import moviepy.editor as mp
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2

class EditingProject:
    """مشروع مونتاج"""
    
    def __init__(self, name: str, source_files: List[str]):
        self.name = name
        self.source_files = source_files
        self.clips = []
        self.audio_tracks = []
        self.effects = []
        self.transitions = []
        self.subtitles = []
        self.output_settings = {}
        self.created_at = datetime.now()
        self.duration = 0.0
        self.resolution = (1080, 1920)  # عمودي للتيك توك
        self.fps = 30
        self.project_dir = None
        self.temp_files = []
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "source_files": self.source_files,
            "clips_count": len(self.clips),
            "audio_tracks_count": len(self.audio_tracks),
            "effects_count": len(self.effects),
            "duration": self.duration,
            "resolution": self.resolution,
            "fps": self.fps,
            "created_at": self.created_at.isoformat()
        }

class VideoClip:
    """مقطع فيديو في المشروع"""
    
    def __init__(self, source_file: str, start_time: float, end_time: float):
        self.source_file = source_file
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.position_in_timeline = 0.0
        self.effects = []
        self.transitions = {"in": None, "out": None}
        self.volume = 1.0
        self.speed = 1.0
        self.crop = None
        self.resize = None
        self.rotation = 0
        self.filters = []
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "source_file": self.source_file,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": self.duration,
            "position_in_timeline": self.position_in_timeline,
            "volume": self.volume,
            "speed": self.speed,
            "rotation": self.rotation
        }

class VideoEditor:
    """محرر الفيديو التلقائي"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # تحميل إعدادات المونتاج
        self.settings = self._load_editing_settings()
        
        # مجلدات العمل
        self.projects_dir = Path("data/editing_projects")
        self.templates_dir = Path("data/editing_templates")
        self.assets_dir = Path("data/editing_assets")
        self.output_dir = Path("data/edited_videos")
        
        # إنشاء المجلدات
        for directory in [self.projects_dir, self.templates_dir, self.assets_dir, self.output_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # تحميل الأصول
        self._load_editing_assets()
        
        # إحصائيات
        self.stats = {
            "projects_created": 0,
            "videos_edited": 0,
            "total_editing_time": 0.0,
            "successful_exports": 0,
            "failed_exports": 0
        }
    
    def _load_editing_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المونتاج"""
        try:
            return self.config_manager.get_setting("editing_settings", "video_editor", {
                "default_resolution": [1080, 1920],  # عمودي
                "default_fps": 30,
                "default_bitrate": "5000k",
                "default_audio_bitrate": "192k",
                "max_video_duration": 60.0,  # ثانية
                "min_clip_duration": 1.0,
                "transition_duration": 0.5,
                "fade_duration": 0.3,
                "auto_color_correction": True,
                "auto_audio_normalization": True,
                "auto_stabilization": False,
                "watermark_enabled": False,
                "watermark_position": "bottom_right",
                "watermark_opacity": 0.7,
                "export_format": "mp4",
                "export_quality": "high",
                "parallel_processing": True,
                "temp_cleanup": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المونتاج: {str(e)}")
            return {
                "default_resolution": [1080, 1920],
                "default_fps": 30,
                "default_bitrate": "5000k",
                "default_audio_bitrate": "192k",
                "max_video_duration": 60.0,
                "min_clip_duration": 1.0,
                "transition_duration": 0.5,
                "fade_duration": 0.3,
                "auto_color_correction": True,
                "auto_audio_normalization": True,
                "auto_stabilization": False,
                "watermark_enabled": False,
                "watermark_position": "bottom_right",
                "watermark_opacity": 0.7,
                "export_format": "mp4",
                "export_quality": "high",
                "parallel_processing": True,
                "temp_cleanup": True
            }
    
    def _load_editing_assets(self):
        """تحميل أصول المونتاج"""
        try:
            # إنشاء مجلدات الأصول
            (self.assets_dir / "music").mkdir(exist_ok=True)
            (self.assets_dir / "sound_effects").mkdir(exist_ok=True)
            (self.assets_dir / "fonts").mkdir(exist_ok=True)
            (self.assets_dir / "overlays").mkdir(exist_ok=True)
            (self.assets_dir / "transitions").mkdir(exist_ok=True)
            
            # تحميل قائمة الموسيقى المتاحة
            self.background_music = list((self.assets_dir / "music").glob("*.mp3"))
            self.sound_effects = list((self.assets_dir / "sound_effects").glob("*.wav"))
            self.fonts = list((self.assets_dir / "fonts").glob("*.ttf"))
            
            self.logger.info(f"تم تحميل {len(self.background_music)} ملف موسيقى")
            self.logger.info(f"تم تحميل {len(self.sound_effects)} مؤثر صوتي")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل أصول المونتاج: {str(e)}")
            self.background_music = []
            self.sound_effects = []
            self.fonts = []
    
    def create_project(self, name: str, source_files: List[str]) -> EditingProject:
        """إنشاء مشروع مونتاج جديد"""
        try:
            self.logger.info(f"إنشاء مشروع مونتاج جديد: {name}")
            
            # إنشاء المشروع
            project = EditingProject(name, source_files)
            
            # إنشاء مجلد المشروع
            project_name_safe = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            timestamp = int(datetime.now().timestamp())
            project_dir_name = f"{project_name_safe}_{timestamp}"
            project.project_dir = self.projects_dir / project_dir_name
            project.project_dir.mkdir(exist_ok=True)
            
            # تحديد الإعدادات الافتراضية
            project.resolution = tuple(self.settings.get("default_resolution", [1080, 1920]))
            project.fps = self.settings.get("default_fps", 30)
            
            self.stats["projects_created"] += 1
            
            self.logger.info(f"تم إنشاء المشروع: {project.project_dir}")
            return project
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المشروع: {str(e)}")
            raise
    
    def auto_edit_video(self, source_files: List[str], 
                       ai_analysis: Dict[str, Any] = None,
                       template_name: str = "default") -> str:
        """مونتاج تلقائي للفيديو"""
        try:
            self.logger.info(f"بدء المونتاج التلقائي لـ {len(source_files)} ملف")
            
            # إنشاء مشروع
            project_name = f"auto_edit_{int(datetime.now().timestamp())}"
            project = self.create_project(project_name, source_files)
            
            # تحليل الملفات المصدر
            clips_data = self._analyze_source_files(source_files, ai_analysis)
            
            # إنشاء المقاطع
            project.clips = self._create_clips_from_analysis(clips_data, project)
            
            # ترتيب المقاطع
            self._arrange_clips_timeline(project)
            
            # إضافة الانتقالات
            self._add_transitions(project)
            
            # إضافة الموسيقى
            self._add_background_music(project)
            
            # إضافة النصوص والترجمة
            self._add_subtitles(project, ai_analysis)
            
            # إضافة المؤثرات
            self._add_effects(project)
            
            # تصدير الفيديو
            output_path = self._export_video(project)
            
            # تنظيف الملفات المؤقتة
            if self.settings.get("temp_cleanup", True):
                self._cleanup_temp_files(project)
            
            self.stats["videos_edited"] += 1
            self.stats["successful_exports"] += 1
            
            self.logger.info(f"انتهى المونتاج التلقائي: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"خطأ في المونتاج التلقائي: {str(e)}")
            self.stats["failed_exports"] += 1
            raise
    
    def _analyze_source_files(self, source_files: List[str], 
                             ai_analysis: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """تحليل الملفات المصدر"""
        clips_data = []
        
        try:
            for file_path in source_files:
                if not os.path.exists(file_path):
                    self.logger.warning(f"الملف غير موجود: {file_path}")
                    continue
                
                # معلومات أساسية عن الملف
                with mp.VideoFileClip(file_path) as video:
                    file_info = {
                        "file_path": file_path,
                        "duration": video.duration,
                        "fps": video.fps,
                        "size": video.size,
                        "has_audio": video.audio is not None
                    }
                
                # إضافة تحليل الذكاء الاصطناعي إذا كان متاحاً
                if ai_analysis:
                    # البحث عن تحليل هذا الملف
                    file_analysis = None
                    for analysis in ai_analysis.get("files", []):
                        if analysis.get("file_path") == file_path:
                            file_analysis = analysis
                            break
                    
                    if file_analysis:
                        file_info["ai_analysis"] = file_analysis
                        file_info["interesting_moments"] = file_analysis.get("interesting_moments", [])
                        file_info["quality_score"] = file_analysis.get("quality_score", 0.5)
                
                clips_data.append(file_info)
            
            return clips_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الملفات المصدر: {str(e)}")
            return []
    
    def _create_clips_from_analysis(self, clips_data: List[Dict[str, Any]], 
                                   project: EditingProject) -> List[VideoClip]:
        """إنشاء المقاطع من التحليل"""
        clips = []
        
        try:
            max_duration = self.settings.get("max_video_duration", 60.0)
            min_clip_duration = self.settings.get("min_clip_duration", 1.0)
            
            total_duration = 0.0
            
            for file_data in clips_data:
                file_path = file_data["file_path"]
                
                # إذا كان هناك لحظات مثيرة من التحليل الذكي
                if "interesting_moments" in file_data and file_data["interesting_moments"]:
                    for moment in file_data["interesting_moments"]:
                        if total_duration >= max_duration:
                            break
                        
                        start_time = moment.get("start_time", 0.0)
                        end_time = moment.get("end_time", start_time + 5.0)
                        duration = end_time - start_time
                        
                        # التأكد من الحد الأدنى للمدة
                        if duration < min_clip_duration:
                            end_time = start_time + min_clip_duration
                        
                        # التأكد من عدم تجاوز مدة الملف
                        if end_time > file_data["duration"]:
                            end_time = file_data["duration"]
                            start_time = max(0, end_time - min_clip_duration)
                        
                        # التأكد من عدم تجاوز المدة الإجمالية
                        remaining_time = max_duration - total_duration
                        if end_time - start_time > remaining_time:
                            end_time = start_time + remaining_time
                        
                        clip = VideoClip(file_path, start_time, end_time)
                        clip.position_in_timeline = total_duration
                        clips.append(clip)
                        
                        total_duration += clip.duration
                
                else:
                    # إذا لم يكن هناك تحليل ذكي، استخدم الملف كاملاً أو جزء منه
                    if total_duration >= max_duration:
                        break
                    
                    file_duration = file_data["duration"]
                    remaining_time = max_duration - total_duration
                    
                    # استخدم الملف كاملاً أو الوقت المتبقي
                    clip_duration = min(file_duration, remaining_time)
                    
                    if clip_duration >= min_clip_duration:
                        clip = VideoClip(file_path, 0.0, clip_duration)
                        clip.position_in_timeline = total_duration
                        clips.append(clip)
                        
                        total_duration += clip_duration
            
            project.duration = total_duration
            
            self.logger.info(f"تم إنشاء {len(clips)} مقطع بمدة إجمالية {total_duration:.2f} ثانية")
            return clips
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المقاطع: {str(e)}")
            return []
    
    def _arrange_clips_timeline(self, project: EditingProject):
        """ترتيب المقاطع في الخط الزمني"""
        try:
            # ترتيب المقاطع حسب الجودة والإثارة
            def clip_score(clip):
                # نقاط افتراضية
                score = 0.5
                
                # إذا كان هناك تحليل ذكي، استخدمه
                for file_data in project.source_files:
                    if hasattr(file_data, 'get') and file_data.get("file_path") == clip.source_file:
                        score = file_data.get("quality_score", 0.5)
                        break
                
                return score
            
            # ترتيب المقاطع حسب النقاط (الأفضل أولاً)
            project.clips.sort(key=clip_score, reverse=True)
            
            # إعادة حساب المواضع في الخط الزمني
            current_time = 0.0
            for clip in project.clips:
                clip.position_in_timeline = current_time
                current_time += clip.duration
            
            project.duration = current_time
            
            self.logger.info(f"تم ترتيب {len(project.clips)} مقطع في الخط الزمني")
            
        except Exception as e:
            self.logger.error(f"خطأ في ترتيب المقاطع: {str(e)}")
    
    def _add_transitions(self, project: EditingProject):
        """إضافة انتقالات بين المقاطع"""
        try:
            transition_duration = self.settings.get("transition_duration", 0.5)
            
            for i, clip in enumerate(project.clips):
                if i > 0:  # ليس المقطع الأول
                    clip.transitions["in"] = {
                        "type": "crossfade",
                        "duration": transition_duration
                    }
                
                if i < len(project.clips) - 1:  # ليس المقطع الأخير
                    clip.transitions["out"] = {
                        "type": "crossfade", 
                        "duration": transition_duration
                    }
            
            self.logger.info(f"تم إضافة انتقالات لـ {len(project.clips)} مقطع")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الانتقالات: {str(e)}")
    
    def _add_background_music(self, project: EditingProject):
        """إضافة موسيقى خلفية"""
        try:
            if not self.background_music:
                self.logger.warning("لا توجد ملفات موسيقى متاحة")
                return
            
            # اختيار موسيقى عشوائية
            import random
            music_file = random.choice(self.background_music)
            
            # إضافة المسار الصوتي
            audio_track = {
                "file_path": str(music_file),
                "start_time": 0.0,
                "duration": project.duration,
                "volume": 0.3,  # مستوى منخفض للخلفية
                "fade_in": 1.0,
                "fade_out": 1.0,
                "loop": True
            }
            
            project.audio_tracks.append(audio_track)
            
            self.logger.info(f"تم إضافة موسيقى خلفية: {music_file.name}")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الموسيقى: {str(e)}")
    
    def _add_subtitles(self, project: EditingProject, ai_analysis: Dict[str, Any] = None):
        """إضافة ترجمة ونصوص"""
        try:
            if not ai_analysis:
                return
            
            # البحث عن النصوص المستخرجة من التحليل الصوتي
            for file_analysis in ai_analysis.get("files", []):
                transcription = file_analysis.get("audio_analysis", {}).get("transcription", {})
                
                if transcription and "segments" in transcription:
                    for segment in transcription["segments"]:
                        subtitle = {
                            "text": segment.get("text", ""),
                            "start_time": segment.get("start", 0.0),
                            "end_time": segment.get("end", 0.0),
                            "style": {
                                "font_size": 24,
                                "font_color": "white",
                                "background_color": "black",
                                "position": "bottom",
                                "alignment": "center"
                            }
                        }
                        
                        project.subtitles.append(subtitle)
            
            self.logger.info(f"تم إضافة {len(project.subtitles)} ترجمة")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الترجمة: {str(e)}")
    
    def _add_effects(self, project: EditingProject):
        """إضافة مؤثرات بصرية"""
        try:
            # إضافة تأثيرات أساسية
            for clip in project.clips:
                # تصحيح الألوان التلقائي
                if self.settings.get("auto_color_correction", True):
                    clip.effects.append({
                        "type": "color_correction",
                        "brightness": 0.1,
                        "contrast": 0.1,
                        "saturation": 0.05
                    })
                
                # تحسين الحدة
                clip.effects.append({
                    "type": "sharpen",
                    "strength": 0.3
                })
            
            self.logger.info(f"تم إضافة مؤثرات لـ {len(project.clips)} مقطع")
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المؤثرات: {str(e)}")
    
    def _export_video(self, project: EditingProject) -> str:
        """تصدير الفيديو النهائي"""
        try:
            self.logger.info("بدء تصدير الفيديو النهائي")
            
            # إنشاء اسم ملف الإخراج
            timestamp = int(datetime.now().timestamp())
            output_filename = f"{project.name}_{timestamp}.{self.settings.get('export_format', 'mp4')}"
            output_path = self.output_dir / output_filename
            
            # تحميل وتجميع المقاطع
            video_clips = []
            
            for clip in project.clips:
                # تحميل المقطع
                video_clip = mp.VideoFileClip(clip.source_file).subclip(clip.start_time, clip.end_time)
                
                # تطبيق التأثيرات
                video_clip = self._apply_clip_effects(video_clip, clip)
                
                # تطبيق الانتقالات
                video_clip = self._apply_transitions(video_clip, clip)
                
                video_clips.append(video_clip)
            
            # تجميع المقاطع
            if video_clips:
                final_video = mp.concatenate_videoclips(video_clips, method="compose")
                
                # تطبيق الدقة المطلوبة
                final_video = final_video.resize(project.resolution)
                
                # إضافة الموسيقى
                if project.audio_tracks:
                    final_video = self._add_audio_tracks(final_video, project)
                
                # إضافة الترجمة
                if project.subtitles:
                    final_video = self._add_subtitle_overlay(final_video, project)
                
                # تحديد إعدادات التصدير
                export_settings = self._get_export_settings()
                
                # تصدير الفيديو
                final_video.write_videofile(
                    str(output_path),
                    **export_settings,
                    verbose=False,
                    logger=None
                )
                
                # إغلاق المقاطع
                for clip in video_clips:
                    clip.close()
                final_video.close()
                
                self.logger.info(f"تم تصدير الفيديو: {output_path}")
                return str(output_path)
            
            else:
                raise ValueError("لا توجد مقاطع للتصدير")
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير الفيديو: {str(e)}")
            raise

    def _apply_clip_effects(self, video_clip, clip: VideoClip):
        """تطبيق التأثيرات على المقطع"""
        try:
            # تطبيق السرعة
            if clip.speed != 1.0:
                video_clip = video_clip.fx(mp.speedx, clip.speed)

            # تطبيق الدوران
            if clip.rotation != 0:
                video_clip = video_clip.rotate(clip.rotation)

            # تطبيق القص
            if clip.crop:
                x1, y1, x2, y2 = clip.crop
                video_clip = video_clip.crop(x1=x1, y1=y1, x2=x2, y2=y2)

            # تطبيق تغيير الحجم
            if clip.resize:
                video_clip = video_clip.resize(clip.resize)

            # تطبيق مستوى الصوت
            if video_clip.audio and clip.volume != 1.0:
                video_clip = video_clip.volumex(clip.volume)

            # تطبيق التأثيرات المخصصة
            for effect in clip.effects:
                video_clip = self._apply_single_effect(video_clip, effect)

            return video_clip

        except Exception as e:
            self.logger.error(f"خطأ في تطبيق التأثيرات: {str(e)}")
            return video_clip

    def _apply_single_effect(self, video_clip, effect: Dict[str, Any]):
        """تطبيق تأثير واحد"""
        try:
            effect_type = effect.get("type", "")

            if effect_type == "color_correction":
                # تصحيح الألوان
                brightness = effect.get("brightness", 0.0)
                contrast = effect.get("contrast", 0.0)
                saturation = effect.get("saturation", 0.0)

                if brightness != 0.0:
                    video_clip = video_clip.fx(mp.colorx, 1 + brightness)

                # ملاحظة: MoviePy لا يدعم تعديل التباين والتشبع مباشرة
                # يمكن استخدام مكتبات أخرى مثل OpenCV للتأثيرات المتقدمة

            elif effect_type == "sharpen":
                # تحسين الحدة - يتطلب تطبيق مخصص
                pass

            elif effect_type == "blur":
                # تشويش
                strength = effect.get("strength", 1.0)
                video_clip = video_clip.fx(mp.blur, strength)

            elif effect_type == "fade_in":
                duration = effect.get("duration", 1.0)
                video_clip = video_clip.fadein(duration)

            elif effect_type == "fade_out":
                duration = effect.get("duration", 1.0)
                video_clip = video_clip.fadeout(duration)

            return video_clip

        except Exception as e:
            self.logger.error(f"خطأ في تطبيق التأثير {effect.get('type', 'unknown')}: {str(e)}")
            return video_clip

    def _apply_transitions(self, video_clip, clip: VideoClip):
        """تطبيق الانتقالات"""
        try:
            # انتقال الدخول
            if clip.transitions.get("in"):
                transition = clip.transitions["in"]
                if transition["type"] == "crossfade":
                    duration = transition.get("duration", 0.5)
                    video_clip = video_clip.fadein(duration)

            # انتقال الخروج
            if clip.transitions.get("out"):
                transition = clip.transitions["out"]
                if transition["type"] == "crossfade":
                    duration = transition.get("duration", 0.5)
                    video_clip = video_clip.fadeout(duration)

            return video_clip

        except Exception as e:
            self.logger.error(f"خطأ في تطبيق الانتقالات: {str(e)}")
            return video_clip

    def _add_audio_tracks(self, video_clip, project: EditingProject):
        """إضافة المسارات الصوتية"""
        try:
            audio_clips = []

            # الاحتفاظ بالصوت الأصلي
            if video_clip.audio:
                audio_clips.append(video_clip.audio)

            # إضافة المسارات الإضافية
            for track in project.audio_tracks:
                audio_clip = mp.AudioFileClip(track["file_path"])

                # تطبيق المدة
                if track.get("duration") and track["duration"] < audio_clip.duration:
                    audio_clip = audio_clip.subclip(0, track["duration"])

                # تطبيق التكرار إذا كان مطلوباً
                if track.get("loop", False) and audio_clip.duration < project.duration:
                    loops_needed = int(project.duration / audio_clip.duration) + 1
                    audio_clip = mp.concatenate_audioclips([audio_clip] * loops_needed)
                    audio_clip = audio_clip.subclip(0, project.duration)

                # تطبيق مستوى الصوت
                if track.get("volume", 1.0) != 1.0:
                    audio_clip = audio_clip.volumex(track["volume"])

                # تطبيق التلاشي
                if track.get("fade_in", 0.0) > 0:
                    audio_clip = audio_clip.fadein(track["fade_in"])

                if track.get("fade_out", 0.0) > 0:
                    audio_clip = audio_clip.fadeout(track["fade_out"])

                audio_clips.append(audio_clip)

            # دمج جميع المسارات الصوتية
            if len(audio_clips) > 1:
                final_audio = mp.CompositeAudioClip(audio_clips)
                video_clip = video_clip.set_audio(final_audio)

            return video_clip

        except Exception as e:
            self.logger.error(f"خطأ في إضافة المسارات الصوتية: {str(e)}")
            return video_clip

    def _add_subtitle_overlay(self, video_clip, project: EditingProject):
        """إضافة الترجمة كطبقة علوية"""
        try:
            subtitle_clips = []

            for subtitle in project.subtitles:
                # إنشاء نص
                txt_clip = mp.TextClip(
                    subtitle["text"],
                    fontsize=subtitle["style"].get("font_size", 24),
                    color=subtitle["style"].get("font_color", "white"),
                    font="Arial",  # يمكن تخصيص الخط
                    stroke_color=subtitle["style"].get("background_color", "black"),
                    stroke_width=2
                ).set_start(subtitle["start_time"]).set_end(subtitle["end_time"])

                # تحديد الموضع
                position = subtitle["style"].get("position", "bottom")
                if position == "bottom":
                    txt_clip = txt_clip.set_position(("center", "bottom"))
                elif position == "top":
                    txt_clip = txt_clip.set_position(("center", "top"))
                else:
                    txt_clip = txt_clip.set_position("center")

                subtitle_clips.append(txt_clip)

            # إضافة الترجمة للفيديو
            if subtitle_clips:
                video_clip = mp.CompositeVideoClip([video_clip] + subtitle_clips)

            return video_clip

        except Exception as e:
            self.logger.error(f"خطأ في إضافة الترجمة: {str(e)}")
            return video_clip

    def _get_export_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات التصدير"""
        quality = self.settings.get("export_quality", "high")

        settings = {
            "fps": self.settings.get("default_fps", 30),
            "codec": "libx264",
            "audio_codec": "aac",
            "temp_audiofile": "temp-audio.m4a",
            "remove_temp": True
        }

        if quality == "high":
            settings.update({
                "bitrate": self.settings.get("default_bitrate", "5000k"),
                "audio_bitrate": self.settings.get("default_audio_bitrate", "192k")
            })
        elif quality == "medium":
            settings.update({
                "bitrate": "3000k",
                "audio_bitrate": "128k"
            })
        else:  # low
            settings.update({
                "bitrate": "1500k",
                "audio_bitrate": "96k"
            })

        return settings

    def _cleanup_temp_files(self, project: EditingProject):
        """تنظيف الملفات المؤقتة"""
        try:
            for temp_file in project.temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            project.temp_files.clear()
            self.logger.info("تم تنظيف الملفات المؤقتة")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")

    def get_editing_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المونتاج"""
        return {
            "projects_created": self.stats["projects_created"],
            "videos_edited": self.stats["videos_edited"],
            "total_editing_time": self.stats["total_editing_time"],
            "successful_exports": self.stats["successful_exports"],
            "failed_exports": self.stats["failed_exports"],
            "success_rate": (
                self.stats["successful_exports"] /
                max(1, self.stats["successful_exports"] + self.stats["failed_exports"])
            ) * 100,
            "available_music_tracks": len(self.background_music),
            "available_sound_effects": len(self.sound_effects)
        }

    def save_project(self, project: EditingProject) -> str:
        """حفظ المشروع"""
        try:
            project_file = project.project_dir / "project.json"

            project_data = {
                "name": project.name,
                "source_files": project.source_files,
                "clips": [clip.to_dict() for clip in project.clips],
                "audio_tracks": project.audio_tracks,
                "effects": project.effects,
                "transitions": project.transitions,
                "subtitles": project.subtitles,
                "output_settings": project.output_settings,
                "created_at": project.created_at.isoformat(),
                "duration": project.duration,
                "resolution": project.resolution,
                "fps": project.fps
            }

            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم حفظ المشروع: {project_file}")
            return str(project_file)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ المشروع: {str(e)}")
            raise

    def load_project(self, project_file: str) -> EditingProject:
        """تحميل مشروع محفوظ"""
        try:
            with open(project_file, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            project = EditingProject(
                project_data["name"],
                project_data["source_files"]
            )

            # استعادة البيانات
            project.duration = project_data.get("duration", 0.0)
            project.resolution = tuple(project_data.get("resolution", [1080, 1920]))
            project.fps = project_data.get("fps", 30)
            project.audio_tracks = project_data.get("audio_tracks", [])
            project.effects = project_data.get("effects", [])
            project.transitions = project_data.get("transitions", [])
            project.subtitles = project_data.get("subtitles", [])
            project.output_settings = project_data.get("output_settings", {})

            if "created_at" in project_data:
                project.created_at = datetime.fromisoformat(project_data["created_at"])

            # استعادة المقاطع
            for clip_data in project_data.get("clips", []):
                clip = VideoClip(
                    clip_data["source_file"],
                    clip_data["start_time"],
                    clip_data["end_time"]
                )
                clip.position_in_timeline = clip_data.get("position_in_timeline", 0.0)
                clip.volume = clip_data.get("volume", 1.0)
                clip.speed = clip_data.get("speed", 1.0)
                clip.rotation = clip_data.get("rotation", 0)

                project.clips.append(clip)

            project.project_dir = Path(project_file).parent

            self.logger.info(f"تم تحميل المشروع: {project.name}")
            return project

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المشروع: {str(e)}")
            raise
