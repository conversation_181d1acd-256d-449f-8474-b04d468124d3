#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الحماية من التلاعب - Tamper Protection System
نظام شامل لحماية الملفات والبيانات من التلاعب والتعديل غير المصرح به
"""

import os
import json
import hashlib
import hmac
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum

# مكتبات التشفير والتوقيع
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import load_pem_private_key, load_pem_public_key
from cryptography.hazmat.backends import default_backend
from cryptography.exceptions import InvalidSignature

class ProtectionLevel(Enum):
    """مستويات الحماية"""
    BASIC = "basic"           # حماية أساسية بـ hash
    STANDARD = "standard"     # حماية قياسية بـ HMAC
    ADVANCED = "advanced"     # حماية متقدمة بالتوقيع الرقمي
    CRITICAL = "critical"     # حماية حرجة متعددة الطبقات

class IntegrityStatus(Enum):
    """حالة سلامة الملف"""
    INTACT = "intact"         # سليم
    MODIFIED = "modified"     # تم تعديله
    CORRUPTED = "corrupted"   # تالف
    MISSING = "missing"       # مفقود
    UNKNOWN = "unknown"       # غير معروف

@dataclass
class FileIntegrity:
    """معلومات سلامة الملف"""
    file_path: str
    file_size: int
    modification_time: float
    creation_time: float
    hash_sha256: str
    hash_md5: str
    hmac_signature: Optional[str] = None
    digital_signature: Optional[bytes] = None
    protection_level: ProtectionLevel = ProtectionLevel.BASIC
    created_at: datetime = None
    last_verified: datetime = None
    verification_count: int = 0
    status: IntegrityStatus = IntegrityStatus.UNKNOWN
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_verified is None:
            self.last_verified = datetime.now()

@dataclass
class TamperAlert:
    """تنبيه التلاعب"""
    alert_id: str
    file_path: str
    alert_type: str
    severity: str
    description: str
    detected_at: datetime
    original_integrity: FileIntegrity
    current_state: Dict[str, Any]
    action_taken: Optional[str] = None
    resolved: bool = False

class TamperProtectionSystem:
    """نظام الحماية من التلاعب"""
    
    def __init__(self, base_dir: str = "data"):
        """تهيئة نظام الحماية من التلاعب"""
        self.base_dir = Path(base_dir)
        self.security_dir = self.base_dir / "security"
        self.integrity_dir = self.security_dir / "integrity"
        self.signatures_dir = self.security_dir / "signatures"
        self.alerts_dir = self.security_dir / "alerts"
        
        # إنشاء المجلدات
        for directory in [self.security_dir, self.integrity_dir, self.signatures_dir, self.alerts_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # ملفات النظام
        self.integrity_db_file = self.integrity_dir / "integrity_database.json"
        self.alerts_file = self.alerts_dir / "tamper_alerts.json"
        self.config_file = self.security_dir / "tamper_config.json"
        self.private_key_file = self.signatures_dir / "private_key.pem"
        self.public_key_file = self.signatures_dir / "public_key.pem"
        
        # قواعد البيانات
        self.integrity_database: Dict[str, FileIntegrity] = {}
        self.alerts_database: List[TamperAlert] = []
        self.protected_files: Set[str] = set()
        
        # إعدادات النظام
        self.config = {
            "auto_scan_interval": 300,  # 5 دقائق
            "max_alerts": 1000,
            "alert_retention_days": 30,
            "default_protection_level": ProtectionLevel.STANDARD.value,
            "hmac_key": None,
            "auto_backup_on_change": True,
            "quarantine_suspicious_files": False,
            "real_time_monitoring": True
        }
        
        # إعداد نظام السجلات
        self.logger = logging.getLogger("TamperProtection")
        self.logger.setLevel(logging.INFO)
        
        # إعداد معالج السجلات
        log_file = self.security_dir / "tamper_protection.log"
        handler = logging.FileHandler(log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة النظام"""
        try:
            # تحميل الإعدادات
            self._load_config()
            
            # تحميل قاعدة بيانات السلامة
            self._load_integrity_database()
            
            # تحميل التنبيهات
            self._load_alerts()
            
            # إنشاء أو تحميل مفاتيح التوقيع الرقمي
            self._setup_digital_signature_keys()
            
            # إنشاء مفتاح HMAC إذا لم يكن موجوداً
            if not self.config.get("hmac_key"):
                self.config["hmac_key"] = self._generate_hmac_key()
                self._save_config()
            
            self.logger.info("تم تهيئة نظام الحماية من التلاعب بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة نظام الحماية من التلاعب: {str(e)}")
            raise
    
    def _load_config(self):
        """تحميل إعدادات النظام"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            self.logger.warning(f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def _save_config(self):
        """حفظ إعدادات النظام"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def _load_integrity_database(self):
        """تحميل قاعدة بيانات السلامة"""
        try:
            if self.integrity_db_file.exists():
                with open(self.integrity_db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for file_path, integrity_data in data.items():
                    # تحويل التواريخ من نص إلى datetime
                    if integrity_data.get("created_at"):
                        integrity_data["created_at"] = datetime.fromisoformat(integrity_data["created_at"])
                    if integrity_data.get("last_verified"):
                        integrity_data["last_verified"] = datetime.fromisoformat(integrity_data["last_verified"])
                    
                    # تحويل enum
                    if integrity_data.get("protection_level"):
                        integrity_data["protection_level"] = ProtectionLevel(integrity_data["protection_level"])
                    if integrity_data.get("status"):
                        integrity_data["status"] = IntegrityStatus(integrity_data["status"])
                    
                    self.integrity_database[file_path] = FileIntegrity(**integrity_data)
                    self.protected_files.add(file_path)
                    
        except Exception as e:
            self.logger.warning(f"خطأ في تحميل قاعدة بيانات السلامة: {str(e)}")
    
    def _save_integrity_database(self):
        """حفظ قاعدة بيانات السلامة"""
        try:
            data = {}
            for file_path, integrity in self.integrity_database.items():
                integrity_dict = asdict(integrity)
                # تحويل التواريخ إلى نص
                integrity_dict["created_at"] = integrity.created_at.isoformat()
                integrity_dict["last_verified"] = integrity.last_verified.isoformat()
                # تحويل enum إلى نص
                integrity_dict["protection_level"] = integrity.protection_level.value
                integrity_dict["status"] = integrity.status.value
                data[file_path] = integrity_dict
            
            with open(self.integrity_db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ قاعدة بيانات السلامة: {str(e)}")
    
    def _load_alerts(self):
        """تحميل التنبيهات"""
        try:
            if self.alerts_file.exists():
                with open(self.alerts_file, 'r', encoding='utf-8') as f:
                    alerts_data = json.load(f)
                    
                for alert_data in alerts_data:
                    # تحويل التواريخ
                    alert_data["detected_at"] = datetime.fromisoformat(alert_data["detected_at"])
                    
                    # تحويل FileIntegrity
                    if alert_data.get("original_integrity"):
                        orig_integrity = alert_data["original_integrity"]
                        orig_integrity["created_at"] = datetime.fromisoformat(orig_integrity["created_at"])
                        orig_integrity["last_verified"] = datetime.fromisoformat(orig_integrity["last_verified"])
                        orig_integrity["protection_level"] = ProtectionLevel(orig_integrity["protection_level"])
                        orig_integrity["status"] = IntegrityStatus(orig_integrity["status"])
                        alert_data["original_integrity"] = FileIntegrity(**orig_integrity)
                    
                    self.alerts_database.append(TamperAlert(**alert_data))
                    
        except Exception as e:
            self.logger.warning(f"خطأ في تحميل التنبيهات: {str(e)}")
    
    def _save_alerts(self):
        """حفظ التنبيهات"""
        try:
            alerts_data = []
            for alert in self.alerts_database:
                alert_dict = asdict(alert)
                # تحويل التواريخ
                alert_dict["detected_at"] = alert.detected_at.isoformat()
                
                # تحويل FileIntegrity
                if alert.original_integrity:
                    orig_integrity = asdict(alert.original_integrity)
                    orig_integrity["created_at"] = alert.original_integrity.created_at.isoformat()
                    orig_integrity["last_verified"] = alert.original_integrity.last_verified.isoformat()
                    orig_integrity["protection_level"] = alert.original_integrity.protection_level.value
                    orig_integrity["status"] = alert.original_integrity.status.value
                    alert_dict["original_integrity"] = orig_integrity
                
                alerts_data.append(alert_dict)
            
            with open(self.alerts_file, 'w', encoding='utf-8') as f:
                json.dump(alerts_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ التنبيهات: {str(e)}")
    
    def _generate_hmac_key(self) -> str:
        """إنتاج مفتاح HMAC"""
        import secrets
        return secrets.token_hex(32)
    
    def _setup_digital_signature_keys(self):
        """إعداد مفاتيح التوقيع الرقمي"""
        try:
            # التحقق من وجود المفاتيح
            if not self.private_key_file.exists() or not self.public_key_file.exists():
                self._generate_signature_keys()
            
            # تحميل المفاتيح
            with open(self.private_key_file, 'rb') as f:
                self.private_key = load_pem_private_key(f.read(), password=None, backend=default_backend())
            
            with open(self.public_key_file, 'rb') as f:
                self.public_key = load_pem_public_key(f.read(), backend=default_backend())
            
            self.logger.info("تم تحميل مفاتيح التوقيع الرقمي")
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد مفاتيح التوقيع الرقمي: {str(e)}")
            raise
    
    def _generate_signature_keys(self):
        """إنتاج مفاتيح التوقيع الرقمي"""
        try:
            # إنتاج مفتاح RSA
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
                backend=default_backend()
            )
            
            # الحصول على المفتاح العام
            public_key = private_key.public_key()
            
            # حفظ المفتاح الخاص
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            with open(self.private_key_file, 'wb') as f:
                f.write(private_pem)
            
            # حفظ المفتاح العام
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            with open(self.public_key_file, 'wb') as f:
                f.write(public_pem)
            
            # تعيين صلاحيات الملفات (قراءة فقط للمالك)
            os.chmod(self.private_key_file, 0o600)
            os.chmod(self.public_key_file, 0o644)
            
            self.logger.info("تم إنتاج مفاتيح التوقيع الرقمي الجديدة")
            
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج مفاتيح التوقيع الرقمي: {str(e)}")
            raise

    def protect_file(self, file_path: str, protection_level: ProtectionLevel = None) -> bool:
        """حماية ملف من التلاعب"""
        try:
            file_path = str(Path(file_path).resolve())

            if not Path(file_path).exists():
                self.logger.error(f"الملف غير موجود: {file_path}")
                return False

            if protection_level is None:
                protection_level = ProtectionLevel(self.config["default_protection_level"])

            # حساب معلومات سلامة الملف
            integrity = self._calculate_file_integrity(file_path, protection_level)

            # حفظ معلومات السلامة
            self.integrity_database[file_path] = integrity
            self.protected_files.add(file_path)

            # حفظ قاعدة البيانات
            self._save_integrity_database()

            self.logger.info(f"تم حماية الملف: {file_path} بمستوى {protection_level.value}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في حماية الملف {file_path}: {str(e)}")
            return False

    def _calculate_file_integrity(self, file_path: str, protection_level: ProtectionLevel) -> FileIntegrity:
        """حساب معلومات سلامة الملف"""
        try:
            file_path_obj = Path(file_path)

            # معلومات الملف الأساسية
            stat = file_path_obj.stat()
            file_size = stat.st_size
            modification_time = stat.st_mtime
            creation_time = stat.st_ctime

            # قراءة محتوى الملف
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # حساب hash SHA256
            hash_sha256 = hashlib.sha256(file_content).hexdigest()

            # حساب hash MD5
            hash_md5 = hashlib.md5(file_content).hexdigest()

            # إنشاء كائن FileIntegrity
            integrity = FileIntegrity(
                file_path=file_path,
                file_size=file_size,
                modification_time=modification_time,
                creation_time=creation_time,
                hash_sha256=hash_sha256,
                hash_md5=hash_md5,
                protection_level=protection_level,
                status=IntegrityStatus.INTACT
            )

            # إضافة HMAC للمستويات المتقدمة
            if protection_level in [ProtectionLevel.STANDARD, ProtectionLevel.ADVANCED, ProtectionLevel.CRITICAL]:
                hmac_key = self.config["hmac_key"].encode()
                hmac_signature = hmac.new(hmac_key, file_content, hashlib.sha256).hexdigest()
                integrity.hmac_signature = hmac_signature

            # إضافة التوقيع الرقمي للمستويات المتقدمة
            if protection_level in [ProtectionLevel.ADVANCED, ProtectionLevel.CRITICAL]:
                digital_signature = self._create_digital_signature(file_content)
                integrity.digital_signature = digital_signature

            return integrity

        except Exception as e:
            self.logger.error(f"خطأ في حساب سلامة الملف {file_path}: {str(e)}")
            raise

    def _create_digital_signature(self, data: bytes) -> bytes:
        """إنشاء توقيع رقمي للبيانات"""
        try:
            signature = self.private_key.sign(
                data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return signature

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء التوقيع الرقمي: {str(e)}")
            raise

    def verify_file_integrity(self, file_path: str) -> Tuple[IntegrityStatus, Dict[str, Any]]:
        """التحقق من سلامة الملف"""
        try:
            file_path = str(Path(file_path).resolve())

            if file_path not in self.integrity_database:
                return IntegrityStatus.UNKNOWN, {"error": "الملف غير محمي"}

            original_integrity = self.integrity_database[file_path]

            # التحقق من وجود الملف
            if not Path(file_path).exists():
                status = IntegrityStatus.MISSING
                self._create_tamper_alert(file_path, "file_missing", "high",
                                        "الملف المحمي مفقود", original_integrity, {})
                return status, {"error": "الملف مفقود"}

            # حساب السلامة الحالية
            try:
                current_integrity = self._calculate_file_integrity(file_path, original_integrity.protection_level)
            except Exception as e:
                status = IntegrityStatus.CORRUPTED
                self._create_tamper_alert(file_path, "file_corrupted", "high",
                                        f"الملف تالف: {str(e)}", original_integrity, {"error": str(e)})
                return status, {"error": f"الملف تالف: {str(e)}"}

            # مقارنة المعلومات
            verification_results = {
                "file_size_match": current_integrity.file_size == original_integrity.file_size,
                "sha256_match": current_integrity.hash_sha256 == original_integrity.hash_sha256,
                "md5_match": current_integrity.hash_md5 == original_integrity.hash_md5,
                "modification_time": current_integrity.modification_time,
                "original_modification_time": original_integrity.modification_time,
                "time_changed": current_integrity.modification_time != original_integrity.modification_time
            }

            # التحقق من HMAC
            if original_integrity.hmac_signature:
                verification_results["hmac_match"] = current_integrity.hmac_signature == original_integrity.hmac_signature

            # التحقق من التوقيع الرقمي
            if original_integrity.digital_signature:
                try:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()

                    self.public_key.verify(
                        original_integrity.digital_signature,
                        file_content,
                        padding.PSS(
                            mgf=padding.MGF1(hashes.SHA256()),
                            salt_length=padding.PSS.MAX_LENGTH
                        ),
                        hashes.SHA256()
                    )
                    verification_results["digital_signature_valid"] = True
                except InvalidSignature:
                    verification_results["digital_signature_valid"] = False
                except Exception as e:
                    verification_results["digital_signature_valid"] = False
                    verification_results["signature_error"] = str(e)

            # تحديد حالة السلامة
            if (verification_results["sha256_match"] and
                verification_results["md5_match"] and
                verification_results.get("hmac_match", True) and
                verification_results.get("digital_signature_valid", True)):

                status = IntegrityStatus.INTACT

                # تحديث معلومات التحقق
                original_integrity.last_verified = datetime.now()
                original_integrity.verification_count += 1
                original_integrity.status = status
                self._save_integrity_database()

            else:
                status = IntegrityStatus.MODIFIED

                # إنشاء تنبيه تلاعب
                self._create_tamper_alert(file_path, "file_modified", "medium",
                                        "تم تعديل الملف المحمي", original_integrity, verification_results)

            return status, verification_results

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من سلامة الملف {file_path}: {str(e)}")
            return IntegrityStatus.UNKNOWN, {"error": str(e)}

    def _create_tamper_alert(self, file_path: str, alert_type: str, severity: str,
                           description: str, original_integrity: FileIntegrity,
                           current_state: Dict[str, Any]):
        """إنشاء تنبيه تلاعب"""
        try:
            import uuid

            alert = TamperAlert(
                alert_id=str(uuid.uuid4()),
                file_path=file_path,
                alert_type=alert_type,
                severity=severity,
                description=description,
                detected_at=datetime.now(),
                original_integrity=original_integrity,
                current_state=current_state
            )

            self.alerts_database.append(alert)

            # حفظ التنبيهات
            self._save_alerts()

            # تنظيف التنبيهات القديمة
            self._cleanup_old_alerts()

            self.logger.warning(f"تنبيه تلاعب: {description} - الملف: {file_path}")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تنبيه التلاعب: {str(e)}")

    def _cleanup_old_alerts(self):
        """تنظيف التنبيهات القديمة"""
        try:
            retention_days = self.config["alert_retention_days"]
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            # إزالة التنبيهات القديمة
            self.alerts_database = [
                alert for alert in self.alerts_database
                if alert.detected_at > cutoff_date
            ]

            # الحد الأقصى للتنبيهات
            max_alerts = self.config["max_alerts"]
            if len(self.alerts_database) > max_alerts:
                # الاحتفاظ بأحدث التنبيهات
                self.alerts_database.sort(key=lambda x: x.detected_at, reverse=True)
                self.alerts_database = self.alerts_database[:max_alerts]

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف التنبيهات القديمة: {str(e)}")

    def scan_protected_files(self) -> Dict[str, Tuple[IntegrityStatus, Dict[str, Any]]]:
        """فحص جميع الملفات المحمية"""
        try:
            results = {}

            self.logger.info(f"بدء فحص {len(self.protected_files)} ملف محمي")

            for file_path in list(self.protected_files):
                status, details = self.verify_file_integrity(file_path)
                results[file_path] = (status, details)

            # إحصائيات الفحص
            intact_count = sum(1 for status, _ in results.values() if status == IntegrityStatus.INTACT)
            modified_count = sum(1 for status, _ in results.values() if status == IntegrityStatus.MODIFIED)
            missing_count = sum(1 for status, _ in results.values() if status == IntegrityStatus.MISSING)
            corrupted_count = sum(1 for status, _ in results.values() if status == IntegrityStatus.CORRUPTED)

            self.logger.info(f"نتائج الفحص - سليم: {intact_count}, معدل: {modified_count}, "
                           f"مفقود: {missing_count}, تالف: {corrupted_count}")

            return results

        except Exception as e:
            self.logger.error(f"خطأ في فحص الملفات المحمية: {str(e)}")
            return {}

    def protect_directory(self, directory_path: str, protection_level: ProtectionLevel = None,
                         recursive: bool = True, file_patterns: List[str] = None) -> Dict[str, bool]:
        """حماية مجلد كامل"""
        try:
            directory_path = Path(directory_path)

            if not directory_path.exists() or not directory_path.is_dir():
                self.logger.error(f"المجلد غير موجود: {directory_path}")
                return {}

            if protection_level is None:
                protection_level = ProtectionLevel(self.config["default_protection_level"])

            if file_patterns is None:
                file_patterns = ["*"]

            results = {}

            # البحث عن الملفات
            for pattern in file_patterns:
                if recursive:
                    files = directory_path.rglob(pattern)
                else:
                    files = directory_path.glob(pattern)

                for file_path in files:
                    if file_path.is_file():
                        success = self.protect_file(str(file_path), protection_level)
                        results[str(file_path)] = success

            self.logger.info(f"تم حماية {len(results)} ملف في المجلد: {directory_path}")
            return results

        except Exception as e:
            self.logger.error(f"خطأ في حماية المجلد {directory_path}: {str(e)}")
            return {}

    def unprotect_file(self, file_path: str) -> bool:
        """إلغاء حماية ملف"""
        try:
            file_path = str(Path(file_path).resolve())

            if file_path in self.integrity_database:
                del self.integrity_database[file_path]
                self.protected_files.discard(file_path)
                self._save_integrity_database()

                self.logger.info(f"تم إلغاء حماية الملف: {file_path}")
                return True
            else:
                self.logger.warning(f"الملف غير محمي: {file_path}")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في إلغاء حماية الملف {file_path}: {str(e)}")
            return False

    def update_file_protection(self, file_path: str) -> bool:
        """تحديث حماية ملف بعد تعديله بشكل مصرح به"""
        try:
            file_path = str(Path(file_path).resolve())

            if file_path not in self.integrity_database:
                self.logger.error(f"الملف غير محمي: {file_path}")
                return False

            original_protection_level = self.integrity_database[file_path].protection_level

            # إعادة حساب معلومات السلامة
            new_integrity = self._calculate_file_integrity(file_path, original_protection_level)

            # تحديث قاعدة البيانات
            self.integrity_database[file_path] = new_integrity
            self._save_integrity_database()

            self.logger.info(f"تم تحديث حماية الملف: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في تحديث حماية الملف {file_path}: {str(e)}")
            return False

    def get_protection_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الحماية"""
        try:
            stats = {
                "total_protected_files": len(self.protected_files),
                "total_alerts": len(self.alerts_database),
                "protection_levels": {},
                "file_statuses": {},
                "recent_alerts": 0,
                "alert_types": {},
                "alert_severities": {}
            }

            # إحصائيات مستويات الحماية
            for integrity in self.integrity_database.values():
                level = integrity.protection_level.value
                stats["protection_levels"][level] = stats["protection_levels"].get(level, 0) + 1

                status = integrity.status.value
                stats["file_statuses"][status] = stats["file_statuses"].get(status, 0) + 1

            # إحصائيات التنبيهات
            recent_cutoff = datetime.now() - timedelta(hours=24)

            for alert in self.alerts_database:
                # التنبيهات الحديثة (آخر 24 ساعة)
                if alert.detected_at > recent_cutoff:
                    stats["recent_alerts"] += 1

                # أنواع التنبيهات
                alert_type = alert.alert_type
                stats["alert_types"][alert_type] = stats["alert_types"].get(alert_type, 0) + 1

                # شدة التنبيهات
                severity = alert.severity
                stats["alert_severities"][severity] = stats["alert_severities"].get(severity, 0) + 1

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الحماية: {str(e)}")
            return {}

    def get_alerts(self, severity: str = None, alert_type: str = None,
                  resolved: bool = None, limit: int = None) -> List[TamperAlert]:
        """الحصول على التنبيهات مع إمكانية التصفية"""
        try:
            alerts = self.alerts_database.copy()

            # تصفية حسب الشدة
            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]

            # تصفية حسب النوع
            if alert_type:
                alerts = [alert for alert in alerts if alert.alert_type == alert_type]

            # تصفية حسب حالة الحل
            if resolved is not None:
                alerts = [alert for alert in alerts if alert.resolved == resolved]

            # ترتيب حسب التاريخ (الأحدث أولاً)
            alerts.sort(key=lambda x: x.detected_at, reverse=True)

            # تحديد العدد
            if limit:
                alerts = alerts[:limit]

            return alerts

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التنبيهات: {str(e)}")
            return []

    def resolve_alert(self, alert_id: str, action_taken: str = None) -> bool:
        """حل تنبيه"""
        try:
            for alert in self.alerts_database:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    if action_taken:
                        alert.action_taken = action_taken

                    self._save_alerts()
                    self.logger.info(f"تم حل التنبيه: {alert_id}")
                    return True

            self.logger.warning(f"التنبيه غير موجود: {alert_id}")
            return False

        except Exception as e:
            self.logger.error(f"خطأ في حل التنبيه {alert_id}: {str(e)}")
            return False

    def export_protection_report(self, output_file: str = None) -> str:
        """تصدير تقرير الحماية"""
        try:
            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = self.security_dir / f"protection_report_{timestamp}.json"

            # جمع البيانات
            report_data = {
                "generated_at": datetime.now().isoformat(),
                "statistics": self.get_protection_statistics(),
                "protected_files": {},
                "recent_alerts": []
            }

            # معلومات الملفات المحمية
            for file_path, integrity in self.integrity_database.items():
                integrity_dict = asdict(integrity)
                integrity_dict["created_at"] = integrity.created_at.isoformat()
                integrity_dict["last_verified"] = integrity.last_verified.isoformat()
                integrity_dict["protection_level"] = integrity.protection_level.value
                integrity_dict["status"] = integrity.status.value
                report_data["protected_files"][file_path] = integrity_dict

            # التنبيهات الحديثة (آخر 7 أيام)
            recent_cutoff = datetime.now() - timedelta(days=7)
            recent_alerts = [alert for alert in self.alerts_database if alert.detected_at > recent_cutoff]

            for alert in recent_alerts:
                alert_dict = asdict(alert)
                alert_dict["detected_at"] = alert.detected_at.isoformat()

                if alert.original_integrity:
                    orig_integrity = asdict(alert.original_integrity)
                    orig_integrity["created_at"] = alert.original_integrity.created_at.isoformat()
                    orig_integrity["last_verified"] = alert.original_integrity.last_verified.isoformat()
                    orig_integrity["protection_level"] = alert.original_integrity.protection_level.value
                    orig_integrity["status"] = alert.original_integrity.status.value
                    alert_dict["original_integrity"] = orig_integrity

                report_data["recent_alerts"].append(alert_dict)

            # حفظ التقرير
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"تم تصدير تقرير الحماية: {output_file}")
            return str(output_file)

        except Exception as e:
            self.logger.error(f"خطأ في تصدير تقرير الحماية: {str(e)}")
            return ""

    def backup_protection_database(self, backup_path: str = None) -> bool:
        """نسخ احتياطي لقاعدة بيانات الحماية"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = self.security_dir / f"protection_backup_{timestamp}"

            backup_path = Path(backup_path)
            backup_path.mkdir(parents=True, exist_ok=True)

            # نسخ قاعدة بيانات السلامة
            import shutil
            shutil.copy2(self.integrity_db_file, backup_path / "integrity_database.json")

            # نسخ التنبيهات
            shutil.copy2(self.alerts_file, backup_path / "tamper_alerts.json")

            # نسخ الإعدادات
            shutil.copy2(self.config_file, backup_path / "tamper_config.json")

            # نسخ مفاتيح التوقيع
            shutil.copy2(self.private_key_file, backup_path / "private_key.pem")
            shutil.copy2(self.public_key_file, backup_path / "public_key.pem")

            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return False

    def restore_protection_database(self, backup_path: str) -> bool:
        """استعادة قاعدة بيانات الحماية من النسخة الاحتياطية"""
        try:
            backup_path = Path(backup_path)

            if not backup_path.exists():
                self.logger.error(f"مسار النسخة الاحتياطية غير موجود: {backup_path}")
                return False

            import shutil

            # استعادة قاعدة بيانات السلامة
            backup_integrity_file = backup_path / "integrity_database.json"
            if backup_integrity_file.exists():
                shutil.copy2(backup_integrity_file, self.integrity_db_file)

            # استعادة التنبيهات
            backup_alerts_file = backup_path / "tamper_alerts.json"
            if backup_alerts_file.exists():
                shutil.copy2(backup_alerts_file, self.alerts_file)

            # استعادة الإعدادات
            backup_config_file = backup_path / "tamper_config.json"
            if backup_config_file.exists():
                shutil.copy2(backup_config_file, self.config_file)

            # استعادة مفاتيح التوقيع
            backup_private_key = backup_path / "private_key.pem"
            backup_public_key = backup_path / "public_key.pem"

            if backup_private_key.exists() and backup_public_key.exists():
                shutil.copy2(backup_private_key, self.private_key_file)
                shutil.copy2(backup_public_key, self.public_key_file)

            # إعادة تحميل البيانات
            self._load_config()
            self._load_integrity_database()
            self._load_alerts()
            self._setup_digital_signature_keys()

            self.logger.info(f"تم استعادة النسخة الاحتياطية: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            return False

    def start_real_time_monitoring(self, check_interval: int = 300):
        """بدء المراقبة في الوقت الفعلي"""
        try:
            import threading
            import time

            def monitoring_loop():
                while self.config.get("real_time_monitoring", True):
                    try:
                        # فحص الملفات المحمية
                        scan_results = self.scan_protected_files()

                        # إحصائيات سريعة
                        modified_files = [
                            file_path for file_path, (status, _) in scan_results.items()
                            if status == IntegrityStatus.MODIFIED
                        ]

                        if modified_files:
                            self.logger.warning(f"تم اكتشاف {len(modified_files)} ملف معدل أثناء المراقبة")

                        # انتظار الفترة المحددة
                        time.sleep(check_interval)

                    except Exception as e:
                        self.logger.error(f"خطأ في حلقة المراقبة: {str(e)}")
                        time.sleep(60)  # انتظار دقيقة في حالة الخطأ

            # بدء خيط المراقبة
            monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
            monitoring_thread.start()

            self.logger.info(f"تم بدء المراقبة في الوقت الفعلي (فحص كل {check_interval} ثانية)")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في بدء المراقبة في الوقت الفعلي: {str(e)}")
            return False

    def stop_real_time_monitoring(self):
        """إيقاف المراقبة في الوقت الفعلي"""
        try:
            self.config["real_time_monitoring"] = False
            self._save_config()
            self.logger.info("تم إيقاف المراقبة في الوقت الفعلي")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المراقبة في الوقت الفعلي: {str(e)}")
            return False

    def quarantine_file(self, file_path: str) -> bool:
        """عزل ملف مشبوه"""
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                self.logger.error(f"الملف غير موجود: {file_path}")
                return False

            # إنشاء مجلد الحجر الصحي
            quarantine_dir = self.security_dir / "quarantine"
            quarantine_dir.mkdir(exist_ok=True)

            # نقل الملف إلى الحجر الصحي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            quarantine_file = quarantine_dir / f"{timestamp}_{file_path.name}"

            import shutil
            shutil.move(str(file_path), str(quarantine_file))

            # تسجيل معلومات الحجر الصحي
            quarantine_info = {
                "original_path": str(file_path),
                "quarantine_path": str(quarantine_file),
                "quarantined_at": datetime.now().isoformat(),
                "reason": "suspicious_activity"
            }

            quarantine_log_file = quarantine_dir / "quarantine_log.json"

            if quarantine_log_file.exists():
                with open(quarantine_log_file, 'r', encoding='utf-8') as f:
                    quarantine_log = json.load(f)
            else:
                quarantine_log = []

            quarantine_log.append(quarantine_info)

            with open(quarantine_log_file, 'w', encoding='utf-8') as f:
                json.dump(quarantine_log, f, ensure_ascii=False, indent=2)

            self.logger.warning(f"تم عزل الملف المشبوه: {file_path} -> {quarantine_file}")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في عزل الملف {file_path}: {str(e)}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            status = {
                "system_initialized": True,
                "real_time_monitoring": self.config.get("real_time_monitoring", False),
                "protection_database_size": len(self.integrity_database),
                "alerts_count": len(self.alerts_database),
                "unresolved_alerts": len([a for a in self.alerts_database if not a.resolved]),
                "last_scan": None,
                "system_health": "healthy"
            }

            # البحث عن آخر فحص
            if self.integrity_database:
                last_verified_times = [
                    integrity.last_verified for integrity in self.integrity_database.values()
                    if integrity.last_verified
                ]
                if last_verified_times:
                    status["last_scan"] = max(last_verified_times).isoformat()

            # تقييم صحة النظام
            unresolved_high_alerts = len([
                a for a in self.alerts_database
                if not a.resolved and a.severity == "high"
            ])

            if unresolved_high_alerts > 0:
                status["system_health"] = "critical"
            elif status["unresolved_alerts"] > 10:
                status["system_health"] = "warning"

            return status

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على حالة النظام: {str(e)}")
            return {"system_health": "error", "error": str(e)}

    def cleanup_system(self):
        """تنظيف النظام"""
        try:
            # تنظيف التنبيهات القديمة
            self._cleanup_old_alerts()

            # تنظيف الملفات المحمية غير الموجودة
            missing_files = []
            for file_path in list(self.protected_files):
                if not Path(file_path).exists():
                    missing_files.append(file_path)

            for file_path in missing_files:
                self.unprotect_file(file_path)

            # حفظ التغييرات
            self._save_integrity_database()
            self._save_alerts()

            self.logger.info(f"تم تنظيف النظام - إزالة {len(missing_files)} ملف مفقود")

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النظام: {str(e)}")

    def __del__(self):
        """تنظيف الموارد عند إنهاء الكائن"""
        try:
            # إيقاف المراقبة في الوقت الفعلي
            self.stop_real_time_monitoring()

            # حفظ البيانات النهائية
            self._save_integrity_database()
            self._save_alerts()
            self._save_config()

        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"خطأ في تنظيف الموارد: {str(e)}")


# دوال مساعدة للاستخدام المباشر
def create_tamper_protection_system(base_dir: str = "data") -> TamperProtectionSystem:
    """إنشاء نظام الحماية من التلاعب"""
    return TamperProtectionSystem(base_dir)


def protect_file_quick(file_path: str, protection_level: str = "standard") -> Tuple[bool, str]:
    """حماية سريعة لملف واحد"""
    try:
        protection_system = TamperProtectionSystem()
        level = ProtectionLevel(protection_level)

        success = protection_system.protect_file(file_path, level)
        if success:
            return True, f"تم حماية الملف بنجاح: {file_path}"
        else:
            return False, f"فشل في حماية الملف: {file_path}"

    except Exception as e:
        return False, f"خطأ في الحماية السريعة: {str(e)}"


def verify_file_quick(file_path: str) -> Tuple[bool, str, Dict[str, Any]]:
    """تحقق سريع من سلامة ملف"""
    try:
        protection_system = TamperProtectionSystem()
        status, details = protection_system.verify_file_integrity(file_path)

        if status == IntegrityStatus.INTACT:
            return True, "الملف سليم", details
        elif status == IntegrityStatus.MODIFIED:
            return False, "تم تعديل الملف", details
        elif status == IntegrityStatus.CORRUPTED:
            return False, "الملف تالف", details
        elif status == IntegrityStatus.MISSING:
            return False, "الملف مفقود", details
        else:
            return False, "حالة غير معروفة", details

    except Exception as e:
        return False, f"خطأ في التحقق السريع: {str(e)}", {}


def protect_directory_quick(directory_path: str, protection_level: str = "standard",
                           recursive: bool = True) -> Tuple[bool, str, Dict[str, bool]]:
    """حماية سريعة لمجلد كامل"""
    try:
        protection_system = TamperProtectionSystem()
        level = ProtectionLevel(protection_level)

        results = protection_system.protect_directory(directory_path, level, recursive)

        total_files = len(results)
        protected_files = sum(1 for success in results.values() if success)

        if protected_files == total_files:
            return True, f"تم حماية جميع الملفات ({protected_files}/{total_files})", results
        elif protected_files > 0:
            return True, f"تم حماية {protected_files} من {total_files} ملف", results
        else:
            return False, "فشل في حماية أي ملف", results

    except Exception as e:
        return False, f"خطأ في الحماية السريعة للمجلد: {str(e)}", {}


def scan_protected_files_quick() -> Tuple[bool, str, Dict[str, Any]]:
    """فحص سريع لجميع الملفات المحمية"""
    try:
        protection_system = TamperProtectionSystem()
        results = protection_system.scan_protected_files()

        total_files = len(results)
        intact_files = sum(1 for status, _ in results.values() if status == IntegrityStatus.INTACT)
        modified_files = sum(1 for status, _ in results.values() if status == IntegrityStatus.MODIFIED)

        summary = {
            "total_files": total_files,
            "intact_files": intact_files,
            "modified_files": modified_files,
            "corrupted_files": total_files - intact_files - modified_files,
            "scan_results": results
        }

        if modified_files == 0:
            return True, f"جميع الملفات سليمة ({intact_files}/{total_files})", summary
        else:
            return False, f"تم العثور على {modified_files} ملف معدل من {total_files}", summary

    except Exception as e:
        return False, f"خطأ في الفحص السريع: {str(e)}", {}


def get_protection_report_quick() -> Tuple[bool, str, Dict[str, Any]]:
    """تقرير سريع عن حالة الحماية"""
    try:
        protection_system = TamperProtectionSystem()

        # الحصول على الإحصائيات
        stats = protection_system.get_protection_statistics()

        # الحصول على حالة النظام
        system_status = protection_system.get_system_status()

        # الحصول على التنبيهات غير المحلولة
        unresolved_alerts = protection_system.get_alerts(resolved=False, limit=10)

        report = {
            "statistics": stats,
            "system_status": system_status,
            "recent_alerts": [
                {
                    "alert_id": alert.alert_id,
                    "file_path": alert.file_path,
                    "alert_type": alert.alert_type,
                    "severity": alert.severity,
                    "description": alert.description,
                    "detected_at": alert.detected_at.isoformat()
                }
                for alert in unresolved_alerts
            ]
        }

        health = system_status.get("system_health", "unknown")
        if health == "healthy":
            return True, "النظام يعمل بشكل طبيعي", report
        elif health == "warning":
            return True, "النظام يعمل مع تحذيرات", report
        elif health == "critical":
            return False, "النظام في حالة حرجة", report
        else:
            return False, "حالة النظام غير معروفة", report

    except Exception as e:
        return False, f"خطأ في إنشاء التقرير السريع: {str(e)}", {}


if __name__ == "__main__":
    # مثال على الاستخدام
    protection_system = TamperProtectionSystem()

    # حماية ملف
    success = protection_system.protect_file("./important_file.txt", ProtectionLevel.ADVANCED)
    if success:
        print("تم حماية الملف بنجاح")
    else:
        print("فشل في حماية الملف")

    # التحقق من سلامة الملف
    status, details = protection_system.verify_file_integrity("./important_file.txt")
    print(f"حالة الملف: {status.value}")

    # فحص جميع الملفات المحمية
    scan_results = protection_system.scan_protected_files()
    print(f"تم فحص {len(scan_results)} ملف")

    # الحصول على الإحصائيات
    stats = protection_system.get_protection_statistics()
    print(f"إحصائيات الحماية: {stats}")

    # بدء المراقبة في الوقت الفعلي
    protection_system.start_real_time_monitoring()
    print("تم بدء المراقبة في الوقت الفعلي")

    # تنظيف النظام
    protection_system.cleanup_system()
