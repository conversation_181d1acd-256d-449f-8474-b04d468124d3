#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مجدول النشر - Publishing Scheduler
يقوم بجدولة ونشر المحتوى في الأوقات المثلى
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
import uuid

class PostStatus(Enum):
    """حالة المنشور"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHING = "publishing"
    PUBLISHED = "published"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ScheduledPost:
    """منشور مجدول"""
    id: str
    video_path: str
    platform: str
    title: str
    description: str
    hashtags: List[str]
    scheduled_time: datetime
    status: PostStatus
    created_at: datetime
    published_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    thumbnail_path: Optional[str] = None
    optimization_result: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        data = asdict(self)
        data['scheduled_time'] = self.scheduled_time.isoformat()
        data['created_at'] = self.created_at.isoformat()
        data['published_at'] = self.published_at.isoformat() if self.published_at else None
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScheduledPost':
        """إنشاء من قاموس"""
        data['scheduled_time'] = datetime.fromisoformat(data['scheduled_time'])
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['published_at'] = datetime.fromisoformat(data['published_at']) if data['published_at'] else None
        data['status'] = PostStatus(data['status'])
        return cls(**data)

class PublishingScheduler:
    """مجدول النشر"""
    
    def __init__(self, config_manager, publishers: Dict[str, Any]):
        self.config_manager = config_manager
        self.publishers = publishers  # قاموس من الناشرين لكل منصة
        self.logger = logging.getLogger(__name__)
        
        # قائمة المنشورات المجدولة
        self.scheduled_posts: List[ScheduledPost] = []
        
        # إعدادات المجدول
        self.settings = self._load_scheduler_settings()
        
        # مسار ملف البيانات
        self.data_file = os.path.join(
            self.config_manager.get_app_data_dir(),
            "scheduled_posts.json"
        )
        
        # تحميل المنشورات المحفوظة
        self._load_scheduled_posts()
        
        # خيط المجدول
        self.scheduler_thread = None
        self.is_running = False
        
        # إحصائيات
        self.stats = {
            "total_scheduled": 0,
            "total_published": 0,
            "total_failed": 0,
            "success_rate": 0.0,
            "average_engagement": 0.0,
            "best_posting_times": {},
            "platform_performance": {}
        }
        
        # تحميل الإحصائيات
        self._load_stats()
    
    def _load_scheduler_settings(self) -> Dict[str, Any]:
        """تحميل إعدادات المجدول"""
        try:
            return self.config_manager.get_setting("publishing_settings", "scheduler", {
                "check_interval": 60,  # ثواني
                "max_concurrent_posts": 3,
                "retry_delay": 300,  # 5 دقائق
                "auto_reschedule_failed": True,
                "optimal_posting_times": {
                    "tiktok": {
                        "weekdays": [18, 19, 20, 21],  # 6-9 مساءً
                        "weekends": [17, 18, 19, 20]   # 5-8 مساءً
                    },
                    "instagram": {
                        "weekdays": [17, 18, 19],
                        "weekends": [16, 17, 18, 19]
                    }
                },
                "timezone": "Asia/Riyadh",
                "enable_analytics": True,
                "auto_optimize_timing": True
            })
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات المجدول: {str(e)}")
            return {"check_interval": 60, "max_concurrent_posts": 3}
    
    def schedule_post(self, video_path: str, platform: str, title: str, description: str,
                     hashtags: List[str], scheduled_time: Optional[datetime] = None,
                     thumbnail_path: Optional[str] = None,
                     optimization_result: Optional[Dict[str, Any]] = None) -> str:
        """جدولة منشور جديد"""
        try:
            # إنشاء معرف فريد
            post_id = str(uuid.uuid4())
            
            # تحديد وقت النشر
            if not scheduled_time:
                scheduled_time = self._calculate_optimal_time(platform)
            
            # إنشاء المنشور المجدول
            post = ScheduledPost(
                id=post_id,
                video_path=video_path,
                platform=platform,
                title=title,
                description=description,
                hashtags=hashtags,
                scheduled_time=scheduled_time,
                status=PostStatus.SCHEDULED,
                created_at=datetime.now(),
                thumbnail_path=thumbnail_path,
                optimization_result=optimization_result
            )
            
            # إضافة إلى القائمة
            self.scheduled_posts.append(post)
            
            # حفظ البيانات
            self._save_scheduled_posts()
            
            # تحديث الإحصائيات
            self.stats["total_scheduled"] += 1
            self._save_stats()
            
            self.logger.info(f"تم جدولة منشور جديد: {post_id} للمنصة {platform} في {scheduled_time}")
            
            # بدء المجدول إذا لم يكن يعمل
            if not self.is_running:
                self.start_scheduler()
            
            return post_id
            
        except Exception as e:
            self.logger.error(f"خطأ في جدولة المنشور: {str(e)}")
            raise
    
    def _calculate_optimal_time(self, platform: str) -> datetime:
        """حساب الوقت الأمثل للنشر"""
        try:
            optimal_times = self.settings.get("optimal_posting_times", {})
            platform_times = optimal_times.get(platform, {})
            
            now = datetime.now()
            
            # تحديد إذا كان يوم عمل أم عطلة
            is_weekend = now.weekday() >= 5  # السبت والأحد
            time_key = "weekends" if is_weekend else "weekdays"
            
            hours = platform_times.get(time_key, [18, 19, 20])
            
            # العثور على أقرب وقت مثالي
            best_time = None
            min_wait = float('inf')
            
            for hour in hours:
                # جرب اليوم الحالي
                target_time = now.replace(hour=hour, minute=0, second=0, microsecond=0)
                
                # إذا كان الوقت قد مضى، جرب اليوم التالي
                if target_time <= now:
                    target_time += timedelta(days=1)
                
                wait_time = (target_time - now).total_seconds()
                if wait_time < min_wait:
                    min_wait = wait_time
                    best_time = target_time
            
            # إذا لم نجد وقتاً مناسباً، استخدم وقتاً افتراضياً
            if not best_time:
                best_time = now + timedelta(hours=2)
            
            return best_time
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الوقت الأمثل: {str(e)}")
            return datetime.now() + timedelta(hours=2)
    
    def start_scheduler(self):
        """بدء المجدول"""
        try:
            if self.is_running:
                self.logger.warning("المجدول يعمل بالفعل")
                return
            
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            self.logger.info("تم بدء مجدول النشر")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء المجدول: {str(e)}")
            self.is_running = False
    
    def stop_scheduler(self):
        """إيقاف المجدول"""
        try:
            self.is_running = False
            
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            self.logger.info("تم إيقاف مجدول النشر")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف المجدول: {str(e)}")
    
    def _scheduler_loop(self):
        """حلقة المجدول الرئيسية"""
        try:
            check_interval = self.settings.get("check_interval", 60)
            
            while self.is_running:
                try:
                    # فحص المنشورات المجدولة
                    self._check_scheduled_posts()
                    
                    # انتظار الفترة المحددة
                    time.sleep(check_interval)
                    
                except Exception as e:
                    self.logger.error(f"خطأ في حلقة المجدول: {str(e)}")
                    time.sleep(check_interval)
            
        except Exception as e:
            self.logger.error(f"خطأ في حلقة المجدول الرئيسية: {str(e)}")
        finally:
            self.is_running = False
    
    def _check_scheduled_posts(self):
        """فحص المنشورات المجدولة"""
        try:
            now = datetime.now()
            posts_to_publish = []
            
            # العثور على المنشورات الجاهزة للنشر
            for post in self.scheduled_posts:
                if (post.status == PostStatus.SCHEDULED and 
                    post.scheduled_time <= now):
                    posts_to_publish.append(post)
            
            # نشر المنشورات (مع تحديد عدد أقصى متزامن)
            max_concurrent = self.settings.get("max_concurrent_posts", 3)
            posts_to_publish = posts_to_publish[:max_concurrent]
            
            for post in posts_to_publish:
                self._publish_post(post)
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص المنشورات المجدولة: {str(e)}")
    
    def _publish_post(self, post: ScheduledPost):
        """نشر منشور"""
        try:
            # تحديث حالة المنشور
            post.status = PostStatus.PUBLISHING
            self._save_scheduled_posts()
            
            self.logger.info(f"بدء نشر المنشور {post.id} على {post.platform}")
            
            # الحصول على الناشر المناسب
            publisher = self.publishers.get(post.platform)
            if not publisher:
                raise Exception(f"لا يوجد ناشر للمنصة: {post.platform}")
            
            # نشر المنشور
            result = publisher.publish_video(
                video_path=post.video_path,
                title=post.title,
                description=post.description,
                hashtags=post.hashtags,
                thumbnail_path=post.thumbnail_path
            )
            
            if result.get("success", False):
                # نجح النشر
                post.status = PostStatus.PUBLISHED
                post.published_at = datetime.now()
                post.error_message = None
                
                # تحديث الإحصائيات
                self.stats["total_published"] += 1
                self._update_success_rate()
                
                self.logger.info(f"تم نشر المنشور {post.id} بنجاح")
                
            else:
                # فشل النشر
                self._handle_publish_failure(post, result.get("error", "خطأ غير معروف"))
            
        except Exception as e:
            self._handle_publish_failure(post, str(e))
        
        finally:
            self._save_scheduled_posts()
            self._save_stats()
    
    def _handle_publish_failure(self, post: ScheduledPost, error_message: str):
        """التعامل مع فشل النشر"""
        try:
            post.error_message = error_message
            post.retry_count += 1
            
            self.logger.error(f"فشل نشر المنشور {post.id}: {error_message}")
            
            # إعادة المحاولة إذا لم نصل للحد الأقصى
            if post.retry_count < post.max_retries:
                if self.settings.get("auto_reschedule_failed", True):
                    # إعادة جدولة المنشور
                    retry_delay = self.settings.get("retry_delay", 300)  # 5 دقائق
                    post.scheduled_time = datetime.now() + timedelta(seconds=retry_delay)
                    post.status = PostStatus.SCHEDULED
                    
                    self.logger.info(f"إعادة جدولة المنشور {post.id} للمحاولة {post.retry_count + 1}")
                else:
                    post.status = PostStatus.FAILED
            else:
                # تجاوز الحد الأقصى للمحاولات
                post.status = PostStatus.FAILED
                self.stats["total_failed"] += 1
                self._update_success_rate()
                
                self.logger.error(f"فشل نهائي في نشر المنشور {post.id} بعد {post.retry_count} محاولات")
            
        except Exception as e:
            self.logger.error(f"خطأ في التعامل مع فشل النشر: {str(e)}")
            post.status = PostStatus.FAILED

    def _update_success_rate(self):
        """تحديث معدل النجاح"""
        try:
            total_attempts = self.stats["total_published"] + self.stats["total_failed"]
            if total_attempts > 0:
                self.stats["success_rate"] = (self.stats["total_published"] / total_attempts) * 100
            else:
                self.stats["success_rate"] = 0.0
        except Exception as e:
            self.logger.error(f"خطأ في تحديث معدل النجاح: {str(e)}")

    def get_scheduled_posts(self, status: Optional[PostStatus] = None) -> List[ScheduledPost]:
        """الحصول على المنشورات المجدولة"""
        try:
            if status:
                return [post for post in self.scheduled_posts if post.status == status]
            return self.scheduled_posts.copy()
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المنشورات: {str(e)}")
            return []

    def cancel_post(self, post_id: str) -> bool:
        """إلغاء منشور مجدول"""
        try:
            for post in self.scheduled_posts:
                if post.id == post_id:
                    if post.status in [PostStatus.SCHEDULED, PostStatus.DRAFT]:
                        post.status = PostStatus.CANCELLED
                        self._save_scheduled_posts()
                        self.logger.info(f"تم إلغاء المنشور {post_id}")
                        return True
                    else:
                        self.logger.warning(f"لا يمكن إلغاء المنشور {post_id} - الحالة: {post.status}")
                        return False

            self.logger.warning(f"لم يتم العثور على المنشور {post_id}")
            return False

        except Exception as e:
            self.logger.error(f"خطأ في إلغاء المنشور: {str(e)}")
            return False

    def reschedule_post(self, post_id: str, new_time: datetime) -> bool:
        """إعادة جدولة منشور"""
        try:
            for post in self.scheduled_posts:
                if post.id == post_id:
                    if post.status in [PostStatus.SCHEDULED, PostStatus.FAILED]:
                        post.scheduled_time = new_time
                        post.status = PostStatus.SCHEDULED
                        post.retry_count = 0  # إعادة تعيين عداد المحاولات
                        post.error_message = None

                        self._save_scheduled_posts()
                        self.logger.info(f"تم إعادة جدولة المنشور {post_id} إلى {new_time}")
                        return True
                    else:
                        self.logger.warning(f"لا يمكن إعادة جدولة المنشور {post_id} - الحالة: {post.status}")
                        return False

            self.logger.warning(f"لم يتم العثور على المنشور {post_id}")
            return False

        except Exception as e:
            self.logger.error(f"خطأ في إعادة جدولة المنشور: {str(e)}")
            return False

    def delete_post(self, post_id: str) -> bool:
        """حذف منشور"""
        try:
            for i, post in enumerate(self.scheduled_posts):
                if post.id == post_id:
                    if post.status != PostStatus.PUBLISHING:
                        del self.scheduled_posts[i]
                        self._save_scheduled_posts()
                        self.logger.info(f"تم حذف المنشور {post_id}")
                        return True
                    else:
                        self.logger.warning(f"لا يمكن حذف المنشور {post_id} - جاري النشر")
                        return False

            self.logger.warning(f"لم يتم العثور على المنشور {post_id}")
            return False

        except Exception as e:
            self.logger.error(f"خطأ في حذف المنشور: {str(e)}")
            return False

    def get_post_by_id(self, post_id: str) -> Optional[ScheduledPost]:
        """الحصول على منشور بالمعرف"""
        try:
            for post in self.scheduled_posts:
                if post.id == post_id:
                    return post
            return None
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المنشور: {str(e)}")
            return None

    def get_upcoming_posts(self, hours: int = 24) -> List[ScheduledPost]:
        """الحصول على المنشورات القادمة"""
        try:
            now = datetime.now()
            future_time = now + timedelta(hours=hours)

            upcoming = []
            for post in self.scheduled_posts:
                if (post.status == PostStatus.SCHEDULED and
                    now <= post.scheduled_time <= future_time):
                    upcoming.append(post)

            # ترتيب حسب وقت النشر
            upcoming.sort(key=lambda p: p.scheduled_time)
            return upcoming

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المنشورات القادمة: {str(e)}")
            return []

    def get_failed_posts(self) -> List[ScheduledPost]:
        """الحصول على المنشورات الفاشلة"""
        return self.get_scheduled_posts(PostStatus.FAILED)

    def retry_failed_posts(self) -> int:
        """إعادة محاولة المنشورات الفاشلة"""
        try:
            failed_posts = self.get_failed_posts()
            retried_count = 0

            for post in failed_posts:
                if post.retry_count < post.max_retries:
                    # إعادة جدولة للمحاولة مرة أخرى
                    new_time = self._calculate_optimal_time(post.platform)
                    if self.reschedule_post(post.id, new_time):
                        retried_count += 1

            self.logger.info(f"تم إعادة جدولة {retried_count} منشور فاشل")
            return retried_count

        except Exception as e:
            self.logger.error(f"خطأ في إعادة محاولة المنشورات الفاشلة: {str(e)}")
            return 0

    def get_scheduler_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المجدول"""
        try:
            # إحصائيات أساسية
            stats = self.stats.copy()

            # إحصائيات إضافية
            stats.update({
                "total_posts": len(self.scheduled_posts),
                "scheduled_count": len(self.get_scheduled_posts(PostStatus.SCHEDULED)),
                "published_count": len(self.get_scheduled_posts(PostStatus.PUBLISHED)),
                "failed_count": len(self.get_scheduled_posts(PostStatus.FAILED)),
                "cancelled_count": len(self.get_scheduled_posts(PostStatus.CANCELLED)),
                "is_running": self.is_running,
                "upcoming_24h": len(self.get_upcoming_posts(24))
            })

            return stats

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {}

    def optimize_posting_times(self, platform: str, engagement_data: List[Dict[str, Any]]):
        """تحسين أوقات النشر بناءً على بيانات التفاعل"""
        try:
            if not engagement_data:
                return

            # تحليل أفضل أوقات النشر
            hour_performance = {}

            for data in engagement_data:
                post_time = datetime.fromisoformat(data.get("published_at", ""))
                hour = post_time.hour
                engagement = data.get("engagement_rate", 0)

                if hour not in hour_performance:
                    hour_performance[hour] = []
                hour_performance[hour].append(engagement)

            # حساب متوسط التفاعل لكل ساعة
            avg_performance = {}
            for hour, engagements in hour_performance.items():
                avg_performance[hour] = sum(engagements) / len(engagements)

            # العثور على أفضل الأوقات
            sorted_hours = sorted(avg_performance.items(), key=lambda x: x[1], reverse=True)
            best_hours = [hour for hour, _ in sorted_hours[:4]]  # أفضل 4 ساعات

            # تحديث الإعدادات
            optimal_times = self.settings.get("optimal_posting_times", {})
            if platform not in optimal_times:
                optimal_times[platform] = {}

            optimal_times[platform]["weekdays"] = best_hours
            optimal_times[platform]["weekends"] = best_hours

            # حفظ الإعدادات المحدثة
            self.config_manager.update_setting(
                "publishing_settings",
                "scheduler",
                self.settings
            )

            self.logger.info(f"تم تحسين أوقات النشر للمنصة {platform}: {best_hours}")

        except Exception as e:
            self.logger.error(f"خطأ في تحسين أوقات النشر: {str(e)}")

    def _load_scheduled_posts(self):
        """تحميل المنشورات المجدولة من الملف"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.scheduled_posts = [
                    ScheduledPost.from_dict(post_data)
                    for post_data in data.get("posts", [])
                ]

                self.logger.info(f"تم تحميل {len(self.scheduled_posts)} منشور مجدول")
            else:
                self.scheduled_posts = []

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المنشورات المجدولة: {str(e)}")
            self.scheduled_posts = []

    def _save_scheduled_posts(self):
        """حفظ المنشورات المجدولة إلى الملف"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)

            data = {
                "posts": [post.to_dict() for post in self.scheduled_posts],
                "last_updated": datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ المنشورات المجدولة: {str(e)}")

    def _load_stats(self):
        """تحميل الإحصائيات"""
        try:
            stats_file = os.path.join(
                self.config_manager.get_app_data_dir(),
                "scheduler_stats.json"
            )

            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    self.stats.update(saved_stats)

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def _save_stats(self):
        """حفظ الإحصائيات"""
        try:
            stats_file = os.path.join(
                self.config_manager.get_app_data_dir(),
                "scheduler_stats.json"
            )

            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(stats_file), exist_ok=True)

            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"خطأ في حفظ الإحصائيات: {str(e)}")

    def cleanup_old_posts(self, days: int = 30):
        """تنظيف المنشورات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            initial_count = len(self.scheduled_posts)

            # الاحتفاظ بالمنشورات الحديثة أو غير المكتملة
            self.scheduled_posts = [
                post for post in self.scheduled_posts
                if (post.created_at > cutoff_date or
                    post.status in [PostStatus.SCHEDULED, PostStatus.PUBLISHING])
            ]

            removed_count = initial_count - len(self.scheduled_posts)

            if removed_count > 0:
                self._save_scheduled_posts()
                self.logger.info(f"تم حذف {removed_count} منشور قديم")

            return removed_count

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المنشورات القديمة: {str(e)}")
            return 0
