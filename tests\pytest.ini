[tool:pytest]
# إعدادات pytest للتطبيق الذكي لإنشاء المحتوى

# مجلدات الاختبارات
testpaths = tests

# أنماط ملفات الاختبارات
python_files = test_*.py *_test.py

# أنماط فئات الاختبارات
python_classes = Test*

# أنماط دوال الاختبارات
python_functions = test_*

# العلامات المخصصة
markers =
    unit: اختبارات الوحدة الأساسية
    security: اختبارات الأمان والحماية
    gui: اختبارات واجهة المستخدم الرسومية
    performance: اختبارات الأداء والتحسين
    integration: اختبارات التكامل بين الأنظمة
    slow: اختبارات بطيئة التنفيذ
    network: اختبارات تتطلب اتصال بالشبكة
    database: اختبارات قواعد البيانات
    encryption: اختبارات التشفير
    authentication: اختبارات المصادقة

# خيارات إضافية
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    --color=yes
    --durations=10

# مجلدات يجب تجاهلها
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache

# الحد الأدنى لإصدار pytest
minversion = 7.0

# ترميز الملفات
console_output_style = progress

# تصفية التحذيرات
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*

# إعدادات التغطية
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:

show_missing = True
precision = 2

[coverage:html]
directory = htmlcov
