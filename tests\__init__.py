#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مجموعة اختبارات التطبيق الذكي لإنشاء المحتوى
Test Suite for Smart Content Creation Application
"""

import sys
import os
from pathlib import Path

# إضافة مجلد المشروع إلى مسار Python
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# إعداد متغيرات البيئة للاختبار
os.environ['TESTING'] = 'true'
os.environ['TEST_DATA_DIR'] = str(project_root / 'tests' / 'test_data')

# إنشاء مجلد بيانات الاختبار
test_data_dir = Path(os.environ['TEST_DATA_DIR'])
test_data_dir.mkdir(exist_ok=True)

print("تم تهيئة بيئة الاختبار بنجاح")
