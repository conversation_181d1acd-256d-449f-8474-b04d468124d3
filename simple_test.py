#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

print("Testing imports...")

try:
    from cryptography.fernet import Fernet
    print("✅ cryptography.fernet imported successfully")
except Exception as e:
    print(f"❌ Error importing cryptography.fernet: {e}")

try:
    from dataclasses import dataclass, asdict
    print("✅ dataclasses imported successfully")
except Exception as e:
    print(f"❌ Error importing dataclasses: {e}")

try:
    from enum import Enum
    print("✅ enum imported successfully")
except Exception as e:
    print(f"❌ Error importing enum: {e}")

try:
    from src.security.advanced_encryption import EncryptionLevel
    print("✅ EncryptionLevel imported successfully")
except Exception as e:
    print(f"❌ Error importing EncryptionLevel: {e}")
    import traceback
    traceback.print_exc()

try:
    from src.security.advanced_encryption import AdvancedEncryptionSystem
    print("✅ AdvancedEncryptionSystem imported successfully")
except Exception as e:
    print(f"❌ Error importing AdvancedEncryptionSystem: {e}")
    import traceback
    traceback.print_exc()

print("Import test completed.")
