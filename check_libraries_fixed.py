#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة فحص المكتبات المحدثة - منشئ المحتوى الذكي
Fixed Library Checker Tool - Smart Content Creator

تستخدم هذه الأداة Python الصحيح للتحقق من المكتبات
This tool uses the correct Python to check libraries
"""

import subprocess
import sys
import os

# مسار Python الصحيح
PYTHON_PATH = r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

# المكتبات الأساسية
ESSENTIAL_LIBRARIES = [
    'PyQt6',
    'pandas', 
    'numpy',
    'requests',
    'aiohttp',
    'bcrypt',
    'cryptography',
    'qtawesome'
]

# المكتبات المتقدمة
ADVANCED_LIBRARIES = [
    'matplotlib',
    'seaborn', 
    'plotly',
    'librosa',
    'soundfile',
    'numba',
    'scipy',
    'scikit-learn',
    'playwright',
    'instaloader',
    'tweepy',
    'sqlalchemy',
    'pymongo',
    'redis',
    'rich',
    'typer'
]

def check_library_with_correct_python(lib_name):
    """فحص مكتبة باستخدام Python الصحيح"""
    try:
        # تشغيل Python الصحيح لفحص المكتبة
        result = subprocess.run([
            PYTHON_PATH, 
            '-c', 
            f'import {lib_name}; print("OK")'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            return True, "✅ مثبت ويعمل"
        else:
            return False, f"❌ خطأ: {result.stderr.strip()}"
    except subprocess.TimeoutExpired:
        return False, "❌ انتهت مهلة الفحص"
    except Exception as e:
        return False, f"❌ خطأ في الفحص: {str(e)}"

def check_pip_installation(lib_name):
    """فحص تثبيت المكتبة عبر pip"""
    try:
        result = subprocess.run([
            'pip', 'show', lib_name
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            return True, "✅ مثبت في pip"
        else:
            return False, "❌ غير مثبت في pip"
    except Exception as e:
        return False, f"❌ خطأ في فحص pip: {str(e)}"

def generate_detailed_report():
    """إنشاء تقرير مفصل عن حالة المكتبات"""
    print("=" * 80)
    print("🔍 تقرير حالة تثبيت المكتبات - منشئ المحتوى الذكي")
    print("📊 Library Installation Status Report - Smart Content Creator")
    print("=" * 80)
    
    print(f"\n🐍 مسار Python المستخدم: {PYTHON_PATH}")
    print(f"📦 إصدار Python: ", end="")
    
    try:
        result = subprocess.run([PYTHON_PATH, '--version'], 
                              capture_output=True, text=True)
        print(result.stdout.strip())
    except:
        print("غير معروف")
    
    print("\n" + "=" * 50)
    print("📚 المكتبات الأساسية (Essential Libraries)")
    print("=" * 50)
    
    essential_status = {}
    for lib in ESSENTIAL_LIBRARIES:
        import_status, import_msg = check_library_with_correct_python(lib)
        pip_status, pip_msg = check_pip_installation(lib)
        
        essential_status[lib] = import_status
        
        print(f"\n📦 {lib}:")
        print(f"   استيراد: {import_msg}")
        print(f"   pip: {pip_msg}")
    
    print("\n" + "=" * 50)
    print("🚀 المكتبات المتقدمة (Advanced Libraries)")
    print("=" * 50)
    
    advanced_status = {}
    for lib in ADVANCED_LIBRARIES:
        import_status, import_msg = check_library_with_correct_python(lib)
        pip_status, pip_msg = check_pip_installation(lib)
        
        advanced_status[lib] = import_status
        
        print(f"\n📦 {lib}:")
        print(f"   استيراد: {import_msg}")
        print(f"   pip: {pip_msg}")
    
    # إحصائيات
    essential_working = sum(essential_status.values())
    advanced_working = sum(advanced_status.values())
    
    print("\n" + "=" * 50)
    print("📊 ملخص الإحصائيات (Statistics Summary)")
    print("=" * 50)
    
    print(f"✅ المكتبات الأساسية العاملة: {essential_working}/{len(ESSENTIAL_LIBRARIES)}")
    print(f"🚀 المكتبات المتقدمة العاملة: {advanced_working}/{len(ADVANCED_LIBRARIES)}")
    print(f"📈 إجمالي المكتبات العاملة: {essential_working + advanced_working}/{len(ESSENTIAL_LIBRARIES) + len(ADVANCED_LIBRARIES)}")
    
    # تقييم الحالة
    if essential_working == len(ESSENTIAL_LIBRARIES):
        print("\n🎉 ممتاز! جميع المكتبات الأساسية تعمل بشكل صحيح")
    else:
        print(f"\n⚠️  تحذير: {len(ESSENTIAL_LIBRARIES) - essential_working} مكتبة أساسية لا تعمل")
    
    if advanced_working == len(ADVANCED_LIBRARIES):
        print("🌟 رائع! جميع المكتبات المتقدمة تعمل بشكل صحيح")
    else:
        print(f"ℹ️  معلومة: {len(ADVANCED_LIBRARIES) - advanced_working} مكتبة متقدمة لا تعمل")
    
    return essential_status, advanced_status

def test_core_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n" + "=" * 50)
    print("🧪 اختبار الوظائف الأساسية (Core Functionality Test)")
    print("=" * 50)
    
    tests = [
        ("PyQt6 GUI", "from PyQt6.QtWidgets import QApplication, QMainWindow"),
        ("pandas DataFrame", "import pandas as pd; df = pd.DataFrame({'test': [1,2,3]})"),
        ("numpy Array", "import numpy as np; arr = np.array([1,2,3])"),
        ("requests HTTP", "import requests"),
        ("matplotlib Plot", "import matplotlib.pyplot as plt"),
        ("cryptography", "from cryptography.fernet import Fernet")
    ]
    
    for test_name, test_code in tests:
        try:
            result = subprocess.run([
                PYTHON_PATH, 
                '-c', 
                test_code
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"✅ {test_name}: يعمل بشكل صحيح")
            else:
                print(f"❌ {test_name}: فشل - {result.stderr.strip()}")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")

def main():
    """الدالة الرئيسية"""
    try:
        essential_status, advanced_status = generate_detailed_report()
        test_core_functionality()
        
        print("\n" + "=" * 80)
        print("✅ تم إنجاز فحص المكتبات بنجاح!")
        print("📝 يمكنك الآن المتابعة لبناء التطبيق النهائي")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n❌ تم إيقاف الفحص بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الفحص: {str(e)}")

if __name__ == "__main__":
    main()
